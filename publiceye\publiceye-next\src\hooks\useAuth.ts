import { useState, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { ref, set, get, serverTimestamp } from 'firebase/database';
import { auth, db } from '@/lib/firebase';
import { User } from '@/types/user';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Generate a random color code for user avatar
  const generateColorCode = (): string => {
    const colors = [
      '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
      '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
      '#8BC34A', '#CDDC39', '#FFC107', '#FF9800', '#FF5722'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);

      if (firebaseUser) {
        try {
          // Get user data from Realtime Database
          const userRef = ref(db, `users/${firebaseUser.uid}`);
          const snapshot = await get(userRef);

          if (snapshot.exists()) {
            const userData = snapshot.val();
            setUser({ id: firebaseUser.uid, ...userData } as User);
          } else {
            setUser(null);
          }
        } catch (err) {
          console.error('Error fetching user data:', err);
          setUser(null);
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Sign up
  const signUp = async (
    email: string,
    password: string,
    displayName: string,
    codeName?: string
  ): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Update profile with display name
      await updateProfile(firebaseUser, { displayName });

      // Generate a code name if not provided
      const userCodeName = codeName || displayName.split(' ')[0] || email.split('@')[0];

      // Create user document in Realtime Database
      const userData: Omit<User, 'id'> = {
        email,
        displayName,
        codeName: userCodeName,
        colorCode: generateColorCode(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        role: 'user',
        followedCases: {},  // Empty object for Realtime Database
        createdCases: {},   // Empty object for Realtime Database
        contributedCases: {} // Empty object for Realtime Database
      };

      const userRef = ref(db, `users/${firebaseUser.uid}`);
      await set(userRef, userData);

      // Set user state
      setUser({ id: firebaseUser.uid, ...userData } as User);

      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to sign up');
      console.error(err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Sign in
  const signIn = async (email: string, password: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // 1. Authenticate user
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // 2. Try to get user data
      const userRef = ref(db, `users/${firebaseUser.uid}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        // Use existing data
        const userData = snapshot.val();
        setUser({ id: firebaseUser.uid, ...userData } as User);
      } else {
        // This part is problematic - it creates new data even for signed-up users
        const basicUserData: Omit<User, 'id'> = {
          email: user.email || '',
          displayName: user.displayName || '',
          codeName: user.displayName || '',
          colorCode: generateColorCode(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };
        await set(userRef, basicUserData);
        setUser({ id: firebaseUser.uid, ...basicUserData } as User);
      }

      return true;
    } catch (err: any) {
      // Handle Firebase auth errors
      if (err.code === 'auth/user-not-found') {
        setError('NEED_SIGNUP');
      } else {
        setError(err.message || 'Failed to sign in');
      }
      console.error(err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      await firebaseSignOut(auth);
      setUser(null);
      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to sign out');
      console.error(err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  return {
    user,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    clearError
  };
};
