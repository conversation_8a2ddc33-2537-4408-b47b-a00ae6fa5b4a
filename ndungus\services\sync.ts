/**
 * Sync service for handling offline-first data synchronization
 * Manages the sync queue and handles conflicts
 * Supports both API and Firestore synchronization
 */

import NetInfo from '@react-native-community/netinfo';
import { v4 as uuidv4 } from 'uuid';

import { getObject, setObject, StorageKey } from './storage';

// Sync status
export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  SUCCESS = 'success',
  ERROR = 'error',
}

// Sync result
export interface SyncResult {
  status: SyncStatus;
  message?: string;
  syncedItems: number;
  failedItems: number;
  timestamp: string;
}

// Last sync result
let lastSyncResult: SyncResult = {
  status: SyncStatus.IDLE,
  syncedItems: 0,
  failedItems: 0,
  timestamp: '',
};

// Check if device is online
export const isOnline = async (): Promise<boolean> => {
  const netInfo = await NetInfo.fetch();
  return netInfo.isConnected === true && netInfo.isInternetReachable === true;
};

// Sync operation types
export type SyncOperationType = 'create' | 'update' | 'delete';

// Sync operation
export interface SyncOperation {
  id: string;
  type: SyncOperationType;
  collection: string;
  data: any;
  timestamp: string;
  synced: boolean;
}

/**
 * Get all sync operations from storage
 * @returns Array of sync operations
 */
export async function getSyncQueue(): Promise<SyncOperation[]> {
  try {
    // Get sync queue from storage
    const syncQueue = await getObject<SyncOperation[]>(StorageKey.SYNC_QUEUE);

    // If no sync queue found, initialize with empty array
    if (!syncQueue) {
      await setObject(StorageKey.SYNC_QUEUE, []);
      return [];
    }

    return syncQueue;
  } catch (error) {
    console.error('Error getting sync queue:', error);
    return [];
  }
}

/**
 * Add a sync operation to the queue
 * @param type Operation type
 * @param collection Collection name
 * @param data Operation data
 * @returns Added sync operation
 */
export async function addToSyncQueue(
  type: SyncOperationType,
  collection: string,
  data: any
): Promise<SyncOperation> {
  try {
    // Get sync queue
    const syncQueue = await getSyncQueue();

    // Create new sync operation
    const syncOperation: SyncOperation = {
      id: uuidv4(),
      type,
      collection,
      data,
      timestamp: new Date().toISOString(),
      synced: false,
    };

    // Add to sync queue
    const updatedSyncQueue = [...syncQueue, syncOperation];

    // Save to storage
    await setObject(StorageKey.SYNC_QUEUE, updatedSyncQueue);

    return syncOperation;
  } catch (error) {
    console.error('Error adding to sync queue:', error);
    throw error;
  }
}

/**
 * Mark a sync operation as synced
 * @param id Sync operation ID
 * @returns Updated sync queue
 */
export async function markAsSynced(id: string): Promise<SyncOperation[]> {
  try {
    // Get sync queue
    const syncQueue = await getSyncQueue();

    // Find sync operation
    const syncOperationIndex = syncQueue.findIndex(op => op.id === id);

    if (syncOperationIndex === -1) {
      return syncQueue;
    }

    // Update sync operation
    const updatedSyncQueue = [...syncQueue];
    updatedSyncQueue[syncOperationIndex] = {
      ...updatedSyncQueue[syncOperationIndex],
      synced: true,
    };

    // Save to storage
    await setObject(StorageKey.SYNC_QUEUE, updatedSyncQueue);

    return updatedSyncQueue;
  } catch (error) {
    console.error('Error marking as synced:', error);
    return [];
  }
}

/**
 * Remove synced operations from the queue
 * @returns Updated sync queue
 */
export async function removeSyncedOperations(): Promise<SyncOperation[]> {
  try {
    // Get sync queue
    const syncQueue = await getSyncQueue();

    // Filter out synced operations
    const updatedSyncQueue = syncQueue.filter(op => !op.synced);

    // Save to storage
    await setObject(StorageKey.SYNC_QUEUE, updatedSyncQueue);

    return updatedSyncQueue;
  } catch (error) {
    console.error('Error removing synced operations:', error);
    return [];
  }
}

// Get last sync result
export const getLastSyncResult = (): SyncResult => {
  return lastSyncResult;
};

// Sync data with server
export const syncData = async (): Promise<SyncResult> => {
  // Check if device is online
  const online = await isOnline();
  if (!online) {
    lastSyncResult = {
      status: SyncStatus.ERROR,
      message: 'Device is offline',
      syncedItems: 0,
      failedItems: 0,
      timestamp: new Date().toISOString(),
    };
    return lastSyncResult;
  }

  // Get pending sync items
  const pendingItems = await getPendingSyncItems();
  if (pendingItems.length === 0) {
    lastSyncResult = {
      status: SyncStatus.SUCCESS,
      message: 'No items to sync',
      syncedItems: 0,
      failedItems: 0,
      timestamp: new Date().toISOString(),
    };
    return lastSyncResult;
  }

  // Update sync status
  lastSyncResult = {
    status: SyncStatus.SYNCING,
    syncedItems: 0,
    failedItems: 0,
    timestamp: new Date().toISOString(),
  };

  try {
    // Send pending items to server
    const result = await api.sync.uploadOfflineData(pendingItems);

    // Process sync results
    const { syncedIds, failedIds, conflicts } = result;

    // Mark synced items
    for (const id of syncedIds) {
      await markSyncItemAsSynced(id);
    }

    // Handle conflicts
    if (conflicts && conflicts.length > 0) {
      await handleConflicts(conflicts);
    }

    // Clear synced items
    await clearSyncedItems();

    // Update sync result
    lastSyncResult = {
      status: SyncStatus.SUCCESS,
      message: 'Sync completed successfully',
      syncedItems: syncedIds.length,
      failedItems: failedIds.length,
      timestamp: new Date().toISOString(),
    };

    return lastSyncResult;
  } catch (error: any) {
    // Update sync result
    lastSyncResult = {
      status: SyncStatus.ERROR,
      message: error.message || 'Sync failed',
      syncedItems: 0,
      failedItems: pendingItems.length,
      timestamp: new Date().toISOString(),
    };

    return lastSyncResult;
  }
};

// Handle conflicts
const handleConflicts = async (conflicts: any[]): Promise<void> => {
  // For each conflict
  for (const conflict of conflicts) {
    const { entity, id, serverData, localData, resolution } = conflict;

    // Get local data
    const localItems = await getArray<any>(entity);
    const localIndex = localItems.findIndex((item) => item.id === id);

    if (localIndex === -1) continue;

    // Apply resolution
    switch (resolution) {
      case 'server_wins':
        // Update local data with server data
        localItems[localIndex] = serverData;
        break;

      case 'client_wins':
        // Keep local data
        break;

      case 'merge':
        // Merge server and local data
        localItems[localIndex] = {
          ...serverData,
          ...localData,
          updatedAt: new Date().toISOString(),
        };
        break;

      default:
        // Default to server wins
        localItems[localIndex] = serverData;
        break;
    }

    // Save updated local data
    await setArray(entity, localItems);
  }
};

// Setup automatic sync
export const setupAutoSync = (intervalMinutes: number = 15): () => void => {
  // Convert minutes to milliseconds
  const interval = intervalMinutes * 60 * 1000;

  // Start sync interval
  const intervalId = setInterval(async () => {
    const online = await isOnline();
    if (online) {
      await syncData();
    }
  }, interval);

  // Return cleanup function
  return () => clearInterval(intervalId);
};

// Manual sync with progress callback
export const manualSync = async (
  progressCallback?: (status: SyncStatus, progress: number) => void
): Promise<SyncResult> => {
  try {
    // Check if device is online
    const online = await isOnline();
    if (!online) {
      const result = {
        status: SyncStatus.ERROR,
        message: 'Device is offline',
        syncedItems: 0,
        failedItems: 0,
        timestamp: new Date().toISOString(),
      };
      if (progressCallback) progressCallback(result.status, 0);
      return result;
    }

    // Get pending sync items
    const pendingItems = await getPendingSyncItems();
    if (pendingItems.length === 0) {
      const result = {
        status: SyncStatus.SUCCESS,
        message: 'No items to sync',
        syncedItems: 0,
        failedItems: 0,
        timestamp: new Date().toISOString(),
      };
      if (progressCallback) progressCallback(result.status, 100);
      return result;
    }

    // Update progress
    if (progressCallback) progressCallback(SyncStatus.SYNCING, 0);

    // Sync data
    const result = await syncData();

    // Update progress
    if (progressCallback) progressCallback(result.status, 100);

    return result;
  } catch (error: any) {
    // Handle error
    const result = {
      status: SyncStatus.ERROR,
      message: error.message || 'Sync failed',
      syncedItems: 0,
      failedItems: 0,
      timestamp: new Date().toISOString(),
    };

    // Update progress
    if (progressCallback) progressCallback(result.status, 0);

    return result;
  }
};

/**
 * Sync a single operation with Firestore
 * @param operation Sync operation
 * @returns Whether the operation was synced successfully
 */
export async function syncFirestoreOperation(operation: SyncOperation): Promise<boolean> {
  try {
    // Check if already synced
    if (operation.synced) {
      return true;
    }

    // Check if online
    const online = await isOnline();
    if (!online) {
      return false;
    }

    // Import Firestore functions
    const { addDocument, updateDocument, deleteDocument } = await import('./firebase');

    // Perform operation
    switch (operation.type) {
      case 'create':
        await addDocument(operation.collection, operation.data);
        break;
      case 'update':
        await updateDocument(operation.collection, operation.data.id, operation.data);
        break;
      case 'delete':
        await deleteDocument(operation.collection, operation.data.id);
        break;
    }

    // Mark as synced
    await markAsSynced(operation.id);

    return true;
  } catch (error) {
    console.error('Error syncing operation with Firestore:', error);
    return false;
  }
}

/**
 * Sync all operations with Firestore
 * @returns Number of operations synced
 */
export async function syncAllFirestoreOperations(): Promise<number> {
  try {
    // Check if online
    const online = await isOnline();

    if (!online) {
      return 0;
    }

    // Get sync queue
    const syncQueue = await getSyncQueue();

    // Filter unsynced operations
    const unsyncedOperations = syncQueue.filter(op => !op.synced);

    if (unsyncedOperations.length === 0) {
      return 0;
    }

    // Sync each operation
    let syncedCount = 0;

    for (const operation of unsyncedOperations) {
      const success = await syncFirestoreOperation(operation);

      if (success) {
        syncedCount++;
      }
    }

    // Remove synced operations
    await removeSyncedOperations();

    return syncedCount;
  } catch (error) {
    console.error('Error syncing all operations with Firestore:', error);
    return 0;
  }
}

/**
 * Sync local products with Firestore
 * @returns Number of products synced
 */
export async function syncFirestoreProducts(): Promise<number> {
  try {
    // Check if online
    const online = await isOnline();

    if (!online) {
      return 0;
    }

    // Get local products
    const { getLocalProducts } = await import('./firestoreProducts');
    const localProducts = await getLocalProducts();

    // Import Firestore functions
    const { getDocumentById, addDocument, updateDocument } = await import('./firebase');

    // Sync each product
    let syncedCount = 0;

    for (const product of localProducts) {
      // Check if product exists in Firestore
      const existingProduct = await getDocumentById(COLLECTIONS.PRODUCTS, product.id);

      if (existingProduct) {
        // Update product
        await updateDocument(COLLECTIONS.PRODUCTS, product.id, product);
      } else {
        // Create product
        await addDocument(COLLECTIONS.PRODUCTS, product);
      }

      syncedCount++;
    }

    return syncedCount;
  } catch (error) {
    console.error('Error syncing products with Firestore:', error);
    return 0;
  }
}

/**
 * Sync local sales with Firestore
 * @returns Number of sales synced
 */
export async function syncFirestoreSales(): Promise<number> {
  try {
    // Check if online
    const online = await isOnline();

    if (!online) {
      return 0;
    }

    // Get local sales
    const { getLocalSales } = await import('./firestoreSales');
    const localSales = await getLocalSales();

    // Import Firestore functions
    const { getDocumentById, addDocument, updateDocument } = await import('./firebase');

    // Sync each sale
    let syncedCount = 0;

    for (const sale of localSales) {
      // Check if sale exists in Firestore
      const existingSale = await getDocumentById(COLLECTIONS.SALES, sale.id);

      if (existingSale) {
        // Update sale
        await updateDocument(COLLECTIONS.SALES, sale.id, sale);
      } else {
        // Create sale
        await addDocument(COLLECTIONS.SALES, sale);
      }

      syncedCount++;
    }

    return syncedCount;
  } catch (error) {
    console.error('Error syncing sales with Firestore:', error);
    return 0;
  }
}

/**
 * Sync all local data with Firestore
 * @returns Sync results
 */
export async function syncAllFirestoreData(): Promise<{
  products: number;
  sales: number;
  operations: number;
}> {
  try {
    // Check if online
    const online = await isOnline();

    if (!online) {
      return {
        products: 0,
        sales: 0,
        operations: 0,
      };
    }

    // Sync products
    const productsCount = await syncFirestoreProducts();

    // Sync sales
    const salesCount = await syncFirestoreSales();

    // Sync operations
    const operationsCount = await syncAllFirestoreOperations();

    return {
      products: productsCount,
      sales: salesCount,
      operations: operationsCount,
    };
  } catch (error) {
    console.error('Error syncing all data with Firestore:', error);
    return {
      products: 0,
      sales: 0,
      operations: 0,
    };
  }
}
