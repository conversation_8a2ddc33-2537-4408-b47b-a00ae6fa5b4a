"use client";

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useCart } from '@/context/CartContext';

export interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  image: string;
  category: string;
  description: string;
  isSelected?: boolean;
  onSelectChange?: (isSelected: boolean) => void;
}

export default function ProductCard({
  id,
  name,
  price,
  image,
  category,
  description,
  isSelected = false,
  onSelectChange
}: ProductCardProps) {
  const [quantity, setQuantity] = useState(1);
  const [localSelected, setLocalSelected] = useState(isSelected);

  // Update local state when prop changes
  useEffect(() => {
    setLocalSelected(isSelected);
  }, [isSelected]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value > 0) {
      setQuantity(value);
    }
  };

  const [cartSuccess, setCartSuccess] = useState(false);

  // Get cart functions from context
  const { addToCart } = useCart();

  const handleAddToCart = () => {
    // Add the product to the cart with the selected quantity
    addToCart({
      id,
      name,
      price,
      image,
      category
    }, quantity);

    // Show success message
    setCartSuccess(true);

    // Hide success message after 3 seconds
    setTimeout(() => {
      setCartSuccess(false);
    }, 3000);
  };

  const handleToggleSelect = () => {
    const newSelectedState = !localSelected;
    setLocalSelected(newSelectedState);

    // Notify parent component if callback is provided
    if (onSelectChange) {
      onSelectChange(newSelectedState);
    }
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl relative
        ${localSelected ? 'ring-2 ring-accent scale-[1.02] shadow-lg' : 'hover:scale-[1.02]'}`}
    >
      {/* Success Message */}
      {cartSuccess && (
        <div className="absolute top-2 right-2 left-2 z-30 bg-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg flex items-center justify-center animate-fadeIn">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Added to cart!
        </div>
      )}
      <div className="relative">
        <div
          className={`absolute top-3 left-3 z-20 h-6 w-6 rounded-full flex items-center justify-center cursor-pointer
            ${localSelected ? 'bg-accent' : 'bg-white border border-gray-300'}`}
          onClick={handleToggleSelect}
        >
          {localSelected && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </div>

        <Link href={`/products/${id}`}>
          <div className="h-52 relative bg-gray-50">
            <Image
              src={image}
              alt={name}
              fill
              className="object-contain p-4 transition-transform duration-300 group-hover:scale-110"
            />
            {localSelected && (
              <div className="absolute inset-0 bg-accent bg-opacity-10"></div>
            )}
          </div>
        </Link>
      </div>

      <div className="p-5">
        <Link href={`/products/${id}`}>
          <h3 className="text-lg font-semibold text-primary mb-1 hover:text-accent transition-colors line-clamp-1">{name}</h3>
        </Link>
        <p className="text-gray-700 text-sm mb-2 font-medium">{category}</p>
        <p className="text-accent font-bold text-lg mb-3">KSh {price.toFixed(2)}</p>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{description}</p>

        <div className="flex items-center space-x-3 mb-4">
          <div className="flex-1">
            <label htmlFor={`quantity-${id}`} className="block text-sm text-gray-700 font-medium mb-1">Quantity</label>
            <div className="flex border border-gray-300 rounded overflow-hidden">
              <button
                onClick={() => quantity > 1 && setQuantity(quantity - 1)}
                className="bg-gray-200 px-3 py-1 text-gray-800 hover:bg-gray-300 transition-colors font-bold"
              >
                -
              </button>
              <input
                id={`quantity-${id}`}
                type="number"
                min="1"
                value={quantity}
                onChange={handleQuantityChange}
                className="w-full text-center border-0 py-1 focus:ring-0 text-gray-800"
              />
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="bg-gray-200 px-3 py-1 text-gray-800 hover:bg-gray-300 transition-colors font-bold"
              >
                +
              </button>
            </div>
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleAddToCart}
            className="flex-1 bg-primary text-white py-2 rounded-lg hover:bg-secondary transition-colors flex items-center justify-center shadow-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Add to Cart
          </button>
          <button
            className="bg-gray-100 text-primary p-2 rounded-lg hover:bg-gray-200 transition-colors shadow-sm"
            title="Add to Wishlist"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
