/**
 * Sale model for sales transactions
 */

import { Product } from './Product';

export type PaymentMethod = 'cash' | 'mpesa' | 'card' | 'bank' | 'other';
export type SaleStatus = 'completed' | 'pending' | 'cancelled';

export interface SaleItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  costPrice: number;
}

export interface Sale {
  id: string;
  items: SaleItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  profit: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
  status: SaleStatus;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSaleInput {
  items: {
    productId: string;
    quantity: number;
  }[];
  discount?: number;
  tax?: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
}

/**
 * Calculate the total price for a sale item
 * @param quantity Quantity of items
 * @param unitPrice Price per unit
 * @returns Total price
 */
export function calculateItemTotal(quantity: number, unitPrice: number): number {
  return quantity * unitPrice;
}

/**
 * Calculate the subtotal for a sale
 * @param items Sale items
 * @returns Subtotal
 */
export function calculateSubtotal(items: SaleItem[]): number {
  return items.reduce((sum, item) => sum + item.totalPrice, 0);
}

/**
 * Calculate the total profit for a sale
 * @param items Sale items
 * @returns Total profit
 */
export function calculateProfit(items: SaleItem[]): number {
  return items.reduce((sum, item) => {
    const itemProfit = item.totalPrice - (item.costPrice * item.quantity);
    return sum + itemProfit;
  }, 0);
}

/**
 * Calculate the final total for a sale
 * @param subtotal Subtotal amount
 * @param discount Discount amount
 * @param tax Tax amount
 * @returns Final total
 */
export function calculateTotal(subtotal: number, discount: number = 0, tax: number = 0): number {
  const afterDiscount = subtotal - discount;
  const afterTax = afterDiscount + tax;
  return afterTax;
}

/**
 * Create a sale item from a product and quantity
 * @param product Product to sell
 * @param quantity Quantity to sell
 * @returns Sale item
 */
export function createSaleItem(product: Product, quantity: number): SaleItem {
  const totalPrice = calculateItemTotal(quantity, product.sellingPrice);
  
  return {
    productId: product.id,
    productName: product.name,
    quantity,
    unitPrice: product.sellingPrice,
    totalPrice,
    costPrice: product.buyingPrice,
  };
}

/**
 * Format a sale date for display
 * @param dateString Date string
 * @returns Formatted date
 */
export function formatSaleDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Get a display name for a payment method
 * @param method Payment method
 * @returns Display name
 */
export function getPaymentMethodName(method: PaymentMethod): string {
  switch (method) {
    case 'cash':
      return 'Cash';
    case 'mpesa':
      return 'M-Pesa';
    case 'card':
      return 'Card';
    case 'bank':
      return 'Bank Transfer';
    case 'other':
      return 'Other';
    default:
      return 'Unknown';
  }
}
