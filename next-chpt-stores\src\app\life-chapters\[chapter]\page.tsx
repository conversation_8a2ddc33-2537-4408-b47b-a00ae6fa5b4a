"use client";

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CategorySection from '@/components/CategorySection';
import LifeChapterWelcome from '@/components/LifeChapterWelcome';
import { getLifeChapters, getProductsByLifeChapter, getCategoriesInLifeChapter, getProductsByCategoryInLifeChapter } from '@/data/products';

interface LifeChapterPageProps {
  params: {
    chapter: string;
  };
}

export default function LifeChapterPage({ params }: LifeChapterPageProps) {
  const [lifeChapter, setLifeChapter] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [productsByCategory, setProductsByCategory] = useState<Record<string, any[]>>({});
  const [activeTab, setActiveTab] = useState<string>('categories');
  const [searchQuery, setSearchQuery] = useState('');
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);

  // Define category details
  const categoryDetails = {
    "Kitchen": {
      description: "Essential kitchen items for cooking, cleaning, and organization",
      image: "/categories/kitchen.jpg",
      color: "from-blue-500 to-blue-700"
    },
    "Bedroom": {
      description: "Everything you need for a comfortable and restful sleep space",
      image: "/categories/bedroom.jpg",
      color: "from-indigo-500 to-indigo-700"
    },
    "Bathroom": {
      description: "Bathroom essentials for cleanliness and organization",
      image: "/categories/bathroom.jpg",
      color: "from-teal-500 to-teal-700"
    },
    "Electronics": {
      description: "Must-have electronic devices and accessories",
      image: "/categories/electronics.jpg",
      color: "from-gray-700 to-gray-900"
    },
    "Office": {
      description: "Everything you need for a productive work environment",
      image: "/categories/office.jpg",
      color: "from-green-500 to-green-700"
    },
    "Moving": {
      description: "Essential supplies for a smooth moving experience",
      image: "/categories/moving.jpg",
      color: "from-purple-500 to-purple-700"
    },
    "Home": {
      description: "General home essentials for comfort and functionality",
      image: "/categories/home.jpg",
      color: "from-yellow-500 to-yellow-700"
    },
    "Accessories": {
      description: "Useful accessories to complement your essentials",
      image: "/categories/accessories.jpg",
      color: "from-pink-500 to-pink-700"
    },
    "Clothing": {
      description: "Essential clothing items for your new chapter",
      image: "/categories/clothing.jpg",
      color: "from-red-500 to-red-700"
    },
    "Nursery": {
      description: "Everything you need to create a safe and comfortable nursery",
      image: "/categories/nursery.jpg",
      color: "from-orange-500 to-orange-700"
    }
  };

  useEffect(() => {
    // Convert URL slug to proper chapter name
    const chapterSlug = params.chapter;
    const allChapters = getLifeChapters();

    // Find the chapter that matches the slug (case-insensitive)
    const chapter = allChapters.find(
      ch => ch.toLowerCase().replace(/\s+/g, '-') === chapterSlug.toLowerCase()
    );

    if (!chapter) {
      notFound();
      return;
    }

    setLifeChapter(chapter);

    // Get categories for this life chapter
    const chapterCategories = getCategoriesInLifeChapter(chapter);
    setCategories(chapterCategories);

    // Get products for each category
    const productsByCat: Record<string, any[]> = {};
    let allChapterProducts: any[] = [];

    chapterCategories.forEach(category => {
      const categoryProducts = getProductsByCategoryInLifeChapter(chapter, category);
      productsByCat[category] = categoryProducts;
      allChapterProducts = [...allChapterProducts, ...categoryProducts];
    });

    setProductsByCategory(productsByCat);
    setAllProducts(allChapterProducts);
    setFilteredProducts(allChapterProducts);
  }, [params.chapter]);

  // Filter products based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredProducts(allProducts);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = allProducts.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query) ||
        product.subcategory.toLowerCase().includes(query)
      );
      setFilteredProducts(filtered);
    }
  }, [searchQuery, allProducts]);

  if (!lifeChapter) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-12">
            <h1 className="text-5xl font-bold text-primary mb-8 text-center">{lifeChapter}</h1>

            <div className="mb-12">
              <LifeChapterWelcome lifeChapter={lifeChapter} />
            </div>

            {/* Search bar for all products in this life chapter */}
            <div className="relative max-w-3xl mx-auto mb-12 bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-semibold text-primary mb-4 text-center">Find Exactly What You Need</h2>
              <div className="relative">
                <input
                  type="text"
                  placeholder={`Search all ${lifeChapter} products...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-gray-300 rounded-full px-6 py-4 pl-14 focus:outline-none focus:ring-2 focus:ring-primary text-lg shadow-sm"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-7 w-7 absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <div className="flex justify-between items-center mt-3">
                <p className="text-gray-600 text-sm">Try searching for "essentials", "kitchen", or specific items</p>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-accent hover:text-primary text-sm font-medium bg-gray-100 px-3 py-1 rounded-full"
                  >
                    Clear Search
                  </button>
                )}
              </div>
            </div>

            {/* Tabs for Categories and All Products */}
            <div className="mb-8 categories-section">
              <div className="flex border-b border-gray-200">
                <button
                  className={`py-4 px-6 font-medium text-lg ${activeTab === 'categories' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-primary'}`}
                  onClick={() => setActiveTab('categories')}
                >
                  Categories
                </button>
                <button
                  className={`py-4 px-6 font-medium text-lg ${activeTab === 'all-products' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-primary'}`}
                  onClick={() => setActiveTab('all-products')}
                >
                  All Products
                </button>
              </div>
            </div>
          </div>

          {activeTab === 'categories' && (
            <div>
              {searchQuery ? (
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-primary mb-6">Search Results</h2>
                  {filteredProducts.length === 0 ? (
                    <div className="bg-white p-12 rounded-lg shadow-md text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-xl font-semibold text-primary mb-2">No products found</h3>
                      <p className="text-gray-700 text-lg mb-4">We couldn't find any products matching your search criteria.</p>
                      <button
                        onClick={() => setSearchQuery('')}
                        className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-secondary transition-colors shadow-sm"
                      >
                        Clear Search
                      </button>
                    </div>
                  ) : (
                    <CategorySection
                      title="Search Results"
                      products={filteredProducts}
                    />
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                  {categories.map(category => {
                    const details = categoryDetails[category as keyof typeof categoryDetails] || {
                      description: `Essential ${category.toLowerCase()} items for your ${lifeChapter} journey`,
                      image: "/placeholder.jpg",
                      color: "from-primary to-secondary"
                    };

                    return (
                      <div
                        key={category}
                        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full cursor-pointer"
                        onClick={() => {
                          document.getElementById(`category-${category.toLowerCase().replace(/\s+/g, '-')}`)?.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                          });
                        }}
                      >
                        <div className="relative h-56">
                          <div className={`absolute inset-0 bg-gradient-to-t ${details.color} opacity-90 z-10`}></div>
                          <Image
                            src={details.image}
                            alt={category}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 z-20 flex items-center justify-center">
                            <h3 className="text-2xl font-bold text-white text-center px-4 drop-shadow-lg">{category}</h3>
                          </div>
                        </div>
                        <div className="p-6">
                          <p className="text-gray-700 mb-4">
                            {details.description}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-primary font-semibold">Browse {category}</span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5 text-accent"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Display products by category */}
              {!searchQuery && categories.map(category => (
                <div key={category} id={`category-${category.toLowerCase().replace(/\s+/g, '-')}`}>
                  <CategorySection
                    title={category}
                    products={productsByCategory[category] || []}
                  />
                </div>
              ))}
            </div>
          )}

          {activeTab === 'all-products' && (
            <CategorySection
              title={`All ${lifeChapter} Products`}
              products={filteredProducts}
            />
          )}
        </div>
      </main>

      <Footer />
    </>
  );
}
