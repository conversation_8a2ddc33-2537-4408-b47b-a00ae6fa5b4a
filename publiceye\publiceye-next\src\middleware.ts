import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the hasSeenLanding cookie
  const hasSeenLanding = request.cookies.get('hasSeenLanding');
  
  // If this is the root path and user hasn't seen the landing page
  if (request.nextUrl.pathname === '/' && !hasSeenLanding) {
    // Redirect to about page
    return NextResponse.redirect(new URL('/about', request.url));
  }

  // Remove the redirect from landing to home
  // This allows users to visit the landing page even after they've seen it
  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/about'],
}; 