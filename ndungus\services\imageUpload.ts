/**
 * Image upload service for React Native
 * Handles image picking and uploading to Cloudinary
 */

import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import { FOLDERS, uploadImage } from './cloudinary';

// Image picker options
const DEFAULT_IMAGE_PICKER_OPTIONS: ImagePicker.ImagePickerOptions = {
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  allowsEditing: true,
  aspect: [1, 1],
  quality: 0.8,
};

// Image options
const DEFAULT_IMAGE_OPTIONS = {
  quality: 0.7,
};

/**
 * Request camera permissions
 * @returns Whether permission was granted
 */
export async function requestCameraPermissions(): Promise<boolean> {
  const { status } = await ImagePicker.requestCameraPermissionsAsync();
  return status === 'granted';
}

/**
 * Request media library permissions
 * @returns Whether permission was granted
 */
export async function requestMediaLibraryPermissions(): Promise<boolean> {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
  return status === 'granted';
}

/**
 * Pick an image from the camera
 * @param options Image picker options
 * @returns Image result or null if cancelled
 */
export async function pickImageFromCamera(
  options: ImagePicker.ImagePickerOptions = DEFAULT_IMAGE_PICKER_OPTIONS
): Promise<ImagePicker.ImagePickerResult> {
  // Request camera permissions
  const permissionGranted = await requestCameraPermissions();
  if (!permissionGranted) {
    throw new Error('Camera permission not granted');
  }

  // Launch camera
  return ImagePicker.launchCameraAsync(options);
}

/**
 * Pick an image from the media library
 * @param options Image picker options
 * @returns Image result or null if cancelled
 */
export async function pickImageFromLibrary(
  options: ImagePicker.ImagePickerOptions = DEFAULT_IMAGE_PICKER_OPTIONS
): Promise<ImagePicker.ImagePickerResult> {
  // Request media library permissions
  const permissionGranted = await requestMediaLibraryPermissions();
  if (!permissionGranted) {
    throw new Error('Media library permission not granted');
  }

  // Launch image picker
  return ImagePicker.launchImageLibraryAsync(options);
}

/**
 * Simple function to check image size
 * @param uri Image URI
 * @returns Original URI (no compression for now)
 */
export async function checkImageSize(
  uri: string
): Promise<string> {
  try {
    // Get image info
    const fileInfo = await FileSystem.getInfoAsync(uri);

    // Log file size
    if (fileInfo.size) {
      console.log(`Image size: ${(fileInfo.size / 1024).toFixed(2)} KB`);
    }

    // Return original URI
    return uri;
  } catch (error) {
    console.error('Error checking image size:', error);
    return uri;
  }
}

/**
 * Convert image URI to base64
 * @param uri Image URI
 * @returns Base64 string
 */
export async function imageToBase64(uri: string): Promise<string> {
  try {
    // Read file as base64
    const base64 = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Get file extension
    const extension = uri.split('.').pop()?.toLowerCase() || 'jpg';

    // Return data URL
    return `data:image/${extension};base64,${base64}`;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw error;
  }
}

/**
 * Upload an image to Cloudinary
 * @param uri Image URI
 * @param folder Cloudinary folder
 * @returns Upload result with secure URL
 */
export async function uploadImageToCloudinary(
  uri: string,
  folder: string = FOLDERS.PRODUCTS
): Promise<{ secure_url: string }> {
  try {
    // Check image size
    const checkedUri = await checkImageSize(uri);

    // Upload to Cloudinary directly using the URI
    return uploadImage(checkedUri, folder);
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);
    throw error;
  }
}

/**
 * Pick and upload an image
 * @param source 'camera' or 'library'
 * @param folder Cloudinary folder
 * @returns Upload result or null if cancelled
 */
export async function pickAndUploadImage(
  source: 'camera' | 'library' = 'library',
  folder: string = FOLDERS.PRODUCTS
): Promise<{ secure_url: string } | null> {
  try {
    // Pick image
    const result = source === 'camera'
      ? await pickImageFromCamera()
      : await pickImageFromLibrary();

    // Check if cancelled
    if (result.canceled || !result.assets || result.assets.length === 0) {
      return null;
    }

    // Get image URI
    const uri = result.assets[0].uri;

    // Upload to Cloudinary
    return uploadImageToCloudinary(uri, folder);
  } catch (error) {
    console.error('Error picking and uploading image:', error);
    throw error;
  }
}
