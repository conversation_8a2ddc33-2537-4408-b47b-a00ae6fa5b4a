import { GoogleGenerativeAI } from '@google/generative-ai';

// Configuration
const API_KEY = process.env.GEMINI_API_KEY || '';
const MODEL_NAME = 'gemini-pro';
const TIMEOUT_MS = 10000; // 10 seconds timeout

// Initialize the Gemini AI
let genAI: GoogleGenerativeAI | null = null;
try {
  genAI = new GoogleGenerativeAI(API_KEY);
} catch (error) {
  console.error('Failed to initialize Gemini AI:', error);
}

// Interface for budget recommendation request
interface BudgetRecommendationRequest {
  budget: number;
  lifeChapter?: string;
  category?: string;
  products: any[];
}

// Interface for budget recommendation response
interface BudgetRecommendationResponse {
  success: boolean;
  packages?: Array<{
    title: string;
    description: string;
    items: any[];
    totalPrice: number;
  }>;
  error?: string;
  isAIGenerated: boolean;
}

/**
 * Get budget recommendations using Gemini AI
 */
export async function getBudgetRecommendations(
  request: BudgetRecommendationRequest
): Promise<BudgetRecommendationResponse> {
  // If Gemini AI is not available or API key is missing, use fallback
  if (!genAI || !API_KEY) {
    console.warn('Gemini AI not available, using fallback recommendation system');
    return getFallbackRecommendations(request);
  }

  try {
    // Set up a timeout for the AI request
    const timeoutPromise = new Promise<BudgetRecommendationResponse>((_, reject) => {
      setTimeout(() => {
        reject(new Error('AI request timed out'));
      }, TIMEOUT_MS);
    });

    // Make the actual AI request
    const aiRequestPromise = makeAIRequest(request);

    // Race between the AI request and the timeout
    const result = await Promise.race([aiRequestPromise, timeoutPromise]);
    return result;
  } catch (error) {
    console.error('Error getting AI recommendations:', error);
    return getFallbackRecommendations(request);
  }
}

/**
 * Make the actual AI request to Gemini
 */
async function makeAIRequest(
  request: BudgetRecommendationRequest
): Promise<BudgetRecommendationResponse> {
  try {
    if (!genAI) throw new Error('Gemini AI not initialized');
    
    const model = genAI.getGenerativeModel({ model: MODEL_NAME });
    
    // Prepare the prompt
    const prompt = `
      I need recommendations for a budget of ${request.budget} KSh.
      ${request.lifeChapter ? `Life Chapter: ${request.lifeChapter}` : ''}
      ${request.category ? `Category: ${request.category}` : ''}
      
      Available products:
      ${request.products.map(p => `- ${p.name}: KSh ${p.price} (${p.description})`).join('\n')}
      
      Please create 3 different packages of items that fit within this budget.
      For each package, provide:
      1. A title
      2. A brief description
      3. List of items with their prices
      4. Total price
      
      Format your response as a JSON object with this structure:
      {
        "packages": [
          {
            "title": "Package Title",
            "description": "Package description",
            "items": [{"id": "product-id", "name": "Product Name", "price": 1000}],
            "totalPrice": 1000
          }
        ]
      }
    `;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    // Extract JSON from the response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) throw new Error('Invalid AI response format');
    
    const data = JSON.parse(jsonMatch[0]);
    
    return {
      success: true,
      packages: data.packages,
      isAIGenerated: true
    };
  } catch (error) {
    console.error('Error in AI request:', error);
    throw error;
  }
}

/**
 * Fallback recommendation system when AI is unavailable
 */
function getFallbackRecommendations(
  request: BudgetRecommendationRequest
): BudgetRecommendationResponse {
  const { budget, products } = request;
  
  // Sort products by price (ascending)
  const sortedProducts = [...products].sort((a, b) => a.price - b.price);
  
  // Create basic packages based on price ranges
  const packages = [];
  
  // Essential package (lowest priced items)
  const essentialItems = getItemsWithinBudget(sortedProducts, budget * 0.9, 3, 5);
  if (essentialItems.length > 0) {
    packages.push({
      title: "Essential Package",
      description: "Basic essentials that fit your budget",
      items: essentialItems,
      totalPrice: essentialItems.reduce((sum, item) => sum + item.price, 0)
    });
  }
  
  // Balanced package (mix of prices)
  const balancedItems = getBalancedItems(sortedProducts, budget * 0.95);
  if (balancedItems.length > 0) {
    packages.push({
      title: "Balanced Package",
      description: "A good mix of essentials and quality items",
      items: balancedItems,
      totalPrice: balancedItems.reduce((sum, item) => sum + item.price, 0)
    });
  }
  
  // Premium package (fewer but higher quality items)
  const premiumItems = getPremiumItems(sortedProducts, budget);
  if (premiumItems.length > 0) {
    packages.push({
      title: "Premium Selection",
      description: "Fewer but higher quality items",
      items: premiumItems,
      totalPrice: premiumItems.reduce((sum, item) => sum + item.price, 0)
    });
  }
  
  return {
    success: packages.length > 0,
    packages: packages.length > 0 ? packages : undefined,
    error: packages.length === 0 ? "Could not create packages within budget" : undefined,
    isAIGenerated: false
  };
}

/**
 * Helper function to get items within budget
 */
function getItemsWithinBudget(
  products: any[],
  budget: number,
  minItems: number,
  maxItems: number
): any[] {
  const result = [];
  let remainingBudget = budget;
  
  for (const product of products) {
    if (product.price <= remainingBudget && result.length < maxItems) {
      result.push(product);
      remainingBudget -= product.price;
    }
    
    if (result.length >= maxItems) break;
  }
  
  return result.length >= minItems ? result : [];
}

/**
 * Helper function to get a balanced mix of items
 */
function getBalancedItems(products: any[], budget: number): any[] {
  const lowPriced = products.slice(0, Math.floor(products.length / 3));
  const midPriced = products.slice(Math.floor(products.length / 3), Math.floor(products.length * 2 / 3));
  const highPriced = products.slice(Math.floor(products.length * 2 / 3));
  
  const result = [];
  let remainingBudget = budget;
  
  // Try to include one high-priced item
  for (const product of highPriced) {
    if (product.price <= remainingBudget * 0.5) {
      result.push(product);
      remainingBudget -= product.price;
      break;
    }
  }
  
  // Add some mid-priced items
  for (const product of midPriced) {
    if (product.price <= remainingBudget * 0.4 && !result.some(item => item.id === product.id)) {
      result.push(product);
      remainingBudget -= product.price;
      if (result.length >= 3) break;
    }
  }
  
  // Fill with low-priced items
  for (const product of lowPriced) {
    if (product.price <= remainingBudget && !result.some(item => item.id === product.id)) {
      result.push(product);
      remainingBudget -= product.price;
      if (result.length >= 5) break;
    }
  }
  
  return result.length >= 3 ? result : [];
}

/**
 * Helper function to get premium items
 */
function getPremiumItems(products: any[], budget: number): any[] {
  // Get the top 50% most expensive products
  const premiumProducts = [...products].sort((a, b) => b.price - a.price)
    .slice(0, Math.ceil(products.length / 2));
  
  const result = [];
  let remainingBudget = budget;
  
  for (const product of premiumProducts) {
    if (product.price <= remainingBudget) {
      result.push(product);
      remainingBudget -= product.price;
      if (result.length >= 3) break;
    }
  }
  
  return result.length > 0 ? result : [];
}
