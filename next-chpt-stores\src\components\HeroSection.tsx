import Link from 'next/link';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-r from-primary to-secondary py-16 md:py-24">
      {/* Dark overlay for better text contrast */}
      <div className="absolute inset-0 bg-black opacity-50"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">
              Navigate Life's Chapters with Confidence
            </h1>
            <p className="text-lg mb-8 text-white drop-shadow-lg">
              From moving out to welcoming a baby, we've curated everything you need for life's major transitions.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                href="/life-chapters"
                className="bg-white text-primary px-8 py-3 rounded-full font-medium text-center hover:bg-gray-100 transition-colors shadow-md"
              >
                Explore Life Chapters
              </Link>
              <Link
                href="/about"
                className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full font-medium text-center hover:bg-white hover:text-primary transition-colors shadow-md"
              >
                Learn More
              </Link>
            </div>
          </div>

          <div className="relative h-64 md:h-96">
            <Image
              src="/hero-image.jpg"
              alt="Life transitions essentials"
              fill
              className="object-cover rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full h-16 bg-light" style={{ clipPath: 'polygon(0 100%, 100% 100%, 100% 0)' }}></div>
    </section>
  );
}
