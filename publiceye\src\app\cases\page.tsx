import Link from 'next/link';

export default function CasesPage() {
  // This would normally fetch data from your database
  const cases = [
    {
      id: '1',
      title: 'Police Brutality Case in Minneapolis',
      description: 'Investigation into alleged police misconduct during a peaceful protest.',
      location: 'Minneapolis, MN',
      stage: 'investigate',
      createdAt: '2023-05-15',
      followers: 1245
    },
    {
      id: '2',
      title: 'Environmental Damage Lawsuit',
      description: 'Class action lawsuit against corporation for illegal toxic waste dumping.',
      location: 'Houston, TX',
      stage: 'trial',
      createdAt: '2023-06-22',
      followers: 876
    },
    {
      id: '3',
      title: 'Corruption in City Council',
      description: 'Investigation into alleged bribery and corruption in local government.',
      location: 'Chicago, IL',
      stage: 'report',
      createdAt: '2023-07-10',
      followers: 543
    }
  ];

  return (
    <main>
      <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">All Cases</h1>
          <p className="text-xl opacity-90">Browse all cases currently being tracked.</p>
        </div>
      </section>
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h2 className="text-2xl font-bold">Recent Cases</h2>
            <p className="text-gray-600">Showing {cases.length} cases</p>
          </div>
          
          <Link 
            href="/cases/create"
            className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium hover:shadow-lg transition-all"
          >
            Create New Case
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cases.map(caseItem => (
            <div key={caseItem.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all">
              <div className="h-4 bg-gradient-to-r from-red-600 to-red-800"></div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold">{caseItem.title}</h3>
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    {caseItem.stage.toUpperCase()}
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{caseItem.description}</p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>{caseItem.location}</span>
                  <span>{caseItem.followers} followers</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  );
}
