export interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  lifeChapter: string;  // Independent Living, Relocating, Couples, Baby
  category: string;     // Kitchen, Bedroom, etc.
  subcategory: string;  // Essentials, Intermediate, Comprehensive
  description: string;
}

export const products: Product[] = [
  // Independent Living - Kitchen Category
  {
    id: "kitchen-1",
    name: "Kitchen Towel",
    price: 350,
    image: "/products/kitchen-towel.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Absorbent kitchen towel for cleaning spills and drying dishes."
  },
  {
    id: "kitchen-2",
    name: "Dish Rack",
    price: 1200,
    image: "/products/dish-rack.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Organizers",
    description: "Compact dish rack for drying and organizing your dishes."
  },
  {
    id: "kitchen-3",
    name: "Mop",
    price: 750,
    image: "/products/mop.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Efficient mop for keeping your floors clean and spotless."
  },
  {
    id: "kitchen-4",
    name: "Dustpan and Brush",
    price: 450,
    image: "/products/dustpan.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Handy dustpan and brush set for quick cleanups."
  },
  {
    id: "kitchen-5",
    name: "Seal Tape",
    price: 200,
    image: "/products/seal-tape.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Waterproof seal tape for securing containers and packages."
  },
  {
    id: "kitchen-6",
    name: "Dough Mats",
    price: 600,
    image: "/products/dough-mats.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Non-stick dough mats for baking and food preparation."
  },
  {
    id: "kitchen-7",
    name: "Kettle",
    price: 1500,
    image: "/products/kettle.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Electronics",
    description: "Electric kettle for quickly boiling water for tea, coffee, or instant meals."
  },
  {
    id: "kitchen-8",
    name: "Table Mat",
    price: 400,
    image: "/products/table-mat.jpg",
    lifeChapter: "Independent Living",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Heat-resistant table mat to protect surfaces from hot dishes."
  },

  // Independent Living - Bedroom Category
  {
    id: "bedroom-1",
    name: "Pillow & Pillow Cover",
    price: 1200,
    image: "/products/pillow.jpg",
    lifeChapter: "Independent Living",
    category: "Bedroom",
    subcategory: "Essentials",
    description: "Comfortable pillow with a soft, washable cover for a good night's sleep."
  },
  {
    id: "bedroom-2",
    name: "Bedsheets",
    price: 1500,
    image: "/products/bedsheets.jpg",
    lifeChapter: "Independent Living",
    category: "Bedroom",
    subcategory: "Essentials",
    description: "Soft, durable bedsheets for comfort and style in your bedroom."
  },
  {
    id: "bedroom-3",
    name: "Duvet",
    price: 2500,
    image: "/products/duvet.jpg",
    lifeChapter: "Independent Living",
    category: "Bedroom",
    subcategory: "Intermediate",
    description: "Warm, cozy duvet to keep you comfortable during cold nights."
  },
  {
    id: "bedroom-4",
    name: "Fleece Blanket",
    price: 1800,
    image: "/products/fleece-blanket.jpg",
    lifeChapter: "Independent Living",
    category: "Bedroom",
    subcategory: "Comprehensive",
    description: "Soft fleece blanket for extra warmth and comfort."
  },

  // Independent Living - Bathroom Category
  {
    id: "bathroom-1",
    name: "Mosquito Net",
    price: 900,
    image: "/products/mosquito-net.jpg",
    lifeChapter: "Independent Living",
    category: "Bathroom",
    subcategory: "Essentials",
    description: "Protective mosquito net for shared bathroom spaces."
  },
  {
    id: "bathroom-2",
    name: "Bathing Towel",
    price: 1000,
    image: "/products/bathing-towel.jpg",
    lifeChapter: "Independent Living",
    category: "Bathroom",
    subcategory: "Essentials",
    description: "Absorbent, quick-drying towel for after-shower use."
  },
  {
    id: "bathroom-3",
    name: "Shower Cap",
    price: 250,
    image: "/products/shower-cap.jpg",
    lifeChapter: "Independent Living",
    category: "Bathroom",
    subcategory: "Essentials",
    description: "Waterproof shower cap to keep your hair dry while showering."
  },
  {
    id: "bathroom-4",
    name: "Bathroom Mat",
    price: 700,
    image: "/products/bathroom-mat.jpg",
    lifeChapter: "Independent Living",
    category: "Bathroom",
    subcategory: "Intermediate",
    description: "Non-slip bathroom mat for safety and comfort after showering."
  },

  // Independent Living - Professional Essentials
  {
    id: "independent-1",
    name: "Laptop",
    price: 45000,
    image: "/products/laptop.jpg",
    lifeChapter: "Independent Living",
    category: "Electronics",
    subcategory: "Essentials",
    description: "Reliable laptop for work, job applications, and staying connected."
  },
  {
    id: "independent-2",
    name: "Professional Backpack",
    price: 3500,
    image: "/products/backpack.jpg",
    lifeChapter: "Independent Living",
    category: "Accessories",
    subcategory: "Essentials",
    description: "Stylish and functional backpack for your daily commute and work essentials."
  },
  {
    id: "independent-3",
    name: "Business Casual Attire",
    price: 6000,
    image: "/products/business-attire.jpg",
    lifeChapter: "Independent Living",
    category: "Clothing",
    subcategory: "Essentials",
    description: "Professional clothing suitable for interviews and office environments."
  },
  {
    id: "independent-4",
    name: "Planner",
    price: 1200,
    image: "/products/planner.jpg",
    lifeChapter: "Independent Living",
    category: "Office",
    subcategory: "Essentials",
    description: "Comprehensive planner to organize your professional and personal life."
  },

  // Relocating - Home Essentials
  {
    id: "relocating-1",
    name: "Moving Boxes Set",
    price: 2500,
    image: "/products/moving-boxes.jpg",
    lifeChapter: "Relocating",
    category: "Moving",
    subcategory: "Essentials",
    description: "Sturdy boxes in various sizes for packing your belongings."
  },
  {
    id: "relocating-2",
    name: "Packing Tape",
    price: 350,
    image: "/products/packing-tape.jpg",
    lifeChapter: "Relocating",
    category: "Moving",
    subcategory: "Essentials",
    description: "Strong adhesive tape for securing your packed boxes."
  },
  {
    id: "relocating-3",
    name: "Bubble Wrap",
    price: 800,
    image: "/products/bubble-wrap.jpg",
    lifeChapter: "Relocating",
    category: "Moving",
    subcategory: "Essentials",
    description: "Protective bubble wrap for fragile items during your move."
  },
  {
    id: "relocating-4",
    name: "Tool Kit",
    price: 3500,
    image: "/products/tool-kit.jpg",
    lifeChapter: "Relocating",
    category: "Home",
    subcategory: "Essentials",
    description: "Essential tools for assembling furniture and making minor repairs in your new home."
  },

  // Couples (Newly Weds) - Home Essentials
  {
    id: "couples-1",
    name: "Cookware Set",
    price: 12000,
    image: "/products/cookware-set.jpg",
    lifeChapter: "Couples",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Complete set of quality cookware for preparing meals together."
  },
  {
    id: "couples-2",
    name: "Dinnerware Set",
    price: 8000,
    image: "/products/dinnerware.jpg",
    lifeChapter: "Couples",
    category: "Kitchen",
    subcategory: "Essentials",
    description: "Elegant dinnerware set for everyday use and special occasions."
  },
  {
    id: "couples-3",
    name: "Bedding Set",
    price: 15000,
    image: "/products/bedding-set.jpg",
    lifeChapter: "Couples",
    category: "Bedroom",
    subcategory: "Essentials",
    description: "Luxurious bedding set for comfort and style in your shared bedroom."
  },
  {
    id: "couples-4",
    name: "Smart Speaker",
    price: 7000,
    image: "/products/smart-speaker.jpg",
    lifeChapter: "Couples",
    category: "Electronics",
    subcategory: "Comprehensive",
    description: "Voice-controlled speaker for music, information, and smart home control."
  },

  // Baby - Nursery Essentials
  {
    id: "baby-1",
    name: "Crib",
    price: 25000,
    image: "/products/crib.jpg",
    lifeChapter: "Baby",
    category: "Nursery",
    subcategory: "Essentials",
    description: "Safe and comfortable crib for your baby's sleep."
  },
  {
    id: "baby-2",
    name: "Changing Table",
    price: 18000,
    image: "/products/changing-table.jpg",
    lifeChapter: "Baby",
    category: "Nursery",
    subcategory: "Essentials",
    description: "Practical changing table with storage for diapers and baby care items."
  },
  {
    id: "baby-3",
    name: "Baby Monitor",
    price: 12000,
    image: "/products/baby-monitor.jpg",
    lifeChapter: "Baby",
    category: "Electronics",
    subcategory: "Essentials",
    description: "Reliable monitor to keep an eye and ear on your baby from another room."
  },
  {
    id: "baby-4",
    name: "Diaper Bag",
    price: 5000,
    image: "/products/diaper-bag.jpg",
    lifeChapter: "Baby",
    category: "Accessories",
    subcategory: "Essentials",
    description: "Spacious and organized bag for all your baby's essentials when on the go."
  }
];

// Get products by life chapter
export const getProductsByLifeChapter = (lifeChapter: string) => {
  return products.filter(product => product.lifeChapter.toLowerCase() === lifeChapter.toLowerCase());
};

// Get products by category within a life chapter
export const getProductsByCategoryInLifeChapter = (lifeChapter: string, category: string) => {
  return products.filter(
    product =>
      product.lifeChapter.toLowerCase() === lifeChapter.toLowerCase() &&
      product.category.toLowerCase() === category.toLowerCase()
  );
};

// Get all life chapters
export const getLifeChapters = () => {
  const chapters = [...new Set(products.map(product => product.lifeChapter))];
  return chapters;
};

// Get all categories within a life chapter
export const getCategoriesInLifeChapter = (lifeChapter: string) => {
  const productsInChapter = getProductsByLifeChapter(lifeChapter);
  const categories = [...new Set(productsInChapter.map(product => product.category))];
  return categories;
};

// Get product by ID
export const getProductById = (id: string) => {
  return products.find(product => product.id === id);
};

// Legacy functions for backward compatibility
export const getProductsByCategory = (category: string) => {
  return products.filter(product => product.category.toLowerCase() === category.toLowerCase());
};

export const getCategories = () => {
  const categories = [...new Set(products.map(product => product.category))];
  return categories;
};

export const getSubcategories = () => {
  const subcategories = [...new Set(products.map(product => product.subcategory))];
  return subcategories;
};

export const getProductsBySubcategory = (subcategory: string) => {
  return products.filter(product => product.subcategory.toLowerCase() === subcategory.toLowerCase());
};
