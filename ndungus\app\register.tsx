import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, Pressable, ScrollView, StyleSheet, Text, TextInput, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from './_layout';

export default function RegisterScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Get auth context
  const { signIn } = useAuth();

  // Handle registration
  const handleRegister = async () => {
    // Validate form
    if (!name || !email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // In a real app, this would call the API
      // await api.auth.register({ name, email, password });

      // For demo purposes, simulate a successful registration after a delay
      setTimeout(() => {
        setIsLoading(false);
        signIn(); // This will trigger navigation to tabs
      }, 1500);
    } catch (error: any) {
      setIsLoading(false);
      setError(error.message || 'Registration failed');
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}>
        {/* Logo and Header */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/icon.png')}
            style={styles.logo}
            contentFit="contain"
          />
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Ndungus Shop
          </Text>
          <Text
            style={[
              styles.subtitle,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Create a new account
          </Text>
        </View>

        {/* Registration Form */}
        <GlassCard style={styles.formCard}>
          {error ? (
            <View
              style={[
                styles.errorContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(239, 68, 68, 0.2)'
                    : 'rgba(239, 68, 68, 0.1)',
                },
              ]}>
              <MaterialIcons name="error" size={20} color={Colors.error} />
              <Text style={[styles.errorText, { color: Colors.error }]}>
                {error}
              </Text>
            </View>
          ) : null}

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Full Name
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="person"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Enter your full name"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                value={name}
                onChangeText={setName}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Email
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="email"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Enter your email"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                keyboardType="email-address"
                autoCapitalize="none"
                value={email}
                onChangeText={setEmail}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Password
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="lock"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Enter your password"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={setPassword}
              />
              <Pressable
                onPress={() => setShowPassword(!showPassword)}
                style={styles.passwordToggle}>
                <MaterialIcons
                  name={showPassword ? 'visibility-off' : 'visibility'}
                  size={20}
                  color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                />
              </Pressable>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Confirm Password
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="lock"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Confirm your password"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                secureTextEntry={!showPassword}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
              />
            </View>
          </View>

          <Button
            title="Register"
            variant="primary"
            onPress={handleRegister}
            loading={isLoading}
            style={styles.registerButton}
            fullWidth
          />

          <View style={styles.loginContainer}>
            <Text
              style={[
                styles.loginText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Already have an account?
            </Text>
            <Link href="/login" asChild>
              <Pressable>
                <Text
                  style={[
                    styles.loginLink,
                    { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                  ]}>
                  Login
                </Text>
              </Pressable>
            </Link>
          </View>
        </GlassCard>

        {/* Demo Mode */}
        <View style={styles.demoContainer}>
          <Text
            style={[
              styles.demoText,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Demo Mode: Click Register to continue
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: Spacing.md,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontFamily: Typography.fontFamily.bold,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.regular,
  },
  formCard: {
    padding: Spacing.lg,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
  },
  errorText: {
    marginLeft: Spacing.xs,
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 48,
    fontFamily: Typography.fontFamily.regular,
    fontSize: Typography.fontSize.md,
  },
  passwordToggle: {
    padding: Spacing.xs,
  },
  registerButton: {
    marginBottom: Spacing.md,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.xs,
  },
  loginText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  loginLink: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semiBold,
  },
  demoContainer: {
    marginTop: Spacing.xl,
    alignItems: 'center',
  },
  demoText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.italic,
  },
});
