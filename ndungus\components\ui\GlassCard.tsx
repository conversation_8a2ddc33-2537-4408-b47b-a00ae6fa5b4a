import { BlurView } from 'expo-blur';
import React, { ReactNode } from 'react';
import { StyleSheet, View, ViewStyle, Platform } from 'react-native';

import { BorderRadius, Colors, GlassEffect, Spacing } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type GlassCardProps = {
  children: ReactNode;
  style?: ViewStyle;
  intensity?: number;
  tint?: 'default' | 'light' | 'dark';
  padding?: keyof typeof Spacing | number;
  borderRadius?: keyof typeof BorderRadius | number;
};

/**
 * A card component with a glassmorphism effect
 * Uses BlurView on iOS and a semi-transparent background on Android/Web
 */
export function GlassCard({
  children,
  style,
  intensity = 50,
  tint = 'default',
  padding = 'md',
  borderRadius = 'lg',
}: GlassCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const paddingValue = typeof padding === 'string' ? Spacing[padding] : padding;
  const borderRadiusValue = typeof borderRadius === 'string' ? BorderRadius[borderRadius] : borderRadius;

  // Get the appropriate tint based on the color scheme
  const getTint = () => {
    if (tint !== 'default') return tint;
    return colorScheme === 'dark' ? 'dark' : 'light';
  };

  // Get the glass effect styles based on the color scheme
  const glassStyles = colorScheme === 'dark' ? GlassEffect.dark : GlassEffect.light;

  if (Platform.OS === 'ios') {
    return (
      <View
        style={[
          styles.container,
          {
            borderRadius: borderRadiusValue,
            padding: 0, // We'll apply padding to the BlurView
          },
          style,
        ]}>
        <BlurView
          intensity={intensity}
          tint={getTint()}
          style={[
            styles.blurView,
            {
              borderRadius: borderRadiusValue,
              padding: paddingValue,
              borderColor: glassStyles.borderColor,
              borderWidth: glassStyles.borderWidth,
            },
          ]}>
          {children}
        </BlurView>
      </View>
    );
  }

  // For Android and Web, use a semi-transparent background
  return (
    <View
      style={[
        styles.container,
        {
          borderRadius: borderRadiusValue,
          padding: paddingValue,
          backgroundColor: colorScheme === 'dark' 
            ? 'rgba(31, 41, 55, 0.75)' 
            : 'rgba(255, 255, 255, 0.75)',
          borderColor: glassStyles.borderColor,
          borderWidth: glassStyles.borderWidth,
          shadowColor: glassStyles.shadowColor,
          shadowOpacity: glassStyles.shadowOpacity,
          shadowOffset: glassStyles.shadowOffset,
          shadowRadius: glassStyles.shadowRadius,
          elevation: glassStyles.elevation,
        },
        style,
      ]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    margin: Spacing.sm,
  },
  blurView: {
    overflow: 'hidden',
  },
});
