<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Details - Public Eye</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Additional styles specific to the case details page */
        .case-header {
            background-color: var(--primary-color);
            color: white;
            padding: 3rem 0;
        }

        .case-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .case-meta-info {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .case-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .case-meta-item i {
            font-size: 1.2rem;
        }

        .case-media {
            margin: 2rem 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .case-media img {
            width: 100%;
            max-height: 500px;
            object-fit: cover;
        }

        .case-actions {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .follow-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .follow-btn:hover {
            background-color: #1d4ed8;
        }

        .share-btn {
            background-color: white;
            color: var(--dark-text);
            border: 1px solid #d1d5db;
        }

        .share-btn:hover {
            background-color: #f3f4f6;
        }

        .case-content {
            background-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            padding: 2.5rem;
            margin-bottom: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .case-description {
            font-family: 'Montserrat', sans-serif;
            line-height: 1.8;
            margin-bottom: 2rem;
            font-weight: 400;
            font-size: 1.05rem;
        }

        .case-description p {
            margin-bottom: 1.5rem;
        }

        .timeline-section {
            margin: 3rem 0;
        }

        .timeline {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            position: relative;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 4px;
            background-color: #e5e7eb;
            z-index: 1;
        }

        .timeline-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            width: 16%;
            text-align: center;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            color: var(--medium-text);
            font-size: 1.2rem;
        }

        .step-label {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--medium-text);
        }

        .timeline-step.active .step-icon {
            background-color: var(--primary-color);
            color: white;
        }

        .timeline-step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .timeline-step.completed .step-icon {
            background-color: var(--success-color);
            color: white;
        }

        .contributions-section {
            margin: 3rem 0;
        }

        .contributions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .contribution-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }

        .contributor {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .contributor-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e5e7eb;
            overflow: hidden;
        }

        .contributor-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .contributor-info h4 {
            margin-bottom: 0;
            font-size: 1rem;
        }

        .contributor-info p {
            margin-bottom: 0;
            font-size: 0.8rem;
            color: var(--medium-text);
        }

        .contribution-content {
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .add-contribution-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .add-contribution-btn:hover {
            background-color: #1d4ed8;
        }

        .load-more-btn {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
        }

        .load-more-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .map-section {
            margin: 3rem 0;
        }

        .map-container {
            height: 400px;
            background-color: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin: 2rem 0;
        }

        /* Modal styles for add contribution */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(55, 6, 23, 0.8);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 30%, rgba(208, 0, 0, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(0, 119, 182, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .modal-content {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 20px;
            width: 90%;
            max-width: 650px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 2.5rem;
            position: relative;
            box-shadow: 0 15px 35px rgba(208, 0, 0, 0.25);
            border: 1px solid rgba(208, 0, 0, 0.1);
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--urgency-gradient);
            border-radius: 20px 20px 0 0;
        }

        .modal-content h2 {
            color: var(--dark-text);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            font-weight: 800;
            text-align: center;
            background: var(--urgency-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
        }

        .modal-content h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--hope-gradient);
            border-radius: 3px;
        }

        .close-modal {
            position: absolute;
            top: 1.2rem;
            right: 1.2rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--medium-text);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 10px rgba(208, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .close-modal:hover {
            background-color: rgba(208, 0, 0, 0.1);
            color: var(--primary-color);
            transform: rotate(90deg);
        }

        /* Form styles for the modal */
        #contribution-form .form-group {
            margin-bottom: 1.8rem;
        }

        #contribution-form label {
            display: block;
            margin-bottom: 0.7rem;
            font-weight: 600;
            color: var(--dark-text);
            font-size: 0.95rem;
        }

        #contribution-form select,
        #contribution-form textarea,
        #contribution-form input[type="url"] {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 1px solid rgba(209, 213, 219, 0.5);
            border-radius: 12px;
            font-family: 'Montserrat', sans-serif;
            font-size: 1rem;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        #contribution-form select:focus,
        #contribution-form textarea:focus,
        #contribution-form input[type="url"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        #contribution-form textarea {
            min-height: 120px;
            resize: vertical;
        }

        #contribution-form select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236A040F' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 16px 12px;
            padding-right: 2.5rem;
        }

        #contribution-form label i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .media-upload {
            border: 2px dashed rgba(208, 0, 0, 0.3);
            padding: 2rem;
            text-align: center;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
        }

        .media-upload::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(208, 0, 0, 0.05), rgba(0, 119, 182, 0.05));
            z-index: -1;
        }

        .media-upload:hover {
            border-color: var(--primary-color);
            background-color: rgba(208, 0, 0, 0.03);
        }

        .media-upload i {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .media-upload p {
            margin-bottom: 0.5rem;
            color: var(--dark-text);
            font-weight: 600;
        }

        .media-upload .upload-hint {
            font-size: 0.8rem;
            color: var(--medium-text);
            margin-top: 0.5rem;
        }

        .media-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .form-actions {
            text-align: center;
            margin-top: 2.5rem;
            position: relative;
        }

        .form-actions::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--hope-color));
            opacity: 0.5;
        }

        .submit-btn {
            background: var(--urgency-gradient);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(208, 0, 0, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(208, 0, 0, 0.3);
        }

        .submit-btn i {
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .timeline {
                flex-direction: column;
                align-items: flex-start;
                gap: 1.5rem;
            }

            .timeline::before {
                top: 0;
                bottom: 0;
                left: 24px;
                right: auto;
                width: 4px;
                height: auto;
            }

            .timeline-step {
                flex-direction: row;
                width: 100%;
                text-align: left;
                gap: 1rem;
            }

            .step-label {
                margin-top: 0;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Public Eye</h1>
                <p class="tagline">Justice Remembered, Cases Tracked</p>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="create-case.html">Create Case</a></li>
                    <li><a href="your-cases.html">Your Cases</a></li>
                    <li><a href="#" class="btn-primary">Sign In</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="case-header">
            <div class="container">
                <h1 class="case-title" id="case-title">Loading case...</h1>
                <div class="case-meta-info">
                    <div class="case-meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span id="case-location">Location</span>
                    </div>
                    <div class="case-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span id="case-date">Date Reported</span>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <div class="container">
                <div class="case-media" id="case-media">
                    <!-- Media will be inserted here -->
                </div>

                <div class="case-actions">
                    <button class="action-btn follow-btn" id="follow-btn">
                        <i class="fas fa-eye"></i>
                        <span>Follow Case</span>
                        <span id="follower-count">(0)</span>
                    </button>
                    <button class="action-btn share-btn">
                        <i class="fas fa-share-alt"></i>
                        <span>Share</span>
                    </button>
                </div>

                <div class="case-content">
                    <div class="case-description" id="case-description">
                        <!-- Case description will be inserted here -->
                    </div>
                </div>

                <div class="timeline-section">
                    <h2>Case Progress</h2>
                    <div class="timeline" id="case-timeline">
                        <div class="timeline-step" data-step="report">
                            <div class="step-icon">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="step-label">REPORT</div>
                        </div>
                        <div class="timeline-step" data-step="investigate">
                            <div class="step-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="step-label">INVESTIGATE</div>
                        </div>
                        <div class="timeline-step" data-step="arrest">
                            <div class="step-icon">
                                <i class="fas fa-handcuffs"></i>
                            </div>
                            <div class="step-label">ARREST</div>
                        </div>
                        <div class="timeline-step" data-step="pretrial">
                            <div class="step-icon">
                                <i class="fas fa-gavel"></i>
                            </div>
                            <div class="step-label">PRE-TRIAL</div>
                        </div>
                        <div class="timeline-step" data-step="trial">
                            <div class="step-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="step-label">TRIAL</div>
                        </div>
                        <div class="timeline-step" data-step="verdict">
                            <div class="step-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="step-label">VERDICT</div>
                        </div>
                    </div>
                </div>

                <div class="contributions-section">
                    <div class="section-header">
                        <h2>Contributions & Updates</h2>
                        <button class="add-contribution-btn" id="add-contribution-btn">
                            <i class="fas fa-plus"></i>
                            <span>Add Progress Update</span>
                        </button>
                    </div>

                    <div class="contributions-grid" id="contributions-container">
                        <!-- Contribution cards will be inserted here -->
                    </div>

                    <button class="load-more-btn" id="load-more-contributions">Load More Updates</button>
                </div>

                <div class="map-section">
                    <h2>Location</h2>
                    <div class="map-container" id="case-map">
                        <!-- Map will be inserted here -->
                        <p style="text-align: center; padding-top: 180px; color: #6B7280;">
                            Map loading...
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Add Contribution Modal -->
    <div class="modal" id="contribution-modal">
        <div class="modal-content">
            <span class="close-modal" id="close-modal">&times;</span>
            <h2>Add Progress Update</h2>
            <form id="contribution-form">
                <div class="form-group">
                    <label for="progress-stage"><i class="fas fa-tasks"></i> Progress Stage</label>
                    <select id="progress-stage" name="progress-stage" required>
                        <option value="report">REPORT - Initial Documentation</option>
                        <option value="investigate">INVESTIGATE - Gathering Evidence</option>
                        <option value="arrest">ARREST - Suspect Detained</option>
                        <option value="pretrial">PRE-TRIAL - Legal Proceedings</option>
                        <option value="trial">TRIAL - Court Hearings</option>
                        <option value="verdict">VERDICT - Final Decision</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="evidence-description"><i class="fas fa-file-alt"></i> Evidence Description</label>
                    <textarea id="evidence-description" name="evidence-description" placeholder="Provide details about the evidence or update you're contributing..." required></textarea>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-images"></i> Media (Optional)</label>
                    <div class="media-upload" id="contribution-media-upload">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Click to upload or drag and drop</p>
                        <p class="upload-hint">Supported formats: JPG, PNG, MP4, MOV</p>
                        <input type="file" id="contribution-media-input" multiple accept="image/*,video/*" style="display: none;">
                    </div>
                    <div class="media-preview" id="contribution-media-preview"></div>
                </div>

                <div class="form-group">
                    <label for="evidence-link"><i class="fas fa-link"></i> Source Link (Optional)</label>
                    <input type="url" id="evidence-link" name="evidence-link" placeholder="URL to news article, official document, etc.">
                </div>

                <div class="form-actions">
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i> Submit Update
                    </button>
                </div>
            </form>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Public Eye</h3>
                    <p>Keeping cases visible and justice accountable.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="create-case.html">Create Case</a></li>
                        <li><a href="your-cases.html">Your Cases</a></li>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 Public Eye. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/cases.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get case ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const caseId = urlParams.get('id');

            if (!caseId) {
                // No case ID provided, redirect to home
                window.location.href = 'index.html';
                return;
            }

            // Load case details
            loadCaseDetails(caseId);

            // Set up modal functionality
            const modal = document.getElementById('contribution-modal');
            const addContributionBtn = document.getElementById('add-contribution-btn');
            const closeModal = document.getElementById('close-modal');

            addContributionBtn.addEventListener('click', function() {
                modal.style.display = 'flex';
            });

            closeModal.addEventListener('click', function() {
                modal.style.display = 'none';
            });

            window.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });

            // Set up contribution form
            const contributionForm = document.getElementById('contribution-form');

            contributionForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // In a real application, this would send the data to a server
                // For now, we'll just show a success message
                modal.style.display = 'none';
                alert('Thank you for your contribution! It will be reviewed before being published.');
            });

            // Follow button functionality
            const followBtn = document.getElementById('follow-btn');
            let isFollowing = false;

            followBtn.addEventListener('click', function() {
                isFollowing = !isFollowing;

                if (isFollowing) {
                    followBtn.innerHTML = '<i class="fas fa-eye-slash"></i><span>Unfollow</span><span id="follower-count"></span>';
                    alert('You are now following this case. You will receive updates when there is progress.');
                } else {
                    followBtn.innerHTML = '<i class="fas fa-eye"></i><span>Follow Case</span><span id="follower-count"></span>';
                }

                // Update follower count display
                document.getElementById('follower-count').textContent = ` (${currentCase.followers + (isFollowing ? 1 : 0)})`;
            });
        });

        // Global variable to store current case data
        let currentCase;

        // Load case details
        function loadCaseDetails(caseId) {
            // In a real application, this would be an API call
            // For now, we'll use the sample data
            const allCases = [
                ...getSampleCases('popular'),
                ...getSampleCases('recent'),
                ...getSampleCases('followed'),
                ...getSampleCases('more')
            ];

            currentCase = allCases.find(c => c.id == caseId);

            if (!currentCase) {
                // Case not found
                window.location.href = 'index.html';
                return;
            }

            // Update page with case details
            document.getElementById('case-title').textContent = currentCase.title;
            document.getElementById('case-location').textContent = currentCase.location;
            document.getElementById('case-date').textContent = formatDate(currentCase.dateReported);
            document.getElementById('follower-count').textContent = ` (${currentCase.followers})`;

            // Set case media
            const mediaContainer = document.getElementById('case-media');
            mediaContainer.innerHTML = `<img src="${currentCase.image}" alt="${currentCase.title}">`;

            // Set case description
            const descriptionContainer = document.getElementById('case-description');
            descriptionContainer.innerHTML = `<p>${currentCase.description}</p>`;

            // Update timeline
            updateTimeline(currentCase.status);

            // Load contributions
            loadContributions(currentCase);

            // Update page title
            document.title = `${currentCase.title} - Public Eye`;
        }

        // Update timeline based on current status
        function updateTimeline(currentStatus) {
            const timelineSteps = document.querySelectorAll('.timeline-step');
            const statusOrder = ['report', 'investigate', 'arrest', 'pretrial', 'trial', 'verdict'];
            const currentIndex = statusOrder.indexOf(currentStatus);

            timelineSteps.forEach((step, index) => {
                const stepStatus = step.getAttribute('data-step');
                const stepIndex = statusOrder.indexOf(stepStatus);

                if (stepIndex < currentIndex) {
                    // Completed steps
                    step.classList.add('completed');
                } else if (stepIndex === currentIndex) {
                    // Current step
                    step.classList.add('active');
                }
            });
        }

        // Load contributions for a case
        function loadContributions(caseData) {
            const contributionsContainer = document.getElementById('contributions-container');
            contributionsContainer.innerHTML = '';

            // Sample contributor data
            const contributors = [
                { name: 'Alex Johnson', avatar: 'https://randomuser.me/api/portraits/men/32.jpg', date: '2023-09-15', content: 'I\'ve obtained the official police report regarding this case. It confirms that an investigation is actively ongoing, with multiple witnesses being interviewed.' },
                { name: 'Maria Rodriguez', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', date: '2023-09-02', content: 'Local news covered this story last week. They interviewed several affected community members who provided additional evidence of the environmental impact.' }
            ];

            // Create contribution cards
            contributors.forEach(contributor => {
                const card = document.createElement('div');
                card.className = 'contribution-card';

                const contributorEl = document.createElement('div');
                contributorEl.className = 'contributor';

                const avatar = document.createElement('div');
                avatar.className = 'contributor-avatar';
                avatar.innerHTML = `<img src="${contributor.avatar}" alt="${contributor.name}">`;

                const info = document.createElement('div');
                info.className = 'contributor-info';
                info.innerHTML = `
                    <h4>${contributor.name}</h4>
                    <p>${formatDate(contributor.date)}</p>
                `;

                contributorEl.appendChild(avatar);
                contributorEl.appendChild(info);

                const content = document.createElement('div');
                content.className = 'contribution-content';
                content.textContent = contributor.content;

                card.appendChild(contributorEl);
                card.appendChild(content);

                contributionsContainer.appendChild(card);
            });
        }

        // Format date for display
        function formatDate(dateString) {
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            return new Date(dateString).toLocaleDateString(undefined, options);
        }
    </script>
</body>
</html>
