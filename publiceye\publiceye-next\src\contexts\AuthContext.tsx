import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  getAuth,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth';
import { getDatabase, ref, set, get } from 'firebase/database';
import { app } from '@/lib/firebase';
import { User } from '@/types/user';
import axios from 'axios';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<User | null>;
  signUp: (email: string, password: string, displayName: string) => Promise<User | null>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const auth = getAuth(app);
  const db = getDatabase(app);

  // Convert Firebase user to our User type
  const formatUser = async (firebaseUser: FirebaseUser): Promise<User> => {
    try {
      // Get additional user data from database
      console.log('Fetching user data for:', firebaseUser.uid);
      const userRef = ref(db, `users/${firebaseUser.uid}`);
      const snapshot = await get(userRef);

      // If we can access the data, use it
      if (snapshot.exists()) {
        console.log('User data found in database');
        const userData = snapshot.val();
        return {
          id: firebaseUser.uid,
          email: firebaseUser.email || '',
          displayName: firebaseUser.displayName || '',
          photoURL: firebaseUser.photoURL || '',
          role: userData.role || 'user',
          followedCases: userData.followedCases || {},
          contributedCases: userData.contributedCases || {},
          createdCases: userData.createdCases || {},
        };
      } else {
        console.log('No user data found in database, using defaults');
      }
    } catch (err) {
      // If there's a permission error, just use the basic user info
      console.error('Error fetching user data:', err);
      console.log('Using basic user info due to database permission error');
    }

    // Fallback to basic user info from Firebase Auth
    return {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      displayName: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || '',
      role: 'user',
      followedCases: {},
      contributedCases: {},
      createdCases: {},
    };
  };

  // Listen for auth state changes
  useEffect(() => {
    console.log('Setting up auth state listener');
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser ? `User: ${firebaseUser.email}` : 'No user');
      setLoading(true);
      try {
        if (firebaseUser) {
          const formattedUser = await formatUser(firebaseUser);
          console.log('Formatted user:', formattedUser);
          setUser(formattedUser);
        } else {
          setUser(null);
        }
      } catch (err) {
        console.error('Error in auth state change:', err);
        setError('Failed to authenticate');
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [auth]);

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<User | null> => {
    setLoading(true);
    setError(null);

    try {
      console.log('Attempting to sign in with email:', email);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log('Sign in successful, user:', userCredential.user);
      const formattedUser = await formatUser(userCredential.user);
      console.log('Formatted user after sign in:', formattedUser);
      setUser(formattedUser);
      return formattedUser;
    } catch (err: any) {
      console.error('Sign in error:', err);
      if (err.code === 'auth/user-not-found' || err.code === 'auth/wrong-password') {
        setError('Invalid email or password');
      } else if (err.code === 'auth/too-many-requests') {
        setError('Too many failed login attempts. Please try again later.');
      } else {
        setError('Failed to sign in: ' + (err.message || err.toString()));
      }
      throw err; // Rethrow the error so it can be caught in the component
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, displayName: string): Promise<User | null> => {
    setLoading(true);
    setError(null);

    try {
      console.log('Attempting to sign up with email:', email);
      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      console.log('User created successfully:', firebaseUser);

      // Update profile with display name
      await updateProfile(firebaseUser, { displayName });
      console.log('Profile updated with display name:', displayName);

      // Create user profile in database
      const userRef = ref(db, `users/${firebaseUser.uid}`);
      await set(userRef, {
        email,
        displayName,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        role: 'user',
        followedCases: {},
        contributedCases: {},
        createdCases: {},
      });
      console.log('User profile created in database');

      const formattedUser = await formatUser(firebaseUser);
      console.log('Formatted user after sign up:', formattedUser);
      setUser(formattedUser);
      return formattedUser;
    } catch (err: any) {
      console.error('Sign up error:', err);
      if (err.code === 'auth/email-already-in-use') {
        setError('Email already in use');
      } else if (err.code === 'auth/invalid-email') {
        setError('Invalid email format');
      } else if (err.code === 'auth/weak-password') {
        setError('Password is too weak');
      } else {
        setError('Failed to sign up: ' + (err.message || err.toString()));
      }
      throw err; // Rethrow the error so it can be caught in the component
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<void> => {
    setLoading(true);
    try {
      await firebaseSignOut(auth);
      setUser(null);
    } catch (err) {
      console.error('Sign out error:', err);
      setError('Failed to sign out');
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  const value = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
