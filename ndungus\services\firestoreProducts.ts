/**
 * Firestore Product service for managing inventory items
 */

import { v4 as uuidv4 } from 'uuid';

import { calculateProductStatus, CreateProductInput, Product, UpdateProductInput } from '@/models/Product';
import { COLLECTIONS, addDocument, deleteDocument, getDocumentById, getDocuments, updateDocument } from '@/services/firebase';
import { FOLDERS } from '@/services/cloudinary';
import { uploadImageToCloudinary } from '@/services/imageUpload';
import { getObject, setObject, StorageKey } from '@/services/storage';

/**
 * Get all products from Firestore
 * @returns Array of products
 */
export async function getFirestoreProducts(): Promise<Product[]> {
  try {
    // Get products from Firestore
    const products = await getDocuments<Product>(COLLECTIONS.PRODUCTS);
    
    // Also save to local storage for offline access
    if (products.length > 0) {
      await setObject(StorageKey.INVENTORY, products);
    }
    
    return products;
  } catch (error) {
    console.error('Error getting products from Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return getLocalProducts();
  }
}

/**
 * Get all products from local storage
 * @returns Array of products
 */
export async function getLocalProducts(): Promise<Product[]> {
  try {
    // Get products from storage
    const products = await getObject<Product[]>(StorageKey.INVENTORY);
    
    // If no products found, initialize with empty array
    if (!products) {
      await setObject(StorageKey.INVENTORY, []);
      return [];
    }
    
    return products;
  } catch (error) {
    console.error('Error getting products from local storage:', error);
    return [];
  }
}

/**
 * Get a product by ID from Firestore
 * @param id Product ID
 * @returns Product or null if not found
 */
export async function getFirestoreProductById(id: string): Promise<Product | null> {
  try {
    // Get product from Firestore
    const product = await getDocumentById<Product>(COLLECTIONS.PRODUCTS, id);
    return product;
  } catch (error) {
    console.error('Error getting product by ID from Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return getLocalProductById(id);
  }
}

/**
 * Get a product by ID from local storage
 * @param id Product ID
 * @returns Product or null if not found
 */
export async function getLocalProductById(id: string): Promise<Product | null> {
  try {
    const products = await getLocalProducts();
    return products.find(product => product.id === id) || null;
  } catch (error) {
    console.error('Error getting product by ID from local storage:', error);
    return null;
  }
}

/**
 * Create a new product in Firestore
 * @param input Product data
 * @param imageUri Optional image URI to upload
 * @returns Created product
 */
export async function createFirestoreProduct(input: CreateProductInput, imageUri?: string): Promise<Product> {
  try {
    // Upload image to Cloudinary if provided
    let imageUrl = input.image;
    
    if (imageUri) {
      const uploadResult = await uploadImageToCloudinary(imageUri, FOLDERS.PRODUCTS);
      imageUrl = uploadResult.secure_url;
    }
    
    // Create new product
    const newProduct: Product = {
      id: uuidv4(), // Generate a unique ID
      ...input,
      image: imageUrl,
      status: calculateProductStatus(input.units),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to Firestore
    await addDocument<Product>(COLLECTIONS.PRODUCTS, newProduct);
    
    // Also save to local storage for offline access
    await createLocalProduct(newProduct);
    
    return newProduct;
  } catch (error) {
    console.error('Error creating product in Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return createLocalProduct(input, imageUri);
  }
}

/**
 * Create a new product in local storage
 * @param input Product data or complete product object
 * @param imageUri Optional image URI to upload
 * @returns Created product
 */
export async function createLocalProduct(
  input: CreateProductInput | Product,
  imageUri?: string
): Promise<Product> {
  try {
    const products = await getLocalProducts();
    
    // If input is already a complete Product object, use it
    if ('id' in input && 'status' in input && 'createdAt' in input) {
      const newProduct = input as Product;
      
      // Add to products array
      const updatedProducts = [...products, newProduct];
      
      // Save to storage
      await setObject(StorageKey.INVENTORY, updatedProducts);
      
      return newProduct;
    }
    
    // Otherwise, create a new product from input
    const createInput = input as CreateProductInput;
    
    // Upload image if provided
    let imageUrl = createInput.image;
    
    if (imageUri) {
      const uploadResult = await uploadImageToCloudinary(imageUri, FOLDERS.PRODUCTS);
      imageUrl = uploadResult.secure_url;
    }
    
    // Create new product
    const newProduct: Product = {
      id: uuidv4(),
      ...createInput,
      image: imageUrl,
      status: calculateProductStatus(createInput.units),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to products array
    const updatedProducts = [...products, newProduct];
    
    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);
    
    return newProduct;
  } catch (error) {
    console.error('Error creating product in local storage:', error);
    throw new Error('Failed to create product');
  }
}

/**
 * Update a product in Firestore
 * @param input Product data to update
 * @param imageUri Optional image URI to upload
 * @returns Updated product
 */
export async function updateFirestoreProduct(
  input: UpdateProductInput,
  imageUri?: string
): Promise<Product | null> {
  try {
    // Get existing product
    const existingProduct = await getFirestoreProductById(input.id);
    
    if (!existingProduct) {
      return null;
    }
    
    // Upload image if provided
    let imageUrl = input.image !== undefined ? input.image : existingProduct.image;
    
    if (imageUri) {
      const uploadResult = await uploadImageToCloudinary(imageUri, FOLDERS.PRODUCTS);
      imageUrl = uploadResult.secure_url;
    }
    
    // Calculate status if units are updated
    const status = input.units !== undefined
      ? calculateProductStatus(input.units)
      : existingProduct.status;
    
    // Create updated product
    const updatedProduct: Product = {
      ...existingProduct,
      ...input,
      image: imageUrl,
      status,
      updatedAt: new Date().toISOString(),
    };
    
    // Update in Firestore
    await updateDocument<Product>(COLLECTIONS.PRODUCTS, input.id, updatedProduct);
    
    // Also update in local storage for offline access
    await updateLocalProduct(updatedProduct);
    
    return updatedProduct;
  } catch (error) {
    console.error('Error updating product in Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return updateLocalProduct(input, imageUri);
  }
}

/**
 * Update a product in local storage
 * @param input Product data to update or complete product object
 * @param imageUri Optional image URI to upload
 * @returns Updated product
 */
export async function updateLocalProduct(
  input: UpdateProductInput | Product,
  imageUri?: string
): Promise<Product | null> {
  try {
    const products = await getLocalProducts();
    const productIndex = products.findIndex(product => product.id === input.id);
    
    if (productIndex === -1) {
      return null;
    }
    
    const existingProduct = products[productIndex];
    
    // If input is already a complete Product object, use it
    if ('status' in input && 'createdAt' in input) {
      const updatedProduct = input as Product;
      
      // Update products array
      const updatedProducts = [...products];
      updatedProducts[productIndex] = updatedProduct;
      
      // Save to storage
      await setObject(StorageKey.INVENTORY, updatedProducts);
      
      return updatedProduct;
    }
    
    // Otherwise, update from input
    const updateInput = input as UpdateProductInput;
    
    // Upload image if provided
    let imageUrl = updateInput.image !== undefined ? updateInput.image : existingProduct.image;
    
    if (imageUri) {
      const uploadResult = await uploadImageToCloudinary(imageUri, FOLDERS.PRODUCTS);
      imageUrl = uploadResult.secure_url;
    }
    
    // Calculate status if units are updated
    const status = updateInput.units !== undefined
      ? calculateProductStatus(updateInput.units)
      : existingProduct.status;
    
    // Create updated product
    const updatedProduct: Product = {
      ...existingProduct,
      ...updateInput,
      image: imageUrl,
      status,
      updatedAt: new Date().toISOString(),
    };
    
    // Update products array
    const updatedProducts = [...products];
    updatedProducts[productIndex] = updatedProduct;
    
    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);
    
    return updatedProduct;
  } catch (error) {
    console.error('Error updating product in local storage:', error);
    return null;
  }
}

/**
 * Delete a product from Firestore
 * @param id Product ID
 * @returns True if deleted, false otherwise
 */
export async function deleteFirestoreProduct(id: string): Promise<boolean> {
  try {
    // Delete from Firestore
    const success = await deleteDocument(COLLECTIONS.PRODUCTS, id);
    
    if (success) {
      // Also delete from local storage
      await deleteLocalProduct(id);
    }
    
    return success;
  } catch (error) {
    console.error('Error deleting product from Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return deleteLocalProduct(id);
  }
}

/**
 * Delete a product from local storage
 * @param id Product ID
 * @returns True if deleted, false otherwise
 */
export async function deleteLocalProduct(id: string): Promise<boolean> {
  try {
    const products = await getLocalProducts();
    
    // Filter out the product to delete
    const updatedProducts = products.filter(product => product.id !== id);
    
    // If no products were removed, product wasn't found
    if (updatedProducts.length === products.length) {
      return false;
    }
    
    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);
    
    return true;
  } catch (error) {
    console.error('Error deleting product from local storage:', error);
    return false;
  }
}
