'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FaFlag, FaSearch, FaHandcuffs, FaGavel, FaBalanceScale, FaCheckCircle, FaUpload } from 'react-icons/fa';

// Define the CaseStage type
type CaseStage = 'report' | 'investigate' | 'arrest' | 'pretrial' | 'trial' | 'verdict';

export default function CreateCase() {
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [sourceUrl, setSourceUrl] = useState('');
  const [currentStage, setCurrentStage] = useState<CaseStage>('report');
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [mediaPreviewUrls, setMediaPreviewUrls] = useState<string[]>([]);
  
  const router = useRouter();
  
  // Handle media file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setMediaFiles([...mediaFiles, ...filesArray]);
      
      // Create preview URLs
      const newPreviewUrls = filesArray.map(file => URL.createObjectURL(file));
      setMediaPreviewUrls([...mediaPreviewUrls, ...newPreviewUrls]);
    }
  };
  
  // Remove a media file
  const removeMedia = (index: number) => {
    const newFiles = [...mediaFiles];
    newFiles.splice(index, 1);
    setMediaFiles(newFiles);
    
    const newPreviewUrls = [...mediaPreviewUrls];
    URL.revokeObjectURL(newPreviewUrls[index]);
    newPreviewUrls.splice(index, 1);
    setMediaPreviewUrls(newPreviewUrls);
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert(`Case created with title: ${title}, stage: ${currentStage}, media files: ${mediaFiles.length}, source URL: ${sourceUrl || 'none'}`);
    
    // Clean up preview URLs
    mediaPreviewUrls.forEach(url => URL.revokeObjectURL(url));
    
    // In a real implementation, we would save the case to the database
    router.push('/');
  };

  return (
    <main>
      <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">Create a New Case</h1>
          <p className="text-xl opacity-90">Document a case that needs public attention and tracking.</p>
        </div>
      </section>
      
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-md p-8 max-w-3xl mx-auto">
          <form onSubmit={handleSubmit}>
            {/* Case Title */}
            <div className="mb-6">
              <label htmlFor="title" className="block text-gray-700 font-semibold mb-2">
                Case Title
              </label>
              <input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Enter a clear, descriptive title"
                required
              />
            </div>
            
            {/* Location */}
            <div className="mb-6">
              <label htmlFor="location" className="block text-gray-700 font-semibold mb-2">
                Location
              </label>
              <input
                id="location"
                type="text"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="City, State, Country"
                required
              />
            </div>
            
            {/* Media Upload */}
            <div className="mb-6">
              <label className="block text-gray-700 font-semibold mb-2">
                Media (Photos/Videos)
              </label>
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-red-500 hover:bg-red-50 transition-colors"
                onClick={() => document.getElementById('media-input')?.click()}
              >
                <input
                  type="file"
                  id="media-input"
                  multiple
                  accept="image/*,video/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <FaUpload className="mx-auto text-5xl text-red-600 mb-4" />
                <p className="text-gray-700 font-medium">Click to upload or drag and drop</p>
                <p className="text-gray-500 text-sm mt-2">
                  Supported formats: JPG, PNG, MP4, MOV (max 10MB each)
                </p>
              </div>
              
              {/* Media Previews */}
              {mediaPreviewUrls.length > 0 && (
                <div className="grid grid-cols-4 gap-4 mt-4">
                  {mediaPreviewUrls.map((url, index) => (
                    <div key={index} className="relative h-24 rounded-md overflow-hidden">
                      <img 
                        src={url} 
                        alt={`Preview ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => removeMedia(index)}
                        className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Source URL */}
            <div className="mb-6">
              <label htmlFor="sourceUrl" className="block text-gray-700 font-semibold mb-2">
                Related URL
              </label>
              <input
                id="sourceUrl"
                type="url"
                value={sourceUrl}
                onChange={(e) => setSourceUrl(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="News article, official report, or other relevant link"
              />
            </div>
            
            {/* Case Description */}
            <div className="mb-6">
              <label htmlFor="description" className="block text-gray-700 font-semibold mb-2">
                Case Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={6}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Provide a detailed account of the case..."
                required
              ></textarea>
            </div>
            
            {/* Current Progress Stage */}
            <div className="mb-8">
              <label className="block text-gray-700 font-semibold mb-3">
                Current Progress Stage
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'report' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('report')}
                >
                  <FaFlag className="mx-auto text-2xl text-red-600 mb-2" />
                  <p className="font-medium">REPORT</p>
                </div>
                
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'investigate' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('investigate')}
                >
                  <FaSearch className="mx-auto text-2xl text-orange-500 mb-2" />
                  <p className="font-medium">INVESTIGATE</p>
                </div>
                
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'arrest' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('arrest')}
                >
                  <FaHandcuffs className="mx-auto text-2xl text-yellow-500 mb-2" />
                  <p className="font-medium">ARREST</p>
                </div>
                
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'pretrial' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('pretrial')}
                >
                  <FaGavel className="mx-auto text-2xl text-blue-500 mb-2" />
                  <p className="font-medium">PRE-TRIAL</p>
                </div>
                
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'trial' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('trial')}
                >
                  <FaBalanceScale className="mx-auto text-2xl text-indigo-600 mb-2" />
                  <p className="font-medium">TRIAL</p>
                </div>
                
                <div 
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === 'verdict' 
                      ? 'border-red-500 bg-red-50' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage('verdict')}
                >
                  <FaCheckCircle className="mx-auto text-2xl text-green-600 mb-2" />
                  <p className="font-medium">VERDICT</p>
                </div>
              </div>
            </div>
            
            {/* Submit Button */}
            <div className="text-center">
              <button
                type="submit"
                className="px-8 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium hover:shadow-lg transition-all"
              >
                Submit Case
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
}
