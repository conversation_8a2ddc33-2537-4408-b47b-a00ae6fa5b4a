import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';

// Initialize Firebase Admin on the server side
getServerFirebase();
const db = getAdminDatabase();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const limitCount = parseInt(req.query.limit as string) || 10;

    const casesRef = db.ref('cases');
    // Order by createdAt and limit to the last N items
    const snapshot = await casesRef.orderByChild('createdAt').limitToLast(limitCount).once('value');
    const cases = [];

    if (snapshot.exists()) {
      const casesData = snapshot.val();

      // Convert object to array with IDs
      for (const key in casesData) {
        cases.push({
          id: key,
          ...casesData[key]
        });
      }

      // Reverse to get descending order (newest first)
      cases.reverse();

      return res.status(200).json(cases);
    }

    return res.status(200).json([]);
  } catch (error) {
    console.error('Error fetching recent cases:', error);
    return res.status(500).json({ error: 'Failed to fetch recent cases' });
  }
}
