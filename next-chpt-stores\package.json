{"name": "next-chpt-stores", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "clean": "rm -rf .next"}, "dependencies": {"@google/generative-ai": "^0.24.1", "mongoose": "^8.14.2", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}