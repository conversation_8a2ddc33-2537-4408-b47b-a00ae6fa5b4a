import { NextRequest, NextResponse } from 'next/server';
import { getBudgetRecommendations } from '@/utils/aiService';

/**
 * POST /api/recommendations
 * Get budget recommendations for products
 */
export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.budget || !body.products || !Array.isArray(body.products)) {
      return NextResponse.json(
        { error: 'Missing required fields: budget and products array' },
        { status: 400 }
      );
    }
    
    // Get recommendations
    const recommendations = await getBudgetRecommendations({
      budget: body.budget,
      lifeChapter: body.lifeChapter,
      category: body.category,
      products: body.products
    });
    
    return NextResponse.json(recommendations);
  } catch (error) {
    console.error('Error getting recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to get recommendations' },
      { status: 500 }
    );
  }
}
