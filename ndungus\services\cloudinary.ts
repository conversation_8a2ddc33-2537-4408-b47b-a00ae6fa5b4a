/**
 * Cloudinary service for image uploads and management
 */

import { CLOUDINARY_CONFIG } from '@/services/config';

// Folder structure in Cloudinary
export const FOLDERS = {
  PRODUCTS: 'ndungus_shop/products',
  USERS: 'ndungus_shop/users',
  RECEIPTS: 'ndungus_shop/receipts',
};

/**
 * Upload an image to Cloudinary using the upload preset
 * @param imageUri Local image URI
 * @param folder Folder to upload to
 * @returns Cloudinary upload result with secure URL
 */
export async function uploadImage(
  imageUri: string,
  folder: string = FOLDERS.PRODUCTS
): Promise<{ secure_url: string }> {
  try {
    // Create form data for upload
    const formData = new FormData();

    // Get file name from URI
    const uriParts = imageUri.split('/');
    const fileName = uriParts[uriParts.length - 1];

    // Add file to form data
    // @ts-ignore
    formData.append('file', {
      uri: imageUri,
      name: fileName,
      type: 'image/jpeg', // Assuming JPEG format
    });

    // Add upload preset (from Cloudinary settings)
    formData.append('upload_preset', CLOUDINARY_CONFIG.UPLOAD_PRESET);

    // Add folder
    formData.append('folder', folder);

    // Upload to Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CONFIG.CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    // Parse response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to upload image');
    }

    // Return secure URL
    return {
      secure_url: data.secure_url,
    };
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);
    throw error;
  }
}

/**
 * Delete an image from Cloudinary
 * @param publicId Public ID of the image to delete
 * @returns Success status
 */
export async function deleteImage(publicId: string): Promise<boolean> {
  try {
    // Create form data for deletion
    const formData = new FormData();

    // Add public ID
    formData.append('public_id', publicId);

    // Add API key and timestamp
    formData.append('api_key', CLOUDINARY_CONFIG.API_KEY);
    const timestamp = Math.floor(Date.now() / 1000);
    formData.append('timestamp', timestamp.toString());

    // Add upload preset
    formData.append('upload_preset', CLOUDINARY_CONFIG.UPLOAD_PRESET);

    // Delete from Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CONFIG.CLOUD_NAME}/image/destroy`,
      {
        method: 'POST',
        body: formData,
      }
    );

    // Parse response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to delete image');
    }

    // Return success status
    return data.result === 'ok';
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    return false;
  }
}

/**
 * Extract public ID from Cloudinary URL
 * @param url Cloudinary URL
 * @returns Public ID
 */
export function getPublicIdFromUrl(url: string): string {
  try {
    // Parse URL
    const urlObj = new URL(url);

    // Get path without extension
    const pathWithoutExt = urlObj.pathname.split('.')[0];

    // Remove /image/upload/ prefix
    const publicId = pathWithoutExt.replace(/^\/[^\/]+\/[^\/]+\//, '');

    return publicId;
  } catch (error) {
    console.error('Error extracting public ID from URL:', error);
    return '';
  }
}

/**
 * Get a transformed image URL
 * @param url Original Cloudinary URL
 * @param width Image width
 * @param height Image height
 * @returns Transformed image URL
 */
export function getTransformedImageUrl(url: string, width: number = 500, height: number = 500): string {
  try {
    // Check if it's a Cloudinary URL
    if (!url.includes('cloudinary.com')) {
      return url;
    }

    // Parse URL
    const urlObj = new URL(url);

    // Get path parts
    const pathParts = urlObj.pathname.split('/');

    // Find the upload part index
    const uploadIndex = pathParts.findIndex(part => part === 'upload');

    if (uploadIndex === -1) {
      return url;
    }

    // Insert transformation
    pathParts.splice(uploadIndex + 1, 0, `w_${width},h_${height},c_fill,g_auto`);

    // Reconstruct URL
    urlObj.pathname = pathParts.join('/');

    return urlObj.toString();
  } catch (error) {
    console.error('Error transforming image URL:', error);
    return url;
  }
}

