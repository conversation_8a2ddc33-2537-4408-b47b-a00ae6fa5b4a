import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    Pressable,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TextInput,
    View,
} from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { CLOUDINARY_CONFIG, FIREBASE_CONFIG } from '@/services/config';
import { getObject, setObject, StorageKey } from '@/services/storage';
import { syncAllFirestoreData } from '@/services/sync';

// Database settings interface
interface DatabaseSettings {
  firebaseProjectId: string;
  cloudinaryName: string;
  cloudinaryKey: string;
  cloudinarySecret: string;
  useFirebase: boolean;
  useCloudinary: boolean;
}

// Default settings
const DEFAULT_SETTINGS: DatabaseSettings = {
  firebaseProjectId: 'ndungu-shop',
  cloudinaryName: 'Untitled',
  cloudinaryKey: '592135841396749',
  cloudinarySecret: 'GWV3zVxrV_h8C6DBryBtOFyS93Y',
  useFirebase: true,
  useCloudinary: true,
};

export default function DatabaseSettingsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Form state
  const [settings, setSettings] = useState<DatabaseSettings>(DEFAULT_SETTINGS);

  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await getObject<DatabaseSettings>(StorageKey.DATABASE_SETTINGS);

        if (savedSettings) {
          setSettings(savedSettings);
        }
      } catch (error) {
        console.error('Error loading database settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Handle form changes
  const handleChange = (key: keyof DatabaseSettings, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Test Firebase connection
  const testFirebaseConnection = async () => {
    setIsTesting(true);

    try {
      // Import Firebase functions
      const { getDocuments, COLLECTIONS } = await import('@/services/firebase');

      // Test connection by fetching a small amount of data
      await getDocuments(COLLECTIONS.PRODUCTS);

      Alert.alert('Success', 'Successfully connected to Firebase');
    } catch (error: any) {
      Alert.alert('Connection Error', error.message || 'Failed to connect to Firebase');
    } finally {
      setIsTesting(false);
    }
  };

  // Sync data with Firestore
  const handleSyncData = async () => {
    setIsSyncing(true);

    try {
      // Sync data
      const result = await syncAllFirestoreData();

      Alert.alert(
        'Sync Complete',
        `Synced ${result.products} products, ${result.sales} sales, and ${result.operations} operations`
      );
    } catch (error: any) {
      Alert.alert('Sync Error', error.message || 'Failed to sync data with Firestore');
    } finally {
      setIsSyncing(false);
    }
  };

  // Save settings
  const handleSave = async () => {
    setIsLoading(true);

    try {
      // Save settings
      await setObject(StorageKey.DATABASE_SETTINGS, settings);

      // Update Cloudinary config
      CLOUDINARY_CONFIG.CLOUD_NAME = settings.cloudinaryName;
      CLOUDINARY_CONFIG.API_KEY = settings.cloudinaryKey;
      CLOUDINARY_CONFIG.API_SECRET = settings.cloudinarySecret;

      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <GlassCard style={styles.header}>
          <View style={styles.headerContent}>
            <Pressable onPress={() => router.back()} style={styles.backButton}>
              <MaterialIcons
                name="arrow-back"
                size={24}
                color={colorScheme === 'dark' ? Colors.white : Colors.black}
              />
            </Pressable>
            <Text
              style={[
                styles.title,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Database Settings
            </Text>
          </View>
        </GlassCard>

        {/* Firebase Settings */}
        <GlassCard style={styles.formCard}>
          <View style={styles.sectionHeader}>
            <Text
              style={[
                styles.sectionTitle,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Firebase Settings
            </Text>
            <View style={styles.switchContainer}>
              <Text
                style={[
                  styles.switchLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Use Firebase
              </Text>
              <Switch
                value={settings.useFirebase}
                onValueChange={(value) => handleChange('useFirebase', value)}
                trackColor={{ false: Colors.gray400, true: Colors.primary }}
                thumbColor={Colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Project ID
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="your-firebase-project-id"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.firebaseProjectId}
              onChangeText={(value) => handleChange('firebaseProjectId', value)}
              autoCapitalize="none"
            />
          </View>

          <View style={styles.buttonRow}>
            <Button
              title="Test Connection"
              variant="secondary"
              size="sm"
              isLoading={isTesting}
              onPress={testFirebaseConnection}
              style={{ flex: 1, marginRight: Spacing.sm }}
            />
            <Button
              title="Sync Data"
              variant="primary"
              size="sm"
              isLoading={isSyncing}
              onPress={handleSyncData}
              style={{ flex: 1 }}
            />
          </View>
        </GlassCard>

        {/* Cloudinary Settings */}
        <GlassCard style={styles.formCard}>
          <View style={styles.sectionHeader}>
            <Text
              style={[
                styles.sectionTitle,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Cloudinary Settings
            </Text>
            <View style={styles.switchContainer}>
              <Text
                style={[
                  styles.switchLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Use Cloudinary
              </Text>
              <Switch
                value={settings.useCloudinary}
                onValueChange={(value) => handleChange('useCloudinary', value)}
                trackColor={{ false: Colors.gray400, true: Colors.primary }}
                thumbColor={Colors.white}
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Cloud Name
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="your_cloud_name"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.cloudinaryName}
              onChangeText={(value) => handleChange('cloudinaryName', value)}
              autoCapitalize="none"
            />
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              API Key
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="your_api_key"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.cloudinaryKey}
              onChangeText={(value) => handleChange('cloudinaryKey', value)}
              autoCapitalize="none"
              secureTextEntry
            />
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              API Secret
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="your_api_secret"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.cloudinarySecret}
              onChangeText={(value) => handleChange('cloudinarySecret', value)}
              autoCapitalize="none"
              secureTextEntry
            />
          </View>
        </GlassCard>

        {/* Save Button */}
        <Button
          title="Save Settings"
          variant="primary"
          isLoading={isLoading}
          onPress={handleSave}
          style={styles.saveButton}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  formCard: {
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginRight: Spacing.sm,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  input: {
    height: 48,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.sm,
  },
  saveButton: {
    marginTop: Spacing.sm,
  },
});
