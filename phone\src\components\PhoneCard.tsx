import Image from 'next/image';
import Link from 'next/link';
import { Phone } from '@/types';

interface PhoneCardProps {
  phone: Phone;
}

export default function PhoneCard({ phone }: PhoneCardProps) {
  const handleWhatsAppClick = () => {
    const message = `I am interested in ${phone.name}. Is it still available?`;
    const whatsappUrl = `https://wa.me/+254XXXXXXXXX?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <Link href={`/phones/${phone.id}`}>
        <div className="relative h-48 w-full">
          <Image
            src={phone.image}
            alt={phone.name}
            fill
            className="object-cover"
          />
        </div>
      </Link>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900">{phone.name}</h3>
        <p className="text-sm text-gray-600">{phone.storage}</p>
        <p className="text-lg font-bold text-gray-900 mt-2">
          KES {phone.price.toLocaleString()}
        </p>
        <button
          onClick={handleWhatsAppClick}
          className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
        >
          Buy Now
        </button>
      </div>
    </div>
  );
} 