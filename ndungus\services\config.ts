/**
 * Application configuration
 * Contains environment variables and configuration settings
 */

// Import environment variables
import Constants from 'expo-constants';

// Get environment variables from Expo Constants
const expoConstants = Constants.expoConfig?.extra || {};

// Firebase configuration
export const FIREBASE_CONFIG = {
  // Firebase config from environment variables
  API_KEY: process.env.FIREBASE_API_KEY || 'AIzaSyDGf56DgK9zZeYr9fHrNhgF4pytP9oRCuQ',
  AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN || 'ndungu-shop.firebaseapp.com',
  PROJECT_ID: process.env.FIREBASE_PROJECT_ID || 'ndungu-shop',
  STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET || 'ndungu-shop.firebasestorage.app',
  MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID || '922809103168',
  APP_ID: process.env.FIREBASE_APP_ID || '1:922809103168:web:b6c6931ba3570c887540a5',
  MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID || 'G-M6KJQTHX6N',
};

// Cloudinary configuration
export const CLOUDINARY_CONFIG = {
  // Cloudinary config from environment variables
  CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME || 'Untitled',
  API_KEY: process.env.CLOUDINARY_API_KEY || '592135841396749',
  API_SECRET: process.env.CLOUDINARY_API_SECRET || 'GWV3zVxrV_h8C6DBryBtOFyS93Y',
  UPLOAD_PRESET: process.env.CLOUDINARY_UPLOAD_PRESET || 'NDUNGU',
};

// API configuration
export const API_CONFIG = {
  // Base URL for API requests
  BASE_URL: process.env.API_BASE_URL || expoConstants.API_BASE_URL || 'https://api.ndungusshop.com/v1',
  // API timeout in milliseconds
  TIMEOUT: 10000,
};

// App configuration
export const APP_CONFIG = {
  // App name
  APP_NAME: 'Ndungus Shop',
  // App version
  APP_VERSION: Constants.expoConfig?.version || '1.0.0',
  // Environment (development, staging, production)
  ENVIRONMENT: process.env.NODE_ENV || 'development',
  // Debug mode
  DEBUG: process.env.NODE_ENV !== 'production',
};

// Storage configuration
export const STORAGE_CONFIG = {
  // Prefix for AsyncStorage keys
  KEY_PREFIX: '@NdungusShop:',
};

// Export all configurations
export default {
  FIREBASE: FIREBASE_CONFIG,
  CLOUDINARY: CLOUDINARY_CONFIG,
  API: API_CONFIG,
  APP: APP_CONFIG,
  STORAGE: STORAGE_CONFIG,
};
