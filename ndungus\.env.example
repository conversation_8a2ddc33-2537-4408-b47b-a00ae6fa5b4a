# MongoDB connection string
# Format: mongodb+srv://<username>:<password>@<cluster-url>/<database-name>?retryWrites=true&w=majority
MONGODB_URI=mongodb+srv://username:<EMAIL>/ndungus_shop?retryWrites=true&w=majority

# Cloudinary configuration
# Sign up at https://cloudinary.com/ and get these values
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# API configuration
API_BASE_URL=https://api.ndungusshop.com/v1

# App environment (development, staging, production)
APP_ENV=development
