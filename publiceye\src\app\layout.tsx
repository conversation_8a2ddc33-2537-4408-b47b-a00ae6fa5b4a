import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Public Eye - Justice Tracking',
  description: 'Track justice cases and keep them in the public eye',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <header className="bg-gradient-to-r from-red-700 to-red-900 text-white p-4">
          <div className="container mx-auto flex justify-between items-center">
            <a href="/" className="text-2xl font-bold">Public Eye</a>
            <nav>
              <ul className="flex space-x-6">
                <li><a href="/" className="hover:underline">Home</a></li>
                <li><a href="/cases" className="hover:underline">Cases</a></li>
                <li><a href="/cases/create" className="hover:underline">Create Case</a></li>
              </ul>
            </nav>
          </div>
        </header>
        {children}
        <footer className="bg-gray-900 text-white p-6 mt-12">
          <div className="container mx-auto text-center">
            <p>&copy; {new Date().getFullYear()} Public Eye. All rights reserved.</p>
          </div>
        </footer>
      </body>
    </html>
  );
}
