<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Public Eye - Crime Tracker</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.25.0/font/bootstrap-icons.css">
  <!-- Custom CSS -->
  <link rel="stylesheet" href="styles.css">
</head>
<body>

  <!-- Navbar -->
  <nav class="navbar navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="#">Public Eye</a>
      <ul class="navbar-nav">
        <li class="nav-item"><a class="nav-link" href="#">Home</a></li>
        <li class="nav-item"><a class="nav-link" href="#">Cases</a></li>
        <li class="nav-item"><a class="nav-link" href="#">About</a></li>
        <li class="nav-item"><a class="nav-link" href="#">Contact</a></li>
      </ul>
    </div>
  </nav>

  <!-- Video Section -->
  <section class="video-section">
    <div class="container">
      <div class="embed-responsive embed-responsive-16by9">
        <iframe class="embed-responsive-item" src="https://www.youtube.com/embed/your_video_id" allowfullscreen></iframe>
      </div>
    </div>
  </section>

  <!-- Search Bar -->
  <section class="search-section">
    <div class="container">
      <div class="input-group">
        <input type="text" class="form-control" id="searchInput" placeholder="Search for cases...">
        <div class="input-group-append">
          <button class="btn btn-dark" onclick="searchCases()"><i class="bi bi-search"></i> Search</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Cards Section -->
  <section class="cards-section">
    <div class="container">
      <div class="card">
        <h2>Case Title 1</h2>
        <p>Description of the case goes here. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
        <button class="btn btn-dark" onclick="viewDetails(1)">View Details</button>
      </div>
      <div class="card">
        <h2>Case Title 2</h2>
        <p>Description of the case goes here. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
        <button class="btn btn-dark" onclick="viewDetails(2)">View Details</button>
      </div>
      <!-- Add more cards as needed -->
    </div>
  </section>

  <!-- Bootstrap JS and Popper.js -->
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <!-- Your Custom JavaScript -->
  <script src="index.js"></script>
</body>
</html>