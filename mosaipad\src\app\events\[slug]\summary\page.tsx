"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Share2, Sparkles, Star, MessageCircle, Lightbulb, HelpCircle, Link as LinkIcon, Users } from "lucide-react";

interface SummaryData {
  keyTakeaways: string[];
  standoutThoughts: string[];
  unexpectedNuggets: string[];
  questionsReflections: string[];
  mentions: string[];
  communityQuotes: string[];
}

interface EventData {
  id: string;
  name: string;
  description?: string;
  date?: string;
  creator: {
    name?: string;
  };
}

export default function SummaryPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [event, setEvent] = useState<EventData | null>(null);
  const [summary, setSummary] = useState<SummaryData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, [slug]);

  const fetchSummary = async () => {
    try {
      const response = await fetch(`/api/events/${slug}/summary`);
      const data = await response.json();

      if (data.success) {
        setEvent(data.event);
        setSummary({
          keyTakeaways: data.summary.keyTakeaways,
          standoutThoughts: data.summary.standoutThoughts,
          unexpectedNuggets: data.summary.unexpectedNuggets,
          questionsReflections: data.summary.questionsReflections,
          mentions: data.summary.mentions,
          communityQuotes: data.summary.communityQuotes,
        });
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError("Failed to load summary");
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${event?.name} - Collaborative Summary`,
          text: "Check out this collaborative summary from Mosaipad",
          url: window.location.href,
        });
      } catch (err) {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-sky-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading summary...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-sky-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle>Summary Not Available</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button asChild>
              <Link href={`/events/${slug}`}>Back to Event</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-sky-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" asChild>
                <Link href={`/events/${slug}`}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Event
                </Link>
              </Button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">Mosaipad</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleShare}>
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button>
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            📝 EventMosaic Collective Notes
          </h1>
          <h2 className="text-2xl font-semibold text-indigo-600 mb-2">
            {event?.name}
          </h2>
          <p className="text-gray-600">
            A collaborative summary of insights and perspectives
          </p>
          {event?.creator?.name && (
            <p className="text-sm text-gray-500 mt-2">
              Organized by {event.creator.name}
            </p>
          )}
        </div>

        {/* Summary Sections */}
        <div className="space-y-8">
          {/* Key Takeaways */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="w-5 h-5 mr-2 text-yellow-500" />
                Key Takeaways
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {summary?.keyTakeaways.map((takeaway, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span>{takeaway}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Standout Thoughts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
                Standout Thoughts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {summary?.standoutThoughts.map((thought, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-sky-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span>{thought}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Unexpected Nuggets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
                Unexpected Nuggets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {summary?.unexpectedNuggets.map((nugget, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span>{nugget}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Questions & Reflections */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <HelpCircle className="w-5 h-5 mr-2 text-green-500" />
                Questions & Reflections
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {summary?.questionsReflections.map((question, index) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span>{question}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Mentions & Links */}
          {summary?.mentions && summary.mentions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LinkIcon className="w-5 h-5 mr-2 text-blue-500" />
                  Mentions & Links
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {summary.mentions.map((mention, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span>{mention}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Community Quotes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2 text-indigo-500" />
                From the Community
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {summary?.communityQuotes.map((quote, index) => (
                  <blockquote key={index} className="border-l-4 border-indigo-600 pl-4 italic text-gray-700">
                    "{quote}"
                  </blockquote>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center py-8 border-t border-gray-200">
          <p className="text-gray-600">
            Compiled by <strong>Mosaipad</strong> • Transform everyone's notes into collective insights
          </p>
          <div className="mt-4">
            <Button asChild>
              <Link href="/">Create Your Own Event</Link>
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
