export enum CaseStage {
  REPORTED = 'REPORTED',
  INVESTIGATING = 'INVESTIGATING',
  EVIDENCE_COLLECTED = 'EVIDENCE_COLLECTED',
  VERIFIED = 'VERIFIED',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

export interface Media {
  id: string;
  url: string;
  type: 'image' | 'video';
  description?: string;
}

export interface Contribution {
  id: string;
  userId: string;
  codeName: string; // User's chosen code name for privacy
  colorCode: string; // Color code for the user's avatar
  stage: CaseStage;
  description: string;
  mediaUrls: string[];
  sourceUrl?: string;
  createdAt: string;
  isVerified: boolean;
  isFlagged: boolean;
  flagReason?: string;
}

export interface Case {
  id: string;
  title: string;
  location?: string;
  description: string;
  mediaUrls: string[];
  sourceUrl?: string;
  currentStage: CaseStage;
  createdBy: string;
  createdAt: Date | string | number; // Can be a timestamp number in Realtime Database
  updatedAt: Date | string | number; // Can be a timestamp number in Realtime Database
  followers: Record<string, boolean>; // Object with user IDs as keys
  contributions: Record<string, Contribution> | Contribution[]; // Object with contribution IDs as keys or array
  updates: Record<string, {
    stage: CaseStage;
    description: string;
    mediaUrls: string[];
    sourceUrl?: string;
    userId: string;
    contributorName: string;
    contributorColor: string;
    timestamp: number;
  }>;
  isVerified: boolean;
  isFlagged: boolean;
}

export type GroupedContributions = {
  [key in CaseStage]?: Contribution[];
};
