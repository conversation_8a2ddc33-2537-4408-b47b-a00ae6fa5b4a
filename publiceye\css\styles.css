/* Base Styles */
:root {
    /* Justice-Focused Color Theme with Urgency and Hope */
    --primary-color: #D00000;      /* Intense Red for Urgency */
    --secondary-color: #9D0208;    /* Deep Red for Anger */
    --accent-color: #03045E;       /* Deep Blue for Authority */
    --hope-color: #0077B6;         /* Bright Blue for Hope */
    --hope-accent: #90E0EF;        /* Light Blue for Optimism */
    --dark-text: #370617;          /* Deep Red-Brown for Text */
    --medium-text: #6A040F;        /* Muted Red for Secondary Text */
    --light-bg: #FFF1F0;           /* Soft Red-White for Background */
    --card-bg: rgba(255, 255, 255, 0.7); /* Transparent White for Glass Effect */
    --glass-border: rgba(255, 255, 255, 0.4); /* Border for Glass Effect */
    --glass-shadow: rgba(55, 6, 23, 0.15); /* Shadow for Glass Effect */
    --success-color: #38B000;      /* Vibrant Green for Success */
    --warning-color: #FFBA08;      /* Bright Yellow for Warning */
    --danger-color: #DC2F02;       /* Bright Orange-Red for Danger */

    /* Status Colors for Timeline - From Urgency to Resolution */
    --status-report: #D00000;      /* Report stage - Urgent Red */
    --status-investigate: #DC2F02; /* Investigation stage - Orange-Red */
    --status-arrest: #E85D04;      /* Arrest stage - Orange */
    --status-pretrial: #FFBA08;    /* Pre-trial stage - Yellow */
    --status-trial: #3F88C5;       /* Trial stage - Blue */
    --status-verdict: #38B000;     /* Verdict stage - Green for Resolution */

    /* Emotional Gradients */
    --urgency-gradient: linear-gradient(135deg, #D00000, #9D0208);
    --anger-gradient: linear-gradient(135deg, #9D0208, #6A040F);
    --hope-gradient: linear-gradient(135deg, #0077B6, #0096C7);
    --justice-gradient: linear-gradient(135deg, #D00000, #03045E);
    --resolution-gradient: linear-gradient(135deg, #0077B6, #38B000);

    /* Replacing previous gradients */
    --primary-gradient: var(--urgency-gradient);
    --secondary-gradient: var(--anger-gradient);
    --accent-gradient: var(--hope-gradient);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: var(--dark-text);
    line-height: 1.6;
    background-color: var(--light-bg);
    background-image:
        radial-gradient(at 40% 20%, rgba(208, 0, 0, 0.08) 0px, transparent 50%),
        radial-gradient(at 80% 0%, rgba(0, 119, 182, 0.07) 0px, transparent 50%),
        radial-gradient(at 0% 50%, rgba(157, 2, 8, 0.08) 0px, transparent 50%),
        radial-gradient(at 90% 90%, rgba(56, 176, 0, 0.05) 0px, transparent 50%);
    background-attachment: fixed;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--urgency-gradient);
    z-index: 1001;
    box-shadow: 0 0 10px rgba(208, 0, 0, 0.4);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: var(--secondary-color);
}

button {
    cursor: pointer;
    font-family: 'Montserrat', sans-serif;
    border: none;
    outline: none;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.5em;
    letter-spacing: -0.02em;
}

p {
    margin-bottom: 1rem;
    font-weight: 400;
}

/* Header Styles */
header {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 30px rgba(208, 0, 0, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(208, 0, 0, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    background: var(--justice-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-bottom: 0;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.logo h1::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--urgency-gradient);
    transform: scaleX(0.85);
    transform-origin: left;
}

.tagline {
    font-size: 0.9rem;
    color: var(--medium-text);
    margin-bottom: 0;
    font-weight: 600;
    margin-top: 5px;
    opacity: 0.9;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    align-items: center;
}

nav a {
    color: var(--dark-text);
    font-weight: 600;
    padding: 0.5rem;
    position: relative;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

nav a:not(.btn-primary)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--urgency-gradient);
    transition: width 0.3s ease, transform 0.2s ease;
}

nav a:not(.btn-primary):hover::after {
    width: 100%;
    transform: scaleY(1.5);
}

nav a.active::after {
    width: 100%;
    background: var(--urgency-gradient);
    transform: scaleY(1.5);
}

.btn-primary {
    background: var(--urgency-gradient);
    color: white;
    padding: 0.7rem 1.5rem;
    border-radius: 50px;
    font-weight: 700;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(208, 0, 0, 0.3);
    border: none;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(208, 0, 0, 0.3);
    color: white;
}

/* Hero Section */
.hero {
    background: var(--urgency-gradient);
    color: white;
    padding: 5rem 0 4rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 0 0 30px 30px;
    box-shadow: 0 10px 30px rgba(208, 0, 0, 0.2);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 60%, rgba(0, 119, 182, 0.1) 0%, transparent 50%);
}

.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
    z-index: 1;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 800;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem;
    position: relative;
    font-weight: 500;
    z-index: 2;
    opacity: 0.9;
}

.search-container {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.search-container input {
    flex: 1;
    padding: 1.2rem 1.5rem;
    border: none;
    border-radius: 50px 0 0 50px;
    font-size: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    font-family: 'Montserrat', sans-serif;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-right: none;
    background-color: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
}

.search-container input:focus {
    outline: none;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.2);
    background-color: white;
}

.search-btn {
    background: var(--hope-gradient);
    color: white;
    border: none;
    border-radius: 0 50px 50px 0;
    padding: 0 2rem;
    font-size: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.search-btn:hover {
    transform: translateX(2px);
    box-shadow: 0 4px 15px rgba(0, 119, 182, 0.25);
}

/* Category Tabs */
.case-categories {
    padding: 3rem 0 1rem;
    margin-top: -20px;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 0.8rem;
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(208, 0, 0, 0.1);
    border: 1px solid rgba(208, 0, 0, 0.1);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 10;
}

.tab {
    background: none;
    border: none;
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
    color: var(--medium-text);
    border-radius: 50px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.tab:hover {
    background-color: rgba(208, 0, 0, 0.05);
}

.tab.active {
    color: white;
    background: var(--urgency-gradient);
    box-shadow: 0 4px 15px rgba(208, 0, 0, 0.3);
}

/* Cases Grid */
.cases-grid {
    padding: 2rem 0 4rem;
}

.cases-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.case-card {
    background-color: var(--card-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px var(--glass-shadow);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(208, 0, 0, 0.1);
}

.case-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--urgency-gradient);
    z-index: 2;
}

.case-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(208, 0, 0, 0.12);
}

.case-status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--urgency-gradient);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 4px 10px rgba(208, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.case-media-preview {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.case-media-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
    z-index: 1;
}

.case-media-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.case-card h3 {
    padding: 1.2rem 1.5rem 0.5rem;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-text);
    position: relative;
}

.case-excerpt {
    padding: 0 1.5rem;
    color: var(--medium-text);
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.6;
    position: relative;
}

.case-meta {
    padding: 1rem 1.5rem 1.2rem;
    display: flex;
    justify-content: space-between;
    color: var(--medium-text);
    font-size: 0.8rem;
    font-weight: 600;
    position: relative;
}

.case-meta span {
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.case-meta i {
    color: var(--primary-color);
}

.case-meta .followers i {
    color: var(--primary-color);
}

.case-meta .updates i {
    color: var(--hope-color);
}

.progress-bar {
    height: 8px;
    background-color: rgba(229, 231, 235, 0.5);
    position: relative;
    overflow: hidden;
}

.progress-step {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--primary-color);
    width: 40%; /* This will be dynamic based on progress */
    border-radius: 0 4px 4px 0;
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    border: 1px solid rgba(208, 0, 0, 0.2);
    padding: 0.9rem 2.8rem;
    border-radius: 50px;
    font-weight: 700;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(208, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(208, 0, 0, 0.15);
}

.load-more {
    text-align: center;
    margin-top: 3rem;
    position: relative;
}

.load-more::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--hope-color), transparent);
}

/* Footer */
footer {
    background: linear-gradient(to bottom, rgba(55, 6, 23, 0.95), rgba(55, 6, 23, 0.99));
    color: white;
    padding: 5rem 0 2rem;
    position: relative;
    border-radius: 30px 30px 0 0;
    margin-top: 3rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 10% 20%, rgba(208, 0, 0, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 90% 80%, rgba(0, 119, 182, 0.1) 0%, transparent 30%);
    opacity: 0.7;
    border-radius: 30px 30px 0 0;
}

footer::after {
    content: '';
    position: absolute;
    top: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg,
        var(--status-report) 0%,
        var(--status-investigate) 20%,
        var(--status-arrest) 40%,
        var(--status-pretrial) 60%,
        var(--status-trial) 80%,
        var(--status-verdict) 100%
    );
    opacity: 0.8;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
    position: relative;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1.5rem;
    font-weight: 700;
    font-size: 1.3rem;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--hope-gradient);
    border-radius: 3px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
    position: relative;
}

.footer-section ul li::before {
    content: '›';
    position: absolute;
    left: -15px;
    color: var(--hope-color);
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-section ul li:hover::before {
    opacity: 1;
    left: -10px;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-section a:hover {
    color: white;
    transform: translateX(3px);
    display: inline-block;
}

.social-icons {
    display: flex;
    gap: 1.2rem;
}

.social-icons a {
    color: white;
    font-size: 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.social-icons a:hover {
    transform: translateY(-3px);
    background: var(--hope-color);
    border-color: transparent;
}

.copyright {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9rem;
    position: relative;
    font-weight: 500;
}

.copyright::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: var(--hope-gradient);
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        text-align: center;
    }

    nav ul {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero {
        padding: 4rem 0 3rem;
    }

    .hero h2 {
        font-size: 2.2rem;
    }

    .cases-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        padding: 0 1rem;
    }

    .category-tabs {
        max-width: 90%;
        flex-wrap: wrap;
        padding: 0.6rem;
    }

    .tab {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    footer {
        border-radius: 20px 20px 0 0;
    }
}

@media (max-width: 480px) {
    .hero h2 {
        font-size: 1.8rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .category-tabs {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
    }

    .search-container {
        flex-direction: column;
        width: 90%;
    }

    .search-container input,
    .search-btn {
        border-radius: 50px;
        width: 100%;
    }

    .search-btn {
        margin-top: 0.5rem;
    }

    .case-card {
        transform: none !important;
    }

    .case-card:hover {
        transform: translateY(-5px) !important;
    }

    .footer-section h3 {
        font-size: 1.2rem;
    }
}
