import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Fa<PERSON><PERSON><PERSON>, FaReg<PERSON>eart, FaPlus } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import { Case, CaseStage } from '@/types/case';
import ProgressUpdateModal from './ProgressUpdateModal';
import toast from 'react-hot-toast';

interface CaseCardProps {
  caseData: Case;
  onUpdate?: () => void;
}

const CaseCard: React.FC<CaseCardProps> = ({ caseData }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (caseData) {
      setFollowerCount(Object.keys(caseData.followers || {}).length);
      if (user) {
        setIsFollowing(!!caseData.followers?.[user.id]);
      }
    }
  }, [caseData, user]);

  const handleFollow = async () => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/cases/${caseData.id}/follow`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          action: isFollowing ? 'unfollow' : 'follow'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setIsFollowing(data.isFollowing);
        setFollowerCount(data.followersCount);
      }
    } catch (error) {
      console.error('Error following case:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProgressUpdate = async (data: { 
    stage: CaseStage; 
    description: string; 
    mediaUrls: string[];
    sourceUrl?: string;
  }) => {
    try {
      const response = await fetch(`/api/cases/${caseData.id}/progress`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          userId: user?.id
        })
      });

      if (response.ok) {
        setIsProgressModalOpen(false);
        // Optionally refresh the case data
      }
    } catch (error) {
      console.error('Error adding progress update:', error);
    }
  };

  const handleCardClick = () => {
    router.push(`/cases/${caseData.id}`);
  };

  if (!caseData) return null;

  return (
    <div 
      className="bg-white bg-opacity-70 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:scale-[1.02] cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Card Header */}
      <div className="relative h-48">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-60"></div>
        <img
          src={caseData.mediaUrls?.[0] || '/placeholder-case.jpg'}
          alt={caseData.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <h3 className="text-xl font-bold text-white mb-1">{caseData.title}</h3>
          <p className="text-white text-sm opacity-90">{caseData.location}</p>
        </div>
        </div>

      {/* Card Content */}
      <div className="p-4">
        <p className="text-gray-600 mb-4 line-clamp-3">{caseData.description}</p>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
              {caseData.currentStage}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-500 text-sm">
              {followerCount} followers
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleFollow();
            }}
            disabled={isLoading}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              isFollowing
                ? 'bg-red-100 text-red-800 hover:bg-red-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
          >
            <FaHeart className={isFollowing ? 'text-red-600' : 'text-gray-400'} />
            <span>{isFollowing ? 'Following' : 'Follow'}</span>
          </button>

          {user && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsProgressModalOpen(true);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full text-sm font-medium hover:from-red-700 hover:to-red-900 transition-all transform hover:scale-105"
            >
              <FaPlus className="text-sm" />
              <span>Add Progress</span>
            </button>
          )}
        </div>
      </div>

      {/* Progress Update Modal */}
      {isProgressModalOpen && (
        <ProgressUpdateModal
          isOpen={isProgressModalOpen}
          onClose={() => setIsProgressModalOpen(false)}
          onSubmit={handleProgressUpdate}
          currentStage={caseData.currentStage}
        />
      )}
    </div>
  );
};

export default CaseCard;
