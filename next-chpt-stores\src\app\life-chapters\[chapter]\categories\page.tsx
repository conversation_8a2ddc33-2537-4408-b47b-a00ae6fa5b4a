"use client";

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LifeChapterWelcome from '@/components/LifeChapterWelcome';
import { getLifeChapters, getCategoriesInLifeChapter } from '@/data/products';

interface CategoriesPageProps {
  params: {
    chapter: string;
  };
}

export default function CategoriesPage({ params }: CategoriesPageProps) {
  const [lifeChapter, setLifeChapter] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  
  // Define category details
  const categoryDetails = {
    "Kitchen": {
      description: "Essential kitchen items for cooking, cleaning, and organization",
      image: "/categories/kitchen.jpg",
      color: "from-blue-500 to-blue-700"
    },
    "Bedroom": {
      description: "Everything you need for a comfortable and restful sleep space",
      image: "/categories/bedroom.jpg",
      color: "from-indigo-500 to-indigo-700"
    },
    "Bathroom": {
      description: "Bathroom essentials for cleanliness and organization",
      image: "/categories/bathroom.jpg",
      color: "from-teal-500 to-teal-700"
    },
    "Electronics": {
      description: "Must-have electronic devices and accessories",
      image: "/categories/electronics.jpg",
      color: "from-gray-700 to-gray-900"
    },
    "Office": {
      description: "Everything you need for a productive work environment",
      image: "/categories/office.jpg",
      color: "from-green-500 to-green-700"
    },
    "Moving": {
      description: "Essential supplies for a smooth moving experience",
      image: "/categories/moving.jpg",
      color: "from-purple-500 to-purple-700"
    },
    "Home": {
      description: "General home essentials for comfort and functionality",
      image: "/categories/home.jpg",
      color: "from-yellow-500 to-yellow-700"
    },
    "Accessories": {
      description: "Useful accessories to complement your essentials",
      image: "/categories/accessories.jpg",
      color: "from-pink-500 to-pink-700"
    },
    "Clothing": {
      description: "Essential clothing items for your new chapter",
      image: "/categories/clothing.jpg",
      color: "from-red-500 to-red-700"
    },
    "Nursery": {
      description: "Everything you need to create a safe and comfortable nursery",
      image: "/categories/nursery.jpg",
      color: "from-orange-500 to-orange-700"
    }
  };
  
  useEffect(() => {
    // Convert URL slug to proper chapter name
    const chapterSlug = params.chapter;
    const allChapters = getLifeChapters();
    
    // Find the chapter that matches the slug (case-insensitive)
    const chapter = allChapters.find(
      ch => ch.toLowerCase().replace(/\s+/g, '-') === chapterSlug.toLowerCase()
    );
    
    if (!chapter) {
      notFound();
      return;
    }
    
    setLifeChapter(chapter);
    
    // Get categories for this life chapter
    const chapterCategories = getCategoriesInLifeChapter(chapter);
    setCategories(chapterCategories);
  }, [params.chapter]);
  
  if (!lifeChapter) {
    return <div>Loading...</div>;
  }
  
  return (
    <>
      <Header />
      
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-16">
            <h1 className="text-5xl font-bold text-primary mb-8 text-center">{lifeChapter} Categories</h1>
            
            <div className="mb-12">
              <LifeChapterWelcome lifeChapter={lifeChapter} />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {categories.map(category => {
              const details = categoryDetails[category as keyof typeof categoryDetails] || {
                description: `Essential ${category.toLowerCase()} items for your ${lifeChapter} journey`,
                image: "/placeholder.jpg",
                color: "from-primary to-secondary"
              };
              
              return (
                <Link 
                  key={category} 
                  href={`/life-chapters/${lifeChapter.toLowerCase().replace(/\s+/g, '-')}/${category.toLowerCase()}`}
                  className="group"
                >
                  <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full">
                    <div className="relative h-48">
                      <div className={`absolute inset-0 bg-gradient-to-t ${details.color} opacity-80 z-10`}></div>
                      <Image 
                        src={details.image} 
                        alt={category}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 z-20 flex items-center justify-center">
                        <h3 className="text-2xl font-bold text-white text-center px-4">{category}</h3>
                      </div>
                    </div>
                    <div className="p-6">
                      <p className="text-secondary mb-4">
                        {details.description}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-primary font-semibold">Browse {category}</span>
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          className="h-5 w-5 text-accent group-hover:translate-x-1 transition-transform" 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </main>
      
      <Footer />
    </>
  );
}
