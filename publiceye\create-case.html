<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Case - Public Eye</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Additional styles specific to the create case page */
        .create-case-form {
            background-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            padding: 2.5rem;
            margin: 2.5rem 0;
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="url"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 1px solid rgba(209, 213, 219, 0.5);
            border-radius: 12px;
            font-family: 'Montserrat', sans-serif;
            font-size: 1rem;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="url"]:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .form-group textarea {
            min-height: 150px;
            resize: vertical;
        }

        .media-upload {
            border: 2px dashed #d1d5db;
            padding: 2rem;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .media-upload:hover {
            border-color: var(--primary-color);
        }

        .media-upload i {
            font-size: 2rem;
            color: var(--medium-text);
            margin-bottom: 1rem;
        }

        .media-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .media-item {
            position: relative;
            height: 150px;
            background-color: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }

        .media-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .media-item .remove-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background-color: rgba(239, 68, 68, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .progress-options {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .progress-option {
            flex: 1;
            min-width: 120px;
            padding: 1rem;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .progress-option:hover {
            border-color: var(--primary-color);
        }

        .progress-option.selected {
            border-color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.1);
        }

        .progress-option i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--medium-text);
        }

        .progress-option.selected i {
            color: var(--primary-color);
        }

        .submit-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
            display: inline-block;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
        }

        @media (max-width: 768px) {
            .progress-options {
                flex-direction: column;
            }

            .progress-option {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Public Eye</h1>
                <p class="tagline">Justice Remembered, Cases Tracked</p>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="create-case.html" class="active">Create Case</a></li>
                    <li><a href="your-cases.html">Your Cases</a></li>
                    <li><a href="#" class="btn-primary">Sign In</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="page-header">
            <div class="container">
                <h2>Create a New Case</h2>
                <p>Document a case that needs public attention and tracking.</p>
            </div>
        </section>

        <section>
            <div class="container">
                <form class="create-case-form" id="create-case-form">
                    <div class="form-group">
                        <label for="case-title">Case Title</label>
                        <input type="text" id="case-title" name="title" placeholder="Enter a clear, descriptive title" required>
                    </div>

                    <div class="form-group">
                        <label for="case-location">Location</label>
                        <input type="text" id="case-location" name="location" placeholder="City, State, Country">
                    </div>

                    <div class="form-group">
                        <label>Media (Photos/Videos)</label>
                        <div class="media-upload" id="media-upload">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Click to upload or drag and drop</p>
                            <p class="small">Supported formats: JPG, PNG, MP4, MOV (max 10MB each)</p>
                            <input type="file" id="media-input" multiple accept="image/*,video/*" style="display: none;">
                        </div>
                        <div class="media-preview" id="media-preview"></div>
                    </div>

                    <div class="form-group">
                        <label for="case-url">Related URL</label>
                        <input type="url" id="case-url" name="url" placeholder="News article, official report, or other relevant link">
                    </div>

                    <div class="form-group">
                        <label for="case-description">Case Description</label>
                        <textarea id="case-description" name="description" placeholder="Provide a detailed account of the case..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label>Current Progress Stage</label>
                        <div class="progress-options">
                            <div class="progress-option" data-value="report">
                                <i class="fas fa-flag"></i>
                                <p>REPORT</p>
                            </div>
                            <div class="progress-option" data-value="investigate">
                                <i class="fas fa-search"></i>
                                <p>INVESTIGATE</p>
                            </div>
                            <div class="progress-option" data-value="arrest">
                                <i class="fas fa-handcuffs"></i>
                                <p>ARREST</p>
                            </div>
                            <div class="progress-option" data-value="pretrial">
                                <i class="fas fa-gavel"></i>
                                <p>PRE-TRIAL</p>
                            </div>
                            <div class="progress-option" data-value="trial">
                                <i class="fas fa-balance-scale"></i>
                                <p>TRIAL</p>
                            </div>
                            <div class="progress-option" data-value="verdict">
                                <i class="fas fa-check-circle"></i>
                                <p>VERDICT</p>
                            </div>
                        </div>
                        <input type="hidden" id="progress-stage" name="progress" value="report">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="submit-btn">Submit Case</button>
                    </div>
                </form>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Public Eye</h3>
                    <p>Keeping cases visible and justice accountable.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="create-case.html">Create Case</a></li>
                        <li><a href="your-cases.html">Your Cases</a></li>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 Public Eye. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Media upload functionality
            const mediaUpload = document.getElementById('media-upload');
            const mediaInput = document.getElementById('media-input');
            const mediaPreview = document.getElementById('media-preview');

            mediaUpload.addEventListener('click', function() {
                mediaInput.click();
            });

            mediaInput.addEventListener('change', function() {
                handleFiles(this.files);
            });

            // Progress stage selection
            const progressOptions = document.querySelectorAll('.progress-option');
            const progressStageInput = document.getElementById('progress-stage');

            progressOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all options
                    progressOptions.forEach(opt => opt.classList.remove('selected'));

                    // Add selected class to clicked option
                    this.classList.add('selected');

                    // Update hidden input value
                    progressStageInput.value = this.getAttribute('data-value');
                });
            });

            // Select the first progress option by default
            progressOptions[0].classList.add('selected');

            // Form submission
            const form = document.getElementById('create-case-form');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // In a real application, this would send the data to a server
                // For now, we'll just show a success message
                alert('Case submitted successfully! It will be reviewed before being published.');

                // Redirect to home page
                window.location.href = 'index.html';
            });

            // Function to handle file uploads
            function handleFiles(files) {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    // Check if file is an image or video
                    if (!file.type.match('image/*') && !file.type.match('video/*')) {
                        continue;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const mediaItem = document.createElement('div');
                        mediaItem.className = 'media-item';

                        const img = document.createElement('img');
                        img.src = e.target.result;
                        mediaItem.appendChild(img);

                        const removeBtn = document.createElement('button');
                        removeBtn.className = 'remove-btn';
                        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                        removeBtn.addEventListener('click', function() {
                            mediaItem.remove();
                        });

                        mediaItem.appendChild(removeBtn);
                        mediaPreview.appendChild(mediaItem);
                    };

                    reader.readAsDataURL(file);
                }
            }
        });
    </script>
</body>
</html>
