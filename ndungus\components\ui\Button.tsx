import { PlatformPressable } from '@react-navigation/elements';
import * as Haptics from 'expo-haptics';
import React, { ReactNode } from 'react';
import { ActivityIndicator, Platform, StyleSheet, Text, TextStyle, ViewStyle } from 'react-native';

import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

type ButtonProps = {
  onPress: () => void;
  title: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
};

/**
 * Custom button component with different variants and sizes
 */
export function Button({
  onPress,
  title,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}: ButtonProps) {
  const colorScheme = useColorScheme() ?? 'light';

  // Get button styles based on variant and size
  const buttonStyles = getButtonStyles(variant, size, colorScheme, disabled, fullWidth);
  const textStyles = getTextStyles(variant, size, colorScheme, disabled);

  // Handle press with haptic feedback
  const handlePress = () => {
    if (disabled || loading) return;

    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    onPress();
  };

  return (
    <PlatformPressable
      onPress={handlePress}
      style={[buttonStyles, style]}
      disabled={disabled || loading}>
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost'
            ? Colors[colorScheme === 'dark' ? 'primary' : 'primary']
            : Colors.white}
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && icon}
          <Text style={[textStyles, textStyle]}>{title}</Text>
          {icon && iconPosition === 'right' && icon}
        </>
      )}
    </PlatformPressable>
  );
}

// Helper functions to get styles based on props
function getButtonStyles(
  variant: ButtonVariant,
  size: ButtonSize,
  colorScheme: 'light' | 'dark',
  disabled: boolean,
  fullWidth: boolean,
): ViewStyle {
  // Base styles
  const baseStyles: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.md,
    gap: Spacing.sm,
    alignSelf: fullWidth ? 'stretch' : 'flex-start',
    opacity: disabled ? 0.5 : 1,
  };

  // Size styles
  const sizeStyles: Record<ButtonSize, ViewStyle> = {
    sm: {
      paddingVertical: Spacing.xs,
      paddingHorizontal: Spacing.sm,
      minHeight: 32,
    },
    md: {
      paddingVertical: Spacing.sm,
      paddingHorizontal: Spacing.md,
      minHeight: 40,
    },
    lg: {
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      minHeight: 48,
    },
  };

  // Variant styles
  const variantStyles: Record<ButtonVariant, ViewStyle> = {
    primary: {
      backgroundColor: Colors.primary,
    },
    secondary: {
      backgroundColor: Colors.secondary,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colorScheme === 'dark' ? Colors.primary : Colors.primary,
    },
    ghost: {
      backgroundColor: 'transparent',
    },
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
}

function getTextStyles(
  variant: ButtonVariant,
  size: ButtonSize,
  colorScheme: 'light' | 'dark',
  disabled: boolean,
): TextStyle {
  // Base styles
  const baseStyles: TextStyle = {
    fontFamily: Typography.fontFamily.medium,
    textAlign: 'center',
    opacity: disabled ? 0.7 : 1,
  };

  // Size styles
  const sizeStyles: Record<ButtonSize, TextStyle> = {
    sm: {
      fontSize: Typography.fontSize.sm,
      lineHeight: Typography.lineHeight.sm,
    },
    md: {
      fontSize: Typography.fontSize.md,
      lineHeight: Typography.lineHeight.md,
    },
    lg: {
      fontSize: Typography.fontSize.lg,
      lineHeight: Typography.lineHeight.lg,
    },
  };

  // Variant styles
  const variantStyles: Record<ButtonVariant, TextStyle> = {
    primary: {
      color: Colors.black,
      fontWeight: '600',
    },
    secondary: {
      color: Colors.white,
      fontWeight: '600',
    },
    outline: {
      color: colorScheme === 'dark' ? Colors.primary : Colors.primary,
      fontWeight: '500',
    },
    ghost: {
      color: colorScheme === 'dark' ? Colors.primary : Colors.primary,
      fontWeight: '500',
    },
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
}

const styles = StyleSheet.create({
  // Additional styles if needed
});
