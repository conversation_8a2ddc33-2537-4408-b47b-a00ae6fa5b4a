import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TextInput, Pressable, FlatList } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock data for daily notes
const mockNotes = [
  {
    id: '1',
    date: new Date(2023, 4, 17),
    content: 'Customer requested iPhone 14 Pro Max. Need to check with supplier for availability and pricing.',
    tags: ['customer-request', 'inventory'],
  },
  {
    id: '2',
    date: new Date(2023, 4, 17),
    content: 'Need to restock Nike shoes. Current inventory is running low.',
    tags: ['inventory', 'restock'],
  },
  {
    id: '3',
    date: new Date(2023, 4, 16),
    content: 'Supplier meeting scheduled for next week. Prepare list of items to order.',
    tags: ['supplier', 'meeting'],
  },
  {
    id: '4',
    date: new Date(2023, 4, 15),
    content: 'Sales were slow today. Consider running a promotion for the weekend.',
    tags: ['sales', 'promotion'],
  },
  {
    id: '5',
    date: new Date(2023, 4, 14),
    content: 'New competitor opened nearby. Need to monitor their pricing and offerings.',
    tags: ['competition', 'market-research'],
  },
];

// Available tags for notes
const availableTags = [
  'customer-request',
  'inventory',
  'restock',
  'supplier',
  'meeting',
  'sales',
  'promotion',
  'competition',
  'market-research',
];

export default function DailyNotesScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [newNoteContent, setNewNoteContent] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isAddingNote, setIsAddingNote] = useState(false);

  // Format date
  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return date.toLocaleDateString(undefined, options);
  };

  // Get notes for selected date
  const getNotesForDate = (date: Date) => {
    return mockNotes.filter(note => 
      note.date.getDate() === date.getDate() &&
      note.date.getMonth() === date.getMonth() &&
      note.date.getFullYear() === date.getFullYear()
    );
  };

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Add new note
  const addNote = () => {
    if (newNoteContent.trim() === '') return;
    
    // In a real app, you would save the note to the database
    console.log('Adding note:', {
      date: selectedDate,
      content: newNoteContent,
      tags: selectedTags,
    });
    
    // Reset form
    setNewNoteContent('');
    setSelectedTags([]);
    setIsAddingNote(false);
  };

  // Render date navigation
  const renderDateNavigation = () => {
    const prevDate = new Date(selectedDate);
    prevDate.setDate(prevDate.getDate() - 1);
    
    const nextDate = new Date(selectedDate);
    nextDate.setDate(nextDate.getDate() + 1);
    
    return (
      <View style={styles.dateNavigation}>
        <Pressable
          style={styles.dateNavigationButton}
          onPress={() => setSelectedDate(prevDate)}>
          <MaterialIcons
            name="chevron-left"
            size={24}
            color={colorScheme === 'dark' ? Colors.gray300 : Colors.gray700}
          />
        </Pressable>
        
        <Text
          style={[
            styles.dateText,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          {formatDate(selectedDate)}
        </Text>
        
        <Pressable
          style={styles.dateNavigationButton}
          onPress={() => setSelectedDate(nextDate)}>
          <MaterialIcons
            name="chevron-right"
            size={24}
            color={colorScheme === 'dark' ? Colors.gray300 : Colors.gray700}
          />
        </Pressable>
      </View>
    );
  };

  // Render note card
  const renderNoteCard = (note: typeof mockNotes[0]) => {
    return (
      <GlassCard key={note.id} style={styles.noteCard}>
        <Text
          style={[
            styles.noteContent,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          {note.content}
        </Text>
        
        {note.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {note.tags.map(tag => (
              <View
                key={tag}
                style={[
                  styles.tagPill,
                  {
                    backgroundColor: colorScheme === 'dark'
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}>
                <Text
                  style={[
                    styles.tagText,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  {tag}
                </Text>
              </View>
            ))}
          </View>
        )}
      </GlassCard>
    );
  };

  // Render add note form
  const renderAddNoteForm = () => {
    return (
      <GlassCard style={styles.addNoteCard}>
        <Text
          style={[
            styles.addNoteTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Add Note for {formatDate(selectedDate)}
        </Text>
        
        <TextInput
          style={[
            styles.noteInput,
            {
              color: colorScheme === 'dark' ? Colors.white : Colors.black,
              backgroundColor: colorScheme === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.05)',
            },
          ]}
          placeholder="Enter your note here..."
          placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
          multiline
          value={newNoteContent}
          onChangeText={setNewNoteContent}
        />
        
        <Text
          style={[
            styles.tagsTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Tags
        </Text>
        
        <View style={styles.tagsSelectionContainer}>
          {availableTags.map(tag => (
            <Pressable
              key={tag}
              style={[
                styles.tagPill,
                {
                  backgroundColor: selectedTags.includes(tag)
                    ? Colors.primary
                    : colorScheme === 'dark'
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              onPress={() => toggleTag(tag)}>
              <Text
                style={[
                  styles.tagText,
                  {
                    color: selectedTags.includes(tag)
                      ? Colors.black
                      : colorScheme === 'dark'
                        ? Colors.gray300
                        : Colors.gray700,
                  },
                ]}>
                {tag}
              </Text>
            </Pressable>
          ))}
        </View>
        
        <View style={styles.addNoteActions}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => {
              setIsAddingNote(false);
              setNewNoteContent('');
              setSelectedTags([]);
            }}
          />
          <Button
            title="Save Note"
            variant="primary"
            onPress={addNote}
          />
        </View>
      </GlassCard>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Daily Notes',
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.white,
          },
          headerTintColor: colorScheme === 'dark' ? Colors.white : Colors.black,
          headerShadowVisible: false,
        }}
      />
      <View
        style={[
          styles.container,
          { backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.gray50 },
        ]}>
        {/* Date Navigation */}
        <GlassCard style={styles.dateNavigationCard}>
          {renderDateNavigation()}
        </GlassCard>
        
        {/* Notes List */}
        <ScrollView
          style={styles.notesContainer}
          contentContainerStyle={styles.notesContentContainer}>
          {!isAddingNote && (
            <Button
              title="Add Note"
              variant="primary"
              icon={<MaterialIcons name="add" size={18} color={Colors.black} />}
              onPress={() => setIsAddingNote(true)}
              style={styles.addNoteButton}
            />
          )}
          
          {isAddingNote && renderAddNoteForm()}
          
          {getNotesForDate(selectedDate).map(renderNoteCard)}
          
          {getNotesForDate(selectedDate).length === 0 && !isAddingNote && (
            <GlassCard style={styles.emptyNotesCard}>
              <MaterialIcons
                name="event-note"
                size={48}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.emptyNotesText,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                No notes for this day
              </Text>
              <Text
                style={[
                  styles.emptyNotesSubtext,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Add a note to keep track of important information
              </Text>
            </GlassCard>
          )}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  dateNavigationCard: {
    marginBottom: Spacing.md,
    padding: Spacing.sm,
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateNavigationButton: {
    padding: Spacing.sm,
  },
  dateText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
  },
  notesContainer: {
    flex: 1,
  },
  notesContentContainer: {
    paddingBottom: Spacing.xl,
  },
  addNoteButton: {
    marginBottom: Spacing.md,
  },
  noteCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  noteContent: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    lineHeight: Typography.lineHeight.md,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: Spacing.sm,
    gap: Spacing.xs,
  },
  tagPill: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  tagText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  addNoteCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  addNoteTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.sm,
  },
  noteInput: {
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    minHeight: 100,
    textAlignVertical: 'top',
    fontFamily: Typography.fontFamily.regular,
    fontSize: Typography.fontSize.md,
  },
  tagsTitle: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  tagsSelectionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginBottom: Spacing.md,
  },
  addNoteActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  emptyNotesCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
  },
  emptyNotesText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
  },
  emptyNotesSubtext: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
});
