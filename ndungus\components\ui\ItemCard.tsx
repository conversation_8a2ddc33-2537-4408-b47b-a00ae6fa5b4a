import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import { StyleSheet, Text, View, Pressable, ViewStyle } from 'react-native';

import { GlassCard } from '@/components/ui/GlassCard';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type ItemCardProps = {
  name: string;
  image?: string;
  code?: string;
  category?: string;
  buyingPrice?: number;
  sellingPrice?: number;
  units?: number;
  status?: 'in-stock' | 'low-stock' | 'out-of-stock';
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  style?: ViewStyle;
};

/**
 * A card component for displaying inventory items
 */
export function ItemCard({
  name,
  image,
  code,
  category,
  buyingPrice,
  sellingPrice,
  units,
  status = 'in-stock',
  onPress,
  onEdit,
  onDelete,
  style,
}: ItemCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Map status to StatusBadge props
  const getStatusProps = () => {
    switch (status) {
      case 'in-stock':
        return { status: 'success' as const, label: 'In Stock' };
      case 'low-stock':
        return { status: 'warning' as const, label: 'Low Stock' };
      case 'out-of-stock':
        return { status: 'error' as const, label: 'Out of Stock' };
      default:
        return { status: 'info' as const };
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };
  
  return (
    <Pressable onPress={onPress}>
      <GlassCard style={[styles.container, style]}>
        <View style={styles.header}>
          {image ? (
            <Image
              source={{ uri: image }}
              style={styles.image}
              contentFit="cover"
              transition={200}
            />
          ) : (
            <View
              style={[
                styles.imagePlaceholder,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="image"
                size={24}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray500}
              />
            </View>
          )}
          
          <View style={styles.headerContent}>
            <Text
              style={[
                styles.name,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}
              numberOfLines={1}>
              {name}
            </Text>
            
            {code && (
              <Text
                style={[
                  styles.code,
                  { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
                ]}>
                {code}
              </Text>
            )}
            
            {category && (
              <View style={styles.categoryContainer}>
                <Text
                  style={[
                    styles.category,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  {category}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.footer}>
          <View style={styles.priceContainer}>
            {sellingPrice !== undefined && (
              <Text
                style={[
                  styles.sellingPrice,
                  { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                ]}>
                {formatCurrency(sellingPrice)}
              </Text>
            )}
            
            {buyingPrice !== undefined && (
              <Text
                style={[
                  styles.buyingPrice,
                  { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
                ]}>
                Cost: {formatCurrency(buyingPrice)}
              </Text>
            )}
          </View>
          
          <View style={styles.rightFooter}>
            {units !== undefined && (
              <Text
                style={[
                  styles.units,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {units} units
              </Text>
            )}
            
            <StatusBadge {...getStatusProps()} />
          </View>
        </View>
        
        {(onEdit || onDelete) && (
          <View style={styles.actions}>
            {onEdit && (
              <Pressable
                onPress={onEdit}
                style={({ pressed }) => [
                  styles.actionButton,
                  {
                    opacity: pressed ? 0.7 : 1,
                    backgroundColor: colorScheme === 'dark'
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}>
                <MaterialIcons
                  name="edit"
                  size={16}
                  color={colorScheme === 'dark' ? Colors.white : Colors.black}
                />
              </Pressable>
            )}
            
            {onDelete && (
              <Pressable
                onPress={onDelete}
                style={({ pressed }) => [
                  styles.actionButton,
                  {
                    opacity: pressed ? 0.7 : 1,
                    backgroundColor: colorScheme === 'dark'
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}>
                <MaterialIcons
                  name="delete"
                  size={16}
                  color={Colors.error}
                />
              </Pressable>
            )}
          </View>
        )}
      </GlassCard>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.xs,
  },
  header: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 2,
  },
  code: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: 4,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  category: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: Spacing.sm,
  },
  priceContainer: {
    flex: 1,
  },
  sellingPrice: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  buyingPrice: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  rightFooter: {
    alignItems: 'flex-end',
    gap: 4,
  },
  units: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  actions: {
    position: 'absolute',
    top: Spacing.xs,
    right: Spacing.xs,
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: BorderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
