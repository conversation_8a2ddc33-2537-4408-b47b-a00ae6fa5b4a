import React from 'react';
import { format } from 'date-fns';
import { FaLink } from 'react-icons/fa';
import { CaseStage } from '@/types/case';

interface Update {
  stage: CaseStage;
  description: string;
  mediaUrls: string[];
  sourceUrl?: string;
  userId: string;
  contributorName: string;
  contributorColor: string;
  timestamp: number;
}

interface CaseUpdatesProps {
  updates: Record<string, Update>;
}

const CaseUpdates: React.FC<CaseUpdatesProps> = ({ updates }) => {
  const formatDate = (timestamp: number) => {
    return format(new Date(timestamp), 'MMM d, yyyy h:mm a');
  };

  return (
    <div className="space-y-6 mt-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Case Updates</h2>
      {Object.entries(updates)
        .sort(([, a], [, b]) => b.timestamp - a.timestamp)
        .map(([id, update]) => (
          <div
            key={id}
            className="bg-white rounded-lg shadow-md p-6 flex gap-6"
          >
            {/* Main Content */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                  {update.stage.toUpperCase()}
                </span>
                <span className="text-gray-500 text-sm">
                  {formatDate(update.timestamp)}
                </span>
              </div>
              
              <p className="text-gray-700 mb-4">{update.description}</p>

              {/* Media Preview */}
              {update.mediaUrls && update.mediaUrls.length > 0 && (
                <div className="grid grid-cols-4 gap-2 mb-4">
                  {update.mediaUrls.map((url, index) => (
                    <div key={index} className="relative h-24 rounded-md overflow-hidden">
                      <img
                        src={url}
                        alt={`Update media ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Source URL */}
              {update.sourceUrl && (
                <a
                  href={update.sourceUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:underline text-sm"
                >
                  <FaLink className="mr-1" />
                  Source
                </a>
              )}
            </div>

            {/* Contributor Info */}
            <div className="flex flex-col items-center">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-medium text-lg mb-2"
                style={{ backgroundColor: update.contributorColor }}
              >
                {update.contributorName.charAt(0).toUpperCase()}
              </div>
              <span className="text-sm text-gray-600 text-center">
                {update.contributorName}
              </span>
            </div>
          </div>
        ))}
    </div>
  );
};

export default CaseUpdates; 