import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TextInput, Pressable, FlatList } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock data for potential sales
const mockPotentialSales = [
  {
    id: '1',
    itemName: 'iPhone 14 Pro Max',
    description: 'Customer requested the latest iPhone model in Deep Purple color with 256GB storage.',
    customerName: '<PERSON>',
    contact: '+254712345678',
    date: new Date(2023, 4, 17),
    status: 'pending',
  },
  {
    id: '2',
    itemName: 'Samsung 65" QLED TV',
    description: 'Customer interested in a large Samsung TV for their living room. Wants to know about delivery options.',
    customerName: '<PERSON>',
    contact: '+254723456789',
    date: new Date(2023, 4, 16),
    status: 'added-to-stock',
  },
  {
    id: '3',
    itemName: 'MacBook Pro M2',
    description: '16-inch model with 32GB RAM and 1TB storage. Customer will check back next week.',
    customerName: 'David Williams',
    contact: '+254734567890',
    date: new Date(2023, 4, 15),
    status: 'pending',
  },
  {
    id: '4',
    itemName: 'Sony PlayStation 5',
    description: 'Customer wants to be notified when PS5 is back in stock. Willing to pay deposit.',
    customerName: 'Sarah Brown',
    contact: '+254745678901',
    date: new Date(2023, 4, 14),
    status: 'pending',
  },
  {
    id: '5',
    itemName: 'Nike Air Jordan 4',
    description: 'Size 42, Black Cement colorway. Customer will check back in two weeks.',
    customerName: 'Michael Davis',
    contact: '+254756789012',
    date: new Date(2023, 4, 13),
    status: 'added-to-stock',
  },
];

type PotentialSaleStatus = 'pending' | 'added-to-stock' | 'not-available' | 'customer-declined';

export default function PotentialSalesScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | PotentialSaleStatus>('all');
  const [isAddingNew, setIsAddingNew] = useState(false);
  
  // Form state for adding new potential sale
  const [newSale, setNewSale] = useState({
    itemName: '',
    description: '',
    customerName: '',
    contact: '',
  });

  // Format date
  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    };
    return date.toLocaleDateString(undefined, options);
  };

  // Filter potential sales based on search query and active tab
  const filteredSales = mockPotentialSales.filter(sale => {
    const matchesSearch = 
      sale.itemName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sale.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sale.customerName.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = activeTab === 'all' || sale.status === activeTab;
    
    return matchesSearch && matchesTab;
  });

  // Add new potential sale
  const addPotentialSale = () => {
    // Validate form
    if (!newSale.itemName || !newSale.customerName) return;
    
    // In a real app, you would save the potential sale to the database
    console.log('Adding potential sale:', newSale);
    
    // Reset form
    setNewSale({
      itemName: '',
      description: '',
      customerName: '',
      contact: '',
    });
    setIsAddingNew(false);
  };

  // Get status badge props
  const getStatusBadgeProps = (status: PotentialSaleStatus) => {
    switch (status) {
      case 'pending':
        return { status: 'pending' as const, label: 'Pending' };
      case 'added-to-stock':
        return { status: 'success' as const, label: 'Added to Stock' };
      case 'not-available':
        return { status: 'error' as const, label: 'Not Available' };
      case 'customer-declined':
        return { status: 'info' as const, label: 'Customer Declined' };
      default:
        return { status: 'info' as const };
    }
  };

  // Render tabs
  const renderTabs = () => {
    const tabs: { id: 'all' | PotentialSaleStatus; label: string }[] = [
      { id: 'all', label: 'All' },
      { id: 'pending', label: 'Pending' },
      { id: 'added-to-stock', label: 'Added to Stock' },
      { id: 'not-available', label: 'Not Available' },
    ];
    
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabsContainer}>
        {tabs.map(tab => (
          <Pressable
            key={tab.id}
            style={[
              styles.tab,
              activeTab === tab.id && {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 215, 0, 0.2)'
                  : 'rgba(255, 215, 0, 0.1)',
                borderBottomColor: Colors.primary,
                borderBottomWidth: 2,
              },
            ]}
            onPress={() => setActiveTab(tab.id)}>
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === tab.id
                    ? Colors.primary
                    : colorScheme === 'dark'
                      ? Colors.gray300
                      : Colors.gray700,
                  fontFamily: activeTab === tab.id
                    ? Typography.fontFamily.semiBold
                    : Typography.fontFamily.regular,
                },
              ]}>
              {tab.label}
            </Text>
          </Pressable>
        ))}
      </ScrollView>
    );
  };

  // Render potential sale card
  const renderPotentialSaleCard = (sale: typeof mockPotentialSales[0]) => {
    return (
      <GlassCard key={sale.id} style={styles.saleCard}>
        <View style={styles.saleCardHeader}>
          <Text
            style={[
              styles.itemName,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            {sale.itemName}
          </Text>
          <StatusBadge {...getStatusBadgeProps(sale.status as PotentialSaleStatus)} />
        </View>
        
        <Text
          style={[
            styles.description,
            { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
          ]}>
          {sale.description}
        </Text>
        
        <View style={styles.customerInfo}>
          <View style={styles.customerDetail}>
            <MaterialIcons
              name="person"
              size={16}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.customerDetailText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {sale.customerName}
            </Text>
          </View>
          
          {sale.contact && (
            <View style={styles.customerDetail}>
              <MaterialIcons
                name="phone"
                size={16}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.customerDetailText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {sale.contact}
              </Text>
            </View>
          )}
          
          <View style={styles.customerDetail}>
            <MaterialIcons
              name="event"
              size={16}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.customerDetailText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {formatDate(sale.date)}
            </Text>
          </View>
        </View>
        
        {sale.status === 'pending' && (
          <View style={styles.actions}>
            <Button
              title="Add to Stock"
              variant="primary"
              size="sm"
              onPress={() => {}}
            />
            <Button
              title="Not Available"
              variant="outline"
              size="sm"
              onPress={() => {}}
            />
          </View>
        )}
      </GlassCard>
    );
  };

  // Render add new form
  const renderAddNewForm = () => {
    return (
      <GlassCard style={styles.addNewCard}>
        <Text
          style={[
            styles.addNewTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Add Potential Sale
        </Text>
        
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.formLabel,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Item Name *
          </Text>
          <TextInput
            style={[
              styles.formInput,
              {
                color: colorScheme === 'dark' ? Colors.white : Colors.black,
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}
            placeholder="Enter item name"
            placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            value={newSale.itemName}
            onChangeText={(text) => setNewSale({ ...newSale, itemName: text })}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.formLabel,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Description
          </Text>
          <TextInput
            style={[
              styles.formInput,
              styles.textArea,
              {
                color: colorScheme === 'dark' ? Colors.white : Colors.black,
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}
            placeholder="Enter item description"
            placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            value={newSale.description}
            onChangeText={(text) => setNewSale({ ...newSale, description: text })}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.formLabel,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Customer Name *
          </Text>
          <TextInput
            style={[
              styles.formInput,
              {
                color: colorScheme === 'dark' ? Colors.white : Colors.black,
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}
            placeholder="Enter customer name"
            placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            value={newSale.customerName}
            onChangeText={(text) => setNewSale({ ...newSale, customerName: text })}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.formLabel,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Contact Number
          </Text>
          <TextInput
            style={[
              styles.formInput,
              {
                color: colorScheme === 'dark' ? Colors.white : Colors.black,
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}
            placeholder="Enter contact number"
            placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            keyboardType="phone-pad"
            value={newSale.contact}
            onChangeText={(text) => setNewSale({ ...newSale, contact: text })}
          />
        </View>
        
        <View style={styles.formActions}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => {
              setIsAddingNew(false);
              setNewSale({
                itemName: '',
                description: '',
                customerName: '',
                contact: '',
              });
            }}
          />
          <Button
            title="Save"
            variant="primary"
            onPress={addPotentialSale}
          />
        </View>
      </GlassCard>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Potential Sales',
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.white,
          },
          headerTintColor: colorScheme === 'dark' ? Colors.white : Colors.black,
          headerShadowVisible: false,
        }}
      />
      <View
        style={[
          styles.container,
          { backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.gray50 },
        ]}>
        {/* Header */}
        <GlassCard style={styles.header}>
          {/* Search Bar */}
          <View
            style={[
              styles.searchContainer,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}>
            <MaterialIcons
              name="search"
              size={20}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <TextInput
              style={[
                styles.searchInput,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}
              placeholder="Search potential sales..."
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <Pressable onPress={() => setSearchQuery('')}>
                <MaterialIcons
                  name="close"
                  size={20}
                  color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                />
              </Pressable>
            )}
          </View>
          
          {/* Tabs */}
          {renderTabs()}
          
          {/* Add New Button */}
          {!isAddingNew && (
            <Button
              title="Add Potential Sale"
              variant="primary"
              icon={<MaterialIcons name="add" size={18} color={Colors.black} />}
              onPress={() => setIsAddingNew(true)}
              style={styles.addButton}
            />
          )}
        </GlassCard>
        
        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}>
          {isAddingNew && renderAddNewForm()}
          
          {filteredSales.map(renderPotentialSaleCard)}
          
          {filteredSales.length === 0 && (
            <GlassCard style={styles.emptyCard}>
              <MaterialIcons
                name="lightbulb"
                size={48}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.emptyText,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                No potential sales found
              </Text>
              <Text
                style={[
                  styles.emptySubtext,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {searchQuery
                  ? 'Try adjusting your search'
                  : 'Add potential sales to track customer requests'}
              </Text>
            </GlassCard>
          )}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.sm,
  },
  tab: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    marginRight: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  tabText: {
    fontSize: Typography.fontSize.sm,
  },
  addButton: {
    marginTop: Spacing.sm,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
  },
  saleCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  saleCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  itemName: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
  },
  description: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: Spacing.md,
    lineHeight: Typography.lineHeight.md,
  },
  customerInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  customerDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  customerDetailText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  addNewCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  addNewTitle: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  formLabel: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  formInput: {
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    fontFamily: Typography.fontFamily.regular,
    fontSize: Typography.fontSize.md,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  emptyCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
});
