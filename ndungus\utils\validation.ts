/**
 * Validation utilities for form inputs
 */

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation (at least 8 characters, 1 uppercase, 1 lowercase, 1 number)
export const isValidPassword = (password: string): boolean => {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
  return passwordRegex.test(password);
};

// Name validation (at least 2 characters, letters only)
export const isValidName = (name: string): boolean => {
  return name.trim().length >= 2;
};

// Phone validation
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
};

// Form validation for login
export interface LoginValidationResult {
  isValid: boolean;
  emailError?: string;
  passwordError?: string;
}

export const validateLoginForm = (email: string, password: string): LoginValidationResult => {
  const result: LoginValidationResult = { isValid: true };

  if (!email) {
    result.emailError = 'Email is required';
    result.isValid = false;
  } else if (!isValidEmail(email)) {
    result.emailError = 'Please enter a valid email address';
    result.isValid = false;
  }

  if (!password) {
    result.passwordError = 'Password is required';
    result.isValid = false;
  }

  return result;
};

// Form validation for registration
export interface RegisterValidationResult {
  isValid: boolean;
  nameError?: string;
  emailError?: string;
  passwordError?: string;
  confirmPasswordError?: string;
}

export const validateRegisterForm = (
  name: string,
  email: string,
  password: string,
  confirmPassword: string
): RegisterValidationResult => {
  const result: RegisterValidationResult = { isValid: true };

  if (!name) {
    result.nameError = 'Name is required';
    result.isValid = false;
  } else if (!isValidName(name)) {
    result.nameError = 'Name must be at least 2 characters';
    result.isValid = false;
  }

  if (!email) {
    result.emailError = 'Email is required';
    result.isValid = false;
  } else if (!isValidEmail(email)) {
    result.emailError = 'Please enter a valid email address';
    result.isValid = false;
  }

  if (!password) {
    result.passwordError = 'Password is required';
    result.isValid = false;
  } else if (!isValidPassword(password)) {
    result.passwordError = 'Password must be at least 8 characters with 1 uppercase, 1 lowercase, and 1 number';
    result.isValid = false;
  }

  if (!confirmPassword) {
    result.confirmPasswordError = 'Please confirm your password';
    result.isValid = false;
  } else if (password !== confirmPassword) {
    result.confirmPasswordError = 'Passwords do not match';
    result.isValid = false;
  }

  return result;
};
