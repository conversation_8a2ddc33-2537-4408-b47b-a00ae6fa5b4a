"use client";

import { useState } from 'react';
import { Product } from '@/data/products';
import Image from 'next/image';
import { useCart } from '@/context/CartContext';

interface BudgetRecommendationProps {
  products: Product[];
  category: string;
  lifeChapter?: string;
  onClose?: () => void;
  isModal?: boolean;
}

interface RecommendationPackage {
  title: string;
  description: string;
  items: Product[];
  totalPrice: number;
}

export default function BudgetRecommendation({
  products,
  category,
  lifeChapter,
  onClose,
  isModal = false
}: BudgetRecommendationProps) {
  const [budget, setBudget] = useState('');
  const [loading, setLoading] = useState(false);
  const [recommendationPackages, setRecommendationPackages] = useState<RecommendationPackage[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null);

  const handleRecommendation = async () => {
    // Validate budget
    const budgetAmount = parseFloat(budget);
    if (isNaN(budgetAmount) || budgetAmount <= 0) {
      setError('Please enter a valid budget amount');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Call the recommendations API
      const response = await fetch('/api/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          budget: budgetAmount,
          lifeChapter,
          category,
          products
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.packages) {
        setRecommendationPackages(data.packages);
        if (data.packages.length > 0) {
          setSelectedPackage(0); // Select first package by default
        }
      } else {
        setError(data.error || 'Failed to generate recommendations');
      }
    } catch (err) {
      console.error('Error getting recommendations:', err);
      setError('An error occurred while generating recommendations');
    } finally {
      setLoading(false);
    }

  };

  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { addToCart } = useCart();

  const handleAddPackageToCart = (packageIndex: number) => {
    const pkg = recommendationPackages[packageIndex];
    if (!pkg) return;

    // Add each item in the package to the cart
    pkg.items.forEach(item => {
      addToCart(item, 1);
    });

    setSuccessMessage(`Successfully added ${pkg.items.length} items from "${pkg.title}" to your cart!`);

    // Clear the success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  const handleReset = () => {
    setRecommendationPackages([]);
    setSelectedPackage(null);
    setBudget('');
    setError(null);
  };

  const content = (
    <div className={`bg-white rounded-lg shadow-md ${isModal ? '' : 'p-6'}`}>
      {isModal && (
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-xl font-semibold text-primary">
            Budget Recommendations
            {lifeChapter && ` for ${lifeChapter}`}
            {category && ` - ${category}`}
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-700 hover:text-gray-900 bg-gray-100 p-1 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      )}

      <div className={isModal ? 'p-6' : ''}>
        {recommendationPackages.length === 0 ? (
          <>
            {!isModal && (
              <h3 className="text-xl font-semibold text-primary mb-4">Budget Recommendation</h3>
            )}
            <p className="text-gray-700 mb-4">
              Enter your budget and our AI will recommend the best combination of {category.toLowerCase()} items for you.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-grow">
                <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">Your Budget (KSh)</label>
                <input
                  type="number"
                  id="budget"
                  value={budget}
                  onChange={(e) => setBudget(e.target.value)}
                  placeholder="e.g., 5000"
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-primary text-gray-800"
                />
              </div>
              <div className="self-end">
                <button
                  onClick={handleRecommendation}
                  disabled={loading}
                  className={`bg-accent text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors flex items-center ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      Get AI Recommendations
                    </>
                  )}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-4">
                {error}
              </div>
            )}

            <div className="bg-gray-50 p-4 rounded-lg mt-6">
              <h4 className="font-semibold text-primary mb-2">How it works</h4>
              <p className="text-gray-700 mb-3">
                Our AI-powered recommendation system analyzes thousands of product combinations to find the best match for your budget and needs.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-gray-700 font-medium">Prioritizes essential items first</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-gray-700 font-medium">Considers quality and value for money</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-gray-700 font-medium">Provides multiple options to choose from</span>
                </li>
              </ul>
            </div>

            <div className="mt-4 text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
              <p>Powered by Gemini AI</p>
            </div>
          </>
        ) : (
          <>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-primary">Your Personalized Recommendations</h3>
              <button
                onClick={handleReset}
                className="text-gray-700 hover:text-primary text-sm font-medium flex items-center bg-gray-100 px-3 py-1 rounded-lg shadow-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Start Over
              </button>
            </div>

            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-secondary font-medium">Your Budget:</span>
                <span className="text-accent font-bold">KSh {parseFloat(budget).toFixed(2)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-accent h-2.5 rounded-full"
                  style={{ width: '100%' }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {recommendationPackages.map((pkg, index) => (
                <div
                  key={index}
                  className={`border rounded-lg overflow-hidden cursor-pointer transition-all ${selectedPackage === index ? 'border-accent ring-2 ring-accent' : 'border-gray-200 hover:border-accent'}`}
                  onClick={() => setSelectedPackage(index)}
                >
                  <div className={`p-4 ${selectedPackage === index ? 'bg-accent text-white' : 'bg-gray-50 text-primary'}`}>
                    <h4 className="font-semibold">{pkg.title}</h4>
                    <p className={selectedPackage === index ? 'text-white text-opacity-90 font-medium' : 'text-gray-700 font-medium'}>
                      {pkg.description}
                    </p>
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-700 font-medium">Total:</span>
                      <span className="text-accent font-bold">KSh {pkg.totalPrice.toFixed(2)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-accent h-2 rounded-full"
                        style={{ width: `${(pkg.totalPrice / parseFloat(budget)) * 100}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      {pkg.items.length} items included
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Success Message */}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 flex items-center shadow-md animate-fadeIn">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {successMessage}
              </div>
            )}

            {selectedPackage !== null && recommendationPackages[selectedPackage] && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-primary mb-3">
                  {recommendationPackages[selectedPackage].title} Details
                </h4>

                <div className="space-y-3 mb-4">
                  {recommendationPackages[selectedPackage].items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-white rounded border border-gray-100">
                      <div className="flex items-center">
                        <div className="bg-gray-100 rounded-full p-1 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-primary">{item.name}</p>
                          <p className="text-xs text-gray-500">{item.subcategory}</p>
                        </div>
                      </div>
                      <span className="text-accent font-medium">KSh {item.price.toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                <div className="flex justify-between items-center p-3 bg-white rounded border border-gray-200 mb-4">
                  <span className="font-semibold text-primary">Total Package Price:</span>
                  <span className="text-accent font-bold text-lg">
                    KSh {recommendationPackages[selectedPackage].totalPrice.toFixed(2)}
                  </span>
                </div>

                <button
                  onClick={() => handleAddPackageToCart(selectedPackage)}
                  className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-secondary transition-colors flex items-center justify-center shadow-md"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Add All Items to Cart
                </button>
              </div>
            )}

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h5 className="font-medium text-blue-800 mb-1">AI-Powered Recommendation</h5>
                  <p className="text-blue-700 text-sm">
                    These recommendations are generated based on your budget and typical needs for
                    {lifeChapter && ` ${lifeChapter}`}
                    {category && ` - ${category}`}.
                    You can customize these packages by adding or removing items from your cart.
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {content}
        </div>
      </div>
    );
  }

  return content;
}
