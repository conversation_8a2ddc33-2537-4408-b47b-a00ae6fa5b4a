import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { generateSlug } from "@/lib/utils";

const createEventSchema = z.object({
  name: z.string().min(1, "Event name is required"),
  description: z.string().optional(),
  date: z.string().optional(),
  deadline: z.string().min(1, "Deadline is required"),
  role: z.string().default("Host"),
  attendeeEmails: z.string().optional(),
  isPremium: z.boolean().default(false),
  creatorEmail: z.string().email("Valid email is required"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createEventSchema.parse(body);

    // Generate unique slug
    let slug = generateSlug(validatedData.name);
    let counter = 1;
    
    // Ensure slug is unique
    while (await db.event.findUnique({ where: { slug } })) {
      slug = `${generateSlug(validatedData.name)}-${counter}`;
      counter++;
    }

    // Create or find user
    let user = await db.user.findUnique({
      where: { email: validatedData.creatorEmail }
    });

    if (!user) {
      user = await db.user.create({
        data: {
          email: validatedData.creatorEmail,
          name: validatedData.creatorEmail.split('@')[0], // Use email prefix as default name
        }
      });
    }

    // Create event
    const event = await db.event.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        date: validatedData.date ? new Date(validatedData.date) : null,
        deadline: new Date(validatedData.deadline),
        slug,
        isPremium: validatedData.isPremium,
        isPublished: true,
        creatorId: user.id,
      }
    });

    // Process attendee emails if provided
    if (validatedData.attendeeEmails && validatedData.isPremium) {
      const emails = validatedData.attendeeEmails
        .split(/[,\n]/)
        .map(email => email.trim())
        .filter(email => email && email.includes('@'));

      for (const email of emails) {
        await db.attendee.create({
          data: {
            email,
            eventId: event.id,
          }
        });
      }

      // TODO: Send email invitations
    }

    return NextResponse.json({
      success: true,
      event: {
        id: event.id,
        slug: event.slug,
        name: event.name,
        url: `${process.env.APP_URL}/events/${event.slug}`,
      }
    });

  } catch (error) {
    console.error("Error creating event:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    const events = await db.event.findMany({
      where: {
        isPublished: true,
      },
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          }
        },
        _count: {
          select: {
            notes: true,
            attendees: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    const total = await db.event.count({
      where: {
        isPublished: true,
      }
    });

    return NextResponse.json({
      success: true,
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      }
    });

  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
