import { NextApiRequest, NextApiResponse } from 'next';
import { getDatabase } from 'firebase-admin/database';
import { CaseStage } from '@/types/case';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { id } = req.query;
  const { stage, description, mediaUrls, sourceUrl, userId } = req.body;

  if (!id || !stage || !description || !userId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const db = getDatabase();
    const caseRef = db.ref(`cases/${id}`);
    const updatesRef = db.ref(`cases/${id}/updates`);

    // Get user data
    const userRef = db.ref(`users/${userId}`);
    const userSnapshot = await userRef.once('value');
    const userData = userSnapshot.val();

    if (!userData) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create the update object
    const update = {
      stage,
      description,
      mediaUrls: mediaUrls || [],
      sourceUrl: sourceUrl || null,
      userId,
      contributorName: userData.displayName || userData.codeName || 'Anonymous',
      contributorColor: userData.colorCode || '#F44336',
      timestamp: Date.now(),
    };

    // Push the update to the updates array
    const updateRef = await updatesRef.push(update);

    // Update the case's current stage
    await caseRef.update({
      currentStage: stage,
      updatedAt: Date.now(),
    });

    return res.status(200).json({
      success: true,
      updateId: updateRef.key,
      update,
    });
  } catch (error) {
    console.error('Error adding progress update:', error);
    return res.status(500).json({ error: 'Failed to add progress update' });
  }
} 