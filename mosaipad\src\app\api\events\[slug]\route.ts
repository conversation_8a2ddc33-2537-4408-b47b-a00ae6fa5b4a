import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const event = await db.event.findUnique({
      where: {
        slug: params.slug,
        isPublished: true,
      },
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          }
        },
        _count: {
          select: {
            notes: true,
            attendees: true,
          }
        },
        summary: true,
      }
    });

    if (!event) {
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 }
      );
    }

    // Check if deadline has passed
    const isDeadlinePassed = new Date() > event.deadline;

    return NextResponse.json({
      success: true,
      event: {
        ...event,
        isDeadlinePassed,
        canSubmit: !isDeadlinePassed,
      }
    });

  } catch (error) {
    console.error("Error fetching event:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
