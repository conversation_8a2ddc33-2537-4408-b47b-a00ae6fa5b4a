/**
 * Firebase configuration and services
 */

import { initializeApp } from 'firebase/app';
import { createUserWithEmailAndPassword, getAuth, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  limit,
  orderBy,
  query,
  serverTimestamp,
  Timestamp,
  updateDoc,
  where
} from 'firebase/firestore';

// Import Firebase configuration
import { FIREBASE_CONFIG } from './config';

// Your Firebase configuration
const firebaseConfig = {
  apiKey: FIREBASE_CONFIG.API_KEY,
  authDomain: FIREBASE_CONFIG.AUTH_DOMAIN,
  projectId: FIREBASE_CONFIG.PROJECT_ID,
  storageBucket: FIREBASE_CONFIG.STORAGE_BUCKET,
  messagingSenderId: FIREBASE_CONFIG.MESSAGING_SENDER_ID,
  appId: FIREBASE_CONFIG.APP_ID,
  measurementId: FIREBASE_CONFIG.MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Initialize Analytics
// let analytics = null;
// try {
//   analytics = getAnalytics(app);
// } catch (error) {
//   console.error('Error initializing analytics:', error);
// }

// Collection names
export const COLLECTIONS = {
  PRODUCTS: 'products',
  SALES: 'sales',
  ORDERS: 'orders',
  USERS: 'users',
  SETTINGS: 'settings',
};

// Authentication functions
export const loginWithEmail = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    console.error('Error logging in:', error);
    throw new Error(error.message);
  }
};

export const registerWithEmail = async (email: string, password: string) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    console.error('Error registering:', error);
    throw new Error(error.message);
  }
};

export const logoutUser = async () => {
  try {
    await signOut(auth);
    return true;
  } catch (error) {
    console.error('Error logging out:', error);
    return false;
  }
};

// Firestore helper functions
export const getDocuments = async <T>(collectionName: string): Promise<T[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, collectionName));
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T);
  } catch (error) {
    console.error(`Error getting documents from ${collectionName}:`, error);
    return [];
  }
};

export const getDocumentById = async <T>(collectionName: string, id: string): Promise<T | null> => {
  try {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error getting document by ID from ${collectionName}:`, error);
    return null;
  }
};

export const addDocument = async <T>(collectionName: string, data: any): Promise<T> => {
  try {
    // Add timestamp
    const dataWithTimestamp = {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(collection(db, collectionName), dataWithTimestamp);
    return { id: docRef.id, ...data } as T;
  } catch (error) {
    console.error(`Error adding document to ${collectionName}:`, error);
    throw error;
  }
};

export const updateDocument = async <T>(collectionName: string, id: string, data: any): Promise<T | null> => {
  try {
    // Add updated timestamp
    const dataWithTimestamp = {
      ...data,
      updatedAt: serverTimestamp(),
    };

    const docRef = doc(db, collectionName, id);
    await updateDoc(docRef, dataWithTimestamp);

    // Get the updated document
    const updatedDoc = await getDoc(docRef);

    if (updatedDoc.exists()) {
      return { id: updatedDoc.id, ...updatedDoc.data() } as T;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error updating document in ${collectionName}:`, error);
    return null;
  }
};

export const deleteDocument = async (collectionName: string, id: string): Promise<boolean> => {
  try {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
    return true;
  } catch (error) {
    console.error(`Error deleting document from ${collectionName}:`, error);
    return false;
  }
};

// Query functions
export const queryDocuments = async <T>(
  collectionName: string,
  conditions: Array<{ field: string; operator: any; value: any }>,
  orderByField?: string,
  orderDirection?: 'asc' | 'desc',
  limitCount?: number
): Promise<T[]> => {
  try {
    const collectionRef = collection(db, collectionName);
    let queryRef = query(collectionRef);

    // Add where conditions
    if (conditions && conditions.length > 0) {
      conditions.forEach(condition => {
        queryRef = query(queryRef, where(condition.field, condition.operator, condition.value));
      });
    }

    // Add orderBy
    if (orderByField) {
      queryRef = query(queryRef, orderBy(orderByField, orderDirection || 'asc'));
    }

    // Add limit
    if (limitCount) {
      queryRef = query(queryRef, limit(limitCount));
    }

    const querySnapshot = await getDocs(queryRef);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T);
  } catch (error) {
    console.error(`Error querying documents from ${collectionName}:`, error);
    return [];
  }
};

export { Timestamp };
export default db;
