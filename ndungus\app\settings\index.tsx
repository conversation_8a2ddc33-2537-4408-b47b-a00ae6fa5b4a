import { MaterialIcons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { clearAll, getObject } from '@/services/storage';

// Settings menu item interface
interface SettingsMenuItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
}

// Settings menu items
const SETTINGS_MENU: SettingsMenuItem[] = [
  {
    id: 'shop',
    title: 'Shop Settings',
    description: 'Configure your shop information, logo, and contact details',
    icon: 'store',
    route: '/settings/shop',
    color: Colors.primary,
  },
  {
    id: 'database',
    title: 'Database Settings',
    description: 'Configure Firebase and Cloudinary for cloud storage',
    icon: 'storage',
    route: '/settings/database',
    color: Colors.success,
  },
  {
    id: 'appearance',
    title: 'Appearance',
    description: 'Customize the app theme and appearance',
    icon: 'palette',
    route: '/settings/appearance',
    color: Colors.warning,
  },
  {
    id: 'notifications',
    title: 'Notifications',
    description: 'Configure notification settings',
    icon: 'notifications',
    route: '/settings/notifications',
    color: Colors.info,
  },
  {
    id: 'backup',
    title: 'Backup & Restore',
    description: 'Backup and restore your data',
    icon: 'backup',
    route: '/settings/backup',
    color: Colors.error,
  },
];

export default function SettingsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isSyncing, setIsSyncing] = useState(false);
  const [shopName, setShopName] = useState('Ndungus Shop');

  // Load shop name
  useEffect(() => {
    const loadShopName = async () => {
      try {
        const shopSettings = await getObject('shop_settings');

        if (shopSettings && shopSettings.name) {
          setShopName(shopSettings.name);
        }
      } catch (error) {
        console.error('Error loading shop name:', error);
      }
    };

    loadShopName();
  }, []);

  // Handle sync data
  const handleSyncData = async () => {
    setIsSyncing(true);

    try {
      // Sync data
      const result = await syncAllFirestoreData();

      Alert.alert(
        'Sync Complete',
        `Synced ${result.products} products, ${result.sales} sales, and ${result.operations} operations`
      );
    } catch (error: any) {
      Alert.alert('Sync Error', error.message || 'Failed to sync data');
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle clear data
  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'Are you sure you want to clear all data? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearAll();
              Alert.alert('Success', 'All data cleared successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data');
            }
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerContent}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <MaterialIcons
              name="arrow-back"
              size={24}
              color={colorScheme === 'dark' ? Colors.white : Colors.black}
            />
          </Pressable>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Settings
          </Text>
        </View>
        <Text
          style={[
            styles.shopName,
            { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
          ]}>
          {shopName}
        </Text>
      </GlassCard>

      {/* Settings Menu */}
      <View style={styles.menuContainer}>
        {SETTINGS_MENU.map((item) => (
          <Link key={item.id} href={item.route} asChild>
            <Pressable>
              <GlassCard style={styles.menuItem}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: item.color },
                  ]}>
                  <MaterialIcons name={item.icon as any} size={24} color={Colors.white} />
                </View>
                <View style={styles.menuItemContent}>
                  <Text
                    style={[
                      styles.menuItemTitle,
                      { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                    ]}>
                    {item.title}
                  </Text>
                  <Text
                    style={[
                      styles.menuItemDescription,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    {item.description}
                  </Text>
                </View>
                <MaterialIcons
                  name="chevron-right"
                  size={24}
                  color={colorScheme === 'dark' ? Colors.gray300 : Colors.gray700}
                />
              </GlassCard>
            </Pressable>
          </Link>
        ))}
      </View>

      {/* Actions */}
      <GlassCard style={styles.actionsCard}>
        <Text
          style={[
            styles.sectionTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Data Management
        </Text>

        <View style={styles.actionButtons}>
          <Button
            title="Sync Data"
            variant="primary"
            icon={<MaterialIcons name="sync" size={16} color={Colors.black} />}
            isLoading={isSyncing}
            onPress={handleSyncData}
            style={{ flex: 1, marginRight: Spacing.sm }}
          />

          <Button
            title="Clear Data"
            variant="danger"
            icon={<MaterialIcons name="delete" size={16} color={Colors.white} />}
            onPress={handleClearData}
            style={{ flex: 1 }}
          />
        </View>
      </GlassCard>

      {/* App Info */}
      <GlassCard style={styles.infoCard}>
        <Text
          style={[
            styles.appVersion,
            { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
          ]}>
          Version 1.0.0
        </Text>
        <Text
          style={[
            styles.appCopyright,
            { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
          ]}>
          © 2023 Ndungus Shop. All rights reserved.
        </Text>
      </GlassCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  shopName: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    marginTop: Spacing.xs,
  },
  menuContainer: {
    marginBottom: Spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    marginBottom: Spacing.sm,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 2,
  },
  menuItemDescription: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  actionsCard: {
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  infoCard: {
    padding: Spacing.md,
    alignItems: 'center',
  },
  appVersion: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  appCopyright: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
});
