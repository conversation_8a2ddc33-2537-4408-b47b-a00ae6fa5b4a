import { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Link from 'next/link';
import { FaPlus, FaArrowRight } from 'react-icons/fa';
import Layout from '@/components/layout/Layout';
import CaseCard from '@/components/cases/CaseCard';
import { useCases } from '@/hooks/useCases';
import { Case } from '@/types/case';

const Home: NextPage = () => {
  const [activeTab, setActiveTab] = useState<'popular' | 'recent'>('popular');
  const [cases, setCases] = useState<Case[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const { getPopularCases, getRecentCases } = useCases();
  
  useEffect(() => {
    const fetchCases = async () => {
      setIsLoading(true);
      
      let fetchedCases: Case[] = [];
      
      if (activeTab === 'popular') {
        fetchedCases = await getPopularCases();
      } else if (activeTab === 'recent') {
        fetchedCases = await getRecentCases();
      }
      
      setCases(fetchedCases);
      setIsLoading(false);
    };
    
    fetchCases();
  }, [activeTab]);
  
  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-16 md:py-24 relative">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full bg-black opacity-20"></div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl font-extrabold mb-6 leading-tight">
            Keeping Justice in the Public Eye
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-10 font-light">
            Track cases, contribute evidence, and ensure justice is served.
          </p>
          
          {/* Commented out search functionality for now
          <div className="max-w-2xl mx-auto">
            <div className="flex flex-col md:flex-row">
              <div className="relative flex-grow mb-4 md:mb-0">
                <input 
                  type="text" 
                  placeholder="Search cases by name, location, or keyword..." 
                  className="w-full py-4 px-6 rounded-full md:rounded-r-none text-gray-800 focus:outline-none focus:ring-2 focus:ring-red-500"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 md:hidden">
                  <FaSearch className="text-gray-400" />
                </div>
              </div>
              <button className="bg-blue-600 hover:bg-blue-700 text-white py-4 px-8 rounded-full md:rounded-l-none flex items-center justify-center">
                <FaSearch className="mr-2" />
                <span>Search</span>
              </button>
            </div>
          </div>
          */}
        </div>
      </section>
      
      {/* Case Categories */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="bg-white shadow-md rounded-full inline-flex mx-auto">
            <button 
              className={`px-6 py-3 rounded-full font-medium text-sm ${
                activeTab === 'popular' 
                  ? 'bg-gradient-to-r from-red-600 to-red-800 text-white' 
                  : 'text-gray-700 hover:bg-red-50'
              }`}
              onClick={() => setActiveTab('popular')}
            >
              Most Popular
            </button>
            <button 
              className={`px-6 py-3 rounded-full font-medium text-sm ${
                activeTab === 'recent' 
                  ? 'bg-gradient-to-r from-red-600 to-red-800 text-white' 
                  : 'text-gray-700 hover:bg-red-50'
              }`}
              onClick={() => setActiveTab('recent')}
            >
              Recently Added
            </button>
          </div>
        </div>
      </section>
      
      {/* Cases Grid */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white bg-opacity-70 rounded-xl h-96 animate-pulse"></div>
              ))}
            </div>
          ) : cases.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {cases.map((caseItem) => (
                  <CaseCard key={caseItem.id} caseData={caseItem} />
                ))}
              </div>
              
              <div className="mt-12 text-center">
                <button className="inline-flex items-center px-6 py-3 bg-white border border-red-200 rounded-full text-red-700 font-medium hover:bg-red-50 transition-colors">
                  Load More Cases
                  <FaArrowRight className="ml-2" />
                </button>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-gray-700 mb-4">No cases found</h3>
              <p className="text-gray-500 mb-8">Be the first to add a case to the platform.</p>
              <Link 
                href="/cases/create" 
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium hover:shadow-lg transition-all"
              >
                <FaPlus className="mr-2" />
                Create a Case
              </Link>
            </div>
          )}
        </div>
      </section>
      
      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-900 to-blue-700 text-white mt-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Make a Difference Today</h2>
          <p className="text-xl max-w-2xl mx-auto mb-8">
            Help keep important cases in the public eye and contribute to justice being served.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link 
              href="/cases/create" 
              className="px-8 py-4 bg-red-600 hover:bg-red-700 text-white rounded-full font-medium inline-flex items-center justify-center"
            >
              <FaPlus className="mr-2" />
              Create a Case
            </Link>
            <Link 
              href="/about" 
              className="px-8 py-4 bg-white text-blue-800 rounded-full font-medium inline-flex items-center justify-center"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Home;
