import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { generateCollaborativeSummary } from "@/lib/ai-summarizer";

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Find the event
    const event = await db.event.findUnique({
      where: {
        slug: params.slug,
        isPublished: true,
      },
      include: {
        notes: true,
        summary: true,
      }
    });

    if (!event) {
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 }
      );
    }

    // Check if summary already exists
    if (event.summary?.isGenerated) {
      return NextResponse.json({
        success: true,
        summary: event.summary,
        message: "Summary already exists"
      });
    }

    // Check if there are enough notes
    if (event.notes.length < 2) {
      return NextResponse.json(
        { success: false, error: "Need at least 2 notes to generate summary" },
        { status: 400 }
      );
    }

    // Generate the summary using AI
    const summaryResult = await generateCollaborativeSummary(
      event.notes.map(note => ({
        id: note.id,
        content: note.content,
        authorName: note.authorName || undefined,
        standoutThought: note.standoutThought || undefined,
      })),
      event.name,
      event.description || undefined
    );

    // Save or update the summary
    const summary = await db.summary.upsert({
      where: {
        eventId: event.id,
      },
      update: {
        content: JSON.stringify(summaryResult),
        htmlContent: summaryResult.htmlContent,
        keyTakeaways: summaryResult.keyTakeaways,
        standoutThoughts: summaryResult.standoutThoughts,
        unexpectedNuggets: summaryResult.unexpectedNuggets,
        questionsReflections: summaryResult.questionsReflections,
        mentions: summaryResult.mentions,
        communityQuotes: summaryResult.communityQuotes,
        isGenerated: true,
        updatedAt: new Date(),
      },
      create: {
        eventId: event.id,
        content: JSON.stringify(summaryResult),
        htmlContent: summaryResult.htmlContent,
        keyTakeaways: summaryResult.keyTakeaways,
        standoutThoughts: summaryResult.standoutThoughts,
        unexpectedNuggets: summaryResult.unexpectedNuggets,
        questionsReflections: summaryResult.questionsReflections,
        mentions: summaryResult.mentions,
        communityQuotes: summaryResult.communityQuotes,
        isGenerated: true,
      }
    });

    // TODO: If premium event, send email notifications to attendees

    return NextResponse.json({
      success: true,
      summary,
      message: "Summary generated successfully"
    });

  } catch (error) {
    console.error("Error generating summary:", error);
    return NextResponse.json(
      { success: false, error: "Failed to generate summary" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Find the event with its summary
    const event = await db.event.findUnique({
      where: {
        slug: params.slug,
        isPublished: true,
      },
      include: {
        summary: true,
        creator: {
          select: {
            name: true,
          }
        }
      }
    });

    if (!event) {
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 }
      );
    }

    if (!event.summary || !event.summary.isGenerated) {
      return NextResponse.json(
        { success: false, error: "Summary not yet generated" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      event: {
        id: event.id,
        name: event.name,
        description: event.description,
        date: event.date,
        creator: event.creator,
      },
      summary: event.summary,
    });

  } catch (error) {
    console.error("Error fetching summary:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
