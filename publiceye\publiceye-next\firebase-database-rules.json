{"rules": {".read": false, ".write": false, "users": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid", ".validate": "newData.hasChildren(['email', 'displayName', 'createdAt', 'updatedAt', 'role'])", "email": {".validate": "newData.isString() && newData.val().matches(/^[^@]+@[^@]+\\.[^@]+$/)"}, "displayName": {".validate": "newData.isString()"}, "photoURL": {".validate": "newData.isString() || newData.val() === null"}, "createdAt": {".validate": "newData.isString()"}, "updatedAt": {".validate": "newData.isString()"}, "role": {".validate": "newData.isString() && (newData.val() === 'user' || newData.val() === 'admin')"}, "followedCases": {".validate": "newData.isObject() || newData.val() === null"}, "contributedCases": {".validate": "newData.isObject() || newData.val() === null"}, "createdCases": {".validate": "newData.isObject() || newData.val() === null"}}}, "cases": {".read": "auth != null", ".write": "auth != null", "$caseId": {".read": true, ".write": "auth != null", "title": {".validate": "newData.isString() && newData.val().length > 0"}, "description": {".validate": "newData.isString() && newData.val().length > 0"}, "location": {".validate": "newData.isString()"}, "sourceUrl": {".validate": "newData.isString() || newData.val() == null"}, "currentStage": {".validate": "newData.isString() && (newData.val() === 'REPORTED' || newData.val() === 'INVESTIGATING' || newData.val() === 'EVIDENCE_COLLECTED' || newData.val() === 'VERIFIED' || newData.val() === 'RESOLVED' || newData.val() === 'CLOSED')"}, "createdBy": {".validate": "newData.isString() && newData.val() === auth.uid"}, "createdAt": {".validate": "newData.val() == now"}, "updatedAt": {".validate": "newData.val() == now"}, "mediaUrls": {".validate": "newData.isArray()"}, "followers": {"$uid": {".validate": "newData.isBoolean()"}}, "contributions": {"$contributionId": {".read": true, ".write": "auth != null", "userId": {".validate": "newData.isString() && newData.val() === auth.uid"}, "codeName": {".validate": "newData.isString() && newData.val().length > 0"}, "colorCode": {".validate": "newData.isString()"}, "stage": {".validate": "newData.isString() && (newData.val() === 'REPORTED' || newData.val() === 'INVESTIGATING' || newData.val() === 'EVIDENCE_COLLECTED' || newData.val() === 'VERIFIED' || newData.val() === 'RESOLVED' || newData.val() === 'CLOSED')"}, "description": {".validate": "newData.isString() && newData.val().length > 0"}, "mediaUrls": {".validate": "newData.isArray()"}, "sourceUrl": {".validate": "newData.isString() || newData.val() == null"}, "createdAt": {".validate": "newData.val() == now"}, "isVerified": {".validate": "newData.isBoolean()"}, "isFlagged": {".validate": "newData.isBoolean()"}, "flagReason": {".validate": "newData.isString() || newData.val() == null"}}}, "updates": {"$updateId": {".read": true, ".write": "auth != null", "stage": {".validate": "newData.isString() && (newData.val() === 'REPORTED' || newData.val() === 'INVESTIGATING' || newData.val() === 'EVIDENCE_COLLECTED' || newData.val() === 'VERIFIED' || newData.val() === 'RESOLVED' || newData.val() === 'CLOSED')"}, "description": {".validate": "newData.isString() && newData.val().length > 0"}, "mediaUrls": {".validate": "newData.isArray()"}, "sourceUrl": {".validate": "newData.isString() || newData.val() == null"}, "userId": {".validate": "newData.isString() && newData.val() === auth.uid"}, "contributorName": {".validate": "newData.isString() && newData.val().length > 0"}, "contributorColor": {".validate": "newData.isString()"}, "timestamp": {".validate": "newData.val() == now"}}}}}}}