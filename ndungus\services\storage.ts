/**
 * Storage service for offline data persistence
 * Uses AsyncStorage for simple key-value storage
 */

import AsyncStorage from './asyncStorage';

// Storage keys
export enum StorageKey {
  INVENTORY = 'inventory',
  ORDERS = 'orders',
  SALES = 'sales',
  SALES_HISTORY = 'sales_history',
  NOTES = 'notes',
  POTENTIAL_SALES = 'potential_sales',
  SYNC_QUEUE = 'sync_queue',
  USER_PROFILE = 'user_profile',
  APP_SETTINGS = 'app_settings',
  AUTH_STATE = 'auth_state',
  AUTH_TOKEN = 'auth_token',
}

// Get item from storage
export const getItem = async (key: string): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(key);
  } catch (error) {
    console.error('Error getting item from storage:', error);
    return null;
  }
};

// Set item in storage
export const setItem = async (key: string, value: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.error('Error setting item in storage:', error);
  }
};

// Remove item from storage
export const removeItem = async (key: string): Promise<void> => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing item from storage:', error);
  }
};

// Clear all storage
export const clearStorage = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing storage:', error);
  }
};

// Get object from storage
export const getObject = async <T>(key: string): Promise<T | null> => {
  try {
    const json = await AsyncStorage.getItem(key);
    return json ? JSON.parse(json) : null;
  } catch (error) {
    console.error('Error getting object from storage:', error);
    return null;
  }
};

// Set object in storage
export const setObject = async <T>(key: string, value: T): Promise<void> => {
  try {
    const json = JSON.stringify(value);
    await AsyncStorage.setItem(key, json);
  } catch (error) {
    console.error('Error setting object in storage:', error);
  }
};

// Get array from storage
export const getArray = async <T>(key: string): Promise<T[]> => {
  try {
    const json = await AsyncStorage.getItem(key);
    return json ? JSON.parse(json) : [];
  } catch (error) {
    console.error('Error getting array from storage:', error);
    return [];
  }
};

// Set array in storage
export const setArray = async <T>(key: string, value: T[]): Promise<void> => {
  try {
    const json = JSON.stringify(value);
    await AsyncStorage.setItem(key, json);
  } catch (error) {
    console.error('Error setting array in storage:', error);
  }
};

// Add item to array in storage
export const addToArray = async <T>(key: string, item: T): Promise<void> => {
  try {
    const array = await getArray<T>(key);
    array.push(item);
    await setArray(key, array);
  } catch (error) {
    console.error('Error adding item to array in storage:', error);
  }
};

// Update item in array in storage
export const updateInArray = async <T extends { id: string }>(
  key: string,
  item: T
): Promise<void> => {
  try {
    const array = await getArray<T>(key);
    const index = array.findIndex((i) => i.id === item.id);
    if (index !== -1) {
      array[index] = item;
      await setArray(key, array);
    }
  } catch (error) {
    console.error('Error updating item in array in storage:', error);
  }
};

// Remove item from array in storage
export const removeFromArray = async <T extends { id: string }>(
  key: string,
  id: string
): Promise<void> => {
  try {
    const array = await getArray<T>(key);
    const filtered = array.filter((item) => item.id !== id);
    await setArray(key, filtered);
  } catch (error) {
    console.error('Error removing item from array in storage:', error);
  }
};

// Add to sync queue
export const addToSyncQueue = async (
  action: 'create' | 'update' | 'delete',
  entity: string,
  data: any
): Promise<void> => {
  try {
    const queue = await getArray<any>(StorageKey.SYNC_QUEUE);
    queue.push({
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      action,
      entity,
      data,
      synced: false,
    });
    await setArray(StorageKey.SYNC_QUEUE, queue);
  } catch (error) {
    console.error('Error adding to sync queue:', error);
  }
};

// Get pending sync items
export const getPendingSyncItems = async (): Promise<any[]> => {
  try {
    const queue = await getArray<any>(StorageKey.SYNC_QUEUE);
    return queue.filter((item) => !item.synced);
  } catch (error) {
    console.error('Error getting pending sync items:', error);
    return [];
  }
};

// Mark sync item as synced
export const markSyncItemAsSynced = async (id: string): Promise<void> => {
  try {
    const queue = await getArray<any>(StorageKey.SYNC_QUEUE);
    const index = queue.findIndex((item) => item.id === id);
    if (index !== -1) {
      queue[index].synced = true;
      await setArray(StorageKey.SYNC_QUEUE, queue);
    }
  } catch (error) {
    console.error('Error marking sync item as synced:', error);
  }
};

// Clear synced items
export const clearSyncedItems = async (): Promise<void> => {
  try {
    const queue = await getArray<any>(StorageKey.SYNC_QUEUE);
    const pendingItems = queue.filter((item) => !item.synced);
    await setArray(StorageKey.SYNC_QUEUE, pendingItems);
  } catch (error) {
    console.error('Error clearing synced items:', error);
  }
};
