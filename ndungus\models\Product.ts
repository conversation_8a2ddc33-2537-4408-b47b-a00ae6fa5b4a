/**
 * Product model for inventory items
 */

export type ProductStatus = 'in-stock' | 'low-stock' | 'out-of-stock';

export interface Product {
  id: string;
  name: string;
  image?: string;
  code: string;
  category: string;
  buyingPrice: number;
  sellingPrice: number;
  units: number;
  status: ProductStatus;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductInput {
  name: string;
  image?: string;
  code: string;
  category: string;
  buyingPrice: number;
  sellingPrice: number;
  units: number;
  description?: string;
}

export interface UpdateProductInput extends Partial<CreateProductInput> {
  id: string;
}

/**
 * Calculate product status based on units
 * @param units Number of units in stock
 * @returns Product status
 */
export function calculateProductStatus(units: number): ProductStatus {
  if (units <= 0) {
    return 'out-of-stock';
  } else if (units <= 3) {
    return 'low-stock';
  } else {
    return 'in-stock';
  }
}

/**
 * Calculate profit for a product
 * @param product Product to calculate profit for
 * @returns Profit amount
 */
export function calculateProfit(product: Product): number {
  return product.sellingPrice - product.buyingPrice;
}

/**
 * Calculate profit margin for a product
 * @param product Product to calculate profit margin for
 * @returns Profit margin as a percentage
 */
export function calculateProfitMargin(product: Product): number {
  if (product.buyingPrice === 0) return 0;
  return ((product.sellingPrice - product.buyingPrice) / product.buyingPrice) * 100;
}

/**
 * Calculate total value of product inventory
 * @param product Product to calculate inventory value for
 * @returns Total inventory value
 */
export function calculateInventoryValue(product: Product): number {
  return product.buyingPrice * product.units;
}
