<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> | Trusted Babysitter & Elderly Care in Nairobi</title>
    <meta name="description" content="Trusted babysitting & elderly care from the heart in Nairobi. Book your free 30-minute consultation today.">

    <script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HealthAndBeautyBusiness",
  "name": "<PERSON>s",
  "alternateName": "Sharon Cares Babysitting & Elderly Support",
  "url": "https://sharoncares.co.ke",
  "logo": "https://sharoncares.co.ke/assets/SC_big-removebg.png", 
  "image": "https://sharoncares.co.ke/assets/sc_preview.png",
  "description": "Premium in-home babysitting and elderly care services in Nairobi, Kenya. Trusted by working families and professionals.",
  "telephone": "+254792660829",
  "priceRange": "$$$",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Nairobi",
    "addressLocality": "Nairobi",
    "addressCountry": "KE"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": -1.286389,
    "longitude": 36.817223
  },
  "openingHours": "Mo-Su 08:00-18:00",
  "areaServed": {
    "@type": "Place",
    "name": "Nairobi and surrounding suburbs"
  },
  "founder": {
    "@type": "Person",
    "name": "Sharon N."
  },
  "sameAs": [

    "https://sharoncares.co.ke"
  ]
}
</script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@400;500;700&family=Fredoka+One&family=Nunito:wght@400;600;700&display=swap"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="assets/SC_big-removebg.png" type="image/x-icon">
    <style>
        :root {
            --primary: #FF7E5F;
            /* Warm coral - friendly and energetic */
            --secondary: #4ECDC4;
            /* Soft teal - calming and trustworthy */
            --accent: #FFB347;
            /* Sunny yellow - cheerful and warm */
            --light: #F7F9FC;
            /* Very light blue - clean background */
            --dark: #2D3748;
            /* Dark blue-gray - professional text */
            --soft-pink: #FFB7B2;
            /* For playful accents */
            --soft-green: #A2D9A4;
            /* For natural/health associations */
        }

        /* html,
        body {
            overflow-x: hidden;
            max-width: 100%;
        }

        img,
        iframe,
        video {
            max-width: 100%;
            height: auto;
        }

        .container {
            max-width: 100%;
            box-sizing: border-box;
        } */

        body {
            font-family: 'Nunito', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
        }

        h1,
        h2,
        h3,
        h4 {
            font-family: 'Comfortaa', cursive;
            font-weight: 700;
            color: var(--dark);
        }

        .logo-font {
            font-family: 'Fredoka One', cursive;
        }

        .navbar {
            background-color: white !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-family: 'Fredoka One', cursive;
            font-size: 1.8rem;
            color: var(--primary) !important;
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 50px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .nav-link {
            font-family: 'Comfortaa', cursive;
            font-weight: 500;
            color: var(--dark) !important;
            margin: 0 10px;
            padding: 8px 15px !important;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover,
        .nav-link:focus {
            background-color: rgba(255, 126, 95, 0.1);
            color: var(--primary) !important;
        }

        .hero-section {
            background: linear-gradient(135deg, rgba(255, 126, 95, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-text {
            max-width: 600px;
        }

        .hero-title {
            font-size: 3.2rem;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-title span {
            color: var(--primary);
        }

        .hero-tagline {
            font-size: 1.4rem;
            color: var(--dark);
            margin-bottom: 30px;
        }

        .hero-image {
            max-width: 400px;
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border: 5px solid white;
            transform: rotate(3deg);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotate(3deg);
            }

            50% {
                transform: translateY(-15px) rotate(3deg);
            }

            100% {
                transform: translateY(0px) rotate(3deg);
            }
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-family: 'Comfortaa', cursive;
            font-weight: 700;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-primary:hover {
            background-color:  ;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(255, 126, 95, 0.3);
        }

        .btn-secondary {
            background-color: var(--secondary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-family: 'Comfortaa', cursive;
            font-weight: 700;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background-color: #3ABBB3;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(78, 205, 196, 0.3);
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 50px;
            position: relative;
            display: inline-block;
        }

        .section-title:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            bottom: -10px;
            left: 0;
            border-radius: 3px;
        }

        .about-section {
            background-color: white;
            position: relative;
            overflow: hidden;
        }
        .form-message {
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 10px;
  font-size: 14px;
  font-family: 'Nunito', sans-serif;
  display: none; /* Hidden by default */
  max-width: 100%;
  word-break: break-word;
}

/* Success State */
.form-message.success {
  display: block;
  background: #DCE5DC; /* Soft green */
  color: #2D3748;
  border-left: 3px solid #4ECDC4; /* Teal accent */
}

/* Error State */
.form-message.error {
  display: block;
  background: #F9E5E5; /* Soft pink */
  color: #2D3748;
  border-left: 3px solid #FF7E5F; /* Coral accent */
}
        .about-section:before {
            content: '';
            position: absolute;
            width: 300px;
            height: 300px;
            background-color: var(--soft-pink);
            border-radius: 50%;
            top: -150px;
            right: -150px;
            opacity: 0.3;
        }

        .about-section:after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background-color: var(--soft-green);
            border-radius: 50%;
            bottom: -100px;
            left: -100px;
            opacity: 0.3;
        }

        .about-image {
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            max-width: 100%;
            height: auto;
            position: relative;
            z-index: 1;
        }

        .service-card {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            height: 100%;
            transition: all 0.3s ease;
            border-top: 5px solid var(--primary);
            position: relative;
            overflow: hidden;
        }

        .service-card:after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            background-color: var(--accent);
            border-radius: 50%;
            top: -50px;
            right: -50px;
            opacity: 0.1;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .service-icon {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
        }

        .service-title {
            font-size: 1.8rem;
            margin-bottom: 15px;
        }

        .testimonial-section {
            background: linear-gradient(135deg, rgba(255, 179, 71, 0.1) 0%, rgba(162, 217, 164, 0.1) 100%);
            position: relative;
            overflow: hidden;
        }

        .testimonial-section:before {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background-color: var(--accent);
            border-radius: 50%;
            top: -100px;
            left: -100px;
            opacity: 0.2;
        }

        .testimonial-card {
            background-color: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            position: relative;
        }

        .testimonial-card:before {
            content: '"';
            font-family: 'Comfortaa', cursive;
            font-size: 5rem;
            color: rgba(255, 126, 95, 0.1);
            position: absolute;
            top: 10px;
            left: 15px;
            line-height: 1;
        }

        .testimonial-text {
            position: relative;
            z-index: 1;
            font-style: italic;
            margin-bottom: 20px;
        }

        .testimonial-author {
            font-weight: 700;
            color: var(--primary);
        }

        .contact-section {
            background-color: white;
        }

        .contact-form .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #ddd;
            background-color: var(--light);
        }

        .contact-form .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.25rem rgba(255, 126, 95, 0.25);
        }

        .contact-info {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .contact-icon {
            font-size: 1.5rem;
            color: var(--primary);
            margin-right: 10px;
        }

        .booking-section {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1) 0%, rgba(255, 179, 71, 0.1) 100%);
        }

        .booking-card {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }

        .footer {
            background-color: var(--dark);
            color: white;
            padding: 40px 0 20px;
        }

        .footer-logo {
            font-family: 'Fredoka One', cursive;
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 20px;
        }

        .footer-link {
            color: #ccc;
            transition: all 0.3s ease;
            display: block;
            margin-bottom: 10px;
        }

        .footer-link:hover {
            color: var(--primary);
            text-decoration: none;
            transform: translateX(5px);
        }

        .social-icon {
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
            transition: all 0.3s ease;
        }

        .social-icon:hover {
            color: var(--primary);
            transform: scale(1.2);
        }

        /* Playful elements */
        .bubble {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 179, 71, 0.2);
            z-index: 0;
        }

        .wiggle {
            animation: wiggle 2s ease-in-out infinite;
        }

        @keyframes wiggle {
            0% {
                transform: rotate(0deg);
            }

            25% {
                transform: rotate(3deg);
            }

            75% {
                transform: rotate(-3deg);
            }

            100% {
                transform: rotate(0deg);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
  .form-message {
    font-size: 13px;
    padding: 6px 10px;
    margin-top: 8px;
  }

            .hero-tagline {
                font-size: 1.2rem;
            }

            .navbar-brand img {
                height: 40px;
            }

            .section {
                padding: 60px 0;
            }

            .section-title {
                font-size: 2rem;
            }
        }

        .no-bubble-overlap {
            position: relative;
            z-index: 2;
            /* Higher than bubbles (z-index: 0) */
        }

        /* Update bubble styling */
        .bubble {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 179, 71, 0.2);
            z-index: 0;
            pointer-events: none;
            /* Allows clicking through bubbles */
        }
    </style>
    <script src="https://assets.calendly.com/assets/external/widget.js" async></script>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="assets/SC_big-removebg.png" alt="Sharon Cares Logo" class="wiggle">
                <span class="align-middle logo-font">Sharon Cares</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#booking">Booking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="bubble"
            style="width: 150px; height: 150px; top: 10%; left: 5%; background-color: rgba(78,205,196,0.2);"></div>
        <div class="bubble"
            style="width: 200px; height: 200px; bottom: 10%; right: 5%; background-color: rgba(255,179,71,0.2);"></div>

        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-text">
                        <h1 class="hero-title">Compassionate Care <span>for All Ages</span></h1>
                        <p class="hero-tagline">Trusted babysitting & senior care from the heart</p>
                        <div class="d-flex gap-3">
                            <a href="#services" class="btn btn-primary">Babysitting</a>
                            <a href="#services" class="btn btn-secondary">Home Care</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 d-flex justify-content-center mt-5 mt-lg-0">
                    <img src="assets/Sharon_alone.png" alt="Sharon smiling"
                        class="hero-image img-fluid no-bubble-overlap">
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section section">
        <div class="container">
            <h2 class="section-title text-center">Hi, I'm Sharon!</h2>
            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <img src="assets/Sharon_child.png" alt="Sharon caring for someone"
                        class="about-image no-bubble-overlap img-fluid">
                </div>
                <div class="col-lg-6">
                    <p class="lead">I've always loved caring for others — from little ones full of energy to seniors who
                        deserve a friend.</p>
                    <p>With experience in childcare and elderly care, I bring patience, compassion, and
                        a big heart to every family I work with. My approach is simple: treat every individual with the
                        same care and respect I would want for my own family members.</p>

                    <p>When I'm not caring for others, you can find me trying recipes, baking cookies, or volunteering
                        at my church's choir.</p>
                    <div class="mt-4">
                        <h4 class="mb-3">My Certifications:</h4>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> Certified Nurse
                                Assistant (CNA)</li>
                            <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> Basic Life
                                Support (BLS)</li>
                            <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> Red Cross First
                                Aid and Emergency Response</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section section">
        <div class="container">
            <h2 class="section-title text-center">How I Can Help</h2>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-baby"></i>
                        </div>
                        <h3 class="service-title">Babysitting</h3>
                        <p>Fun, safe care for your little ones with engaging activities that stimulate their development
                            while keeping them safe and happy.</p>
                        <button class="btn btn-primary mt-3" data-bs-toggle="collapse"
                            data-bs-target="#babysittingDetails">Learn More</button>
                        <div class="collapse mt-3" id="babysittingDetails">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Diaper changing and
                                    potty training</li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Educational
                                    playtime & activities</li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Homework assistance
                                </li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Meal preparation
                                </li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Bedtime routines
                                </li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Overnight care
                                    available</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <h3 class="service-title">Elderly Care</h3>
                        <p>Warm, reliable support for your loved ones with compassionate companionship and assistance
                            with daily activities.</p>
                        <button class="btn btn-primary mt-3" data-bs-toggle="collapse"
                            data-bs-target="#elderlyDetails">Learn More</button>
                        <div class="collapse mt-3" id="elderlyDetails">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Personal hygiene
                                    assistance</li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Companionship &
                                    conversation</li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Medication
                                    reminders</li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Light housekeeping
                                </li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Meal preparation
                                </li>
                                <li><i class="fas fa-check me-2" style="color: var(--primary);"></i> Errands & shopping
                                </li>

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-5">
                <a href="#contact" class="btn btn-primary btn-lg">Need Care? Let's Chat!</a>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonial-section section">
        <div class="container">
            <h2 class="section-title text-center">What Families Say</h2>
            <ul class="nav nav-tabs justify-content-center mb-4" id="testimonialTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#babysitting">Babysitting</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#elderly">Elderly Care</a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade show active" id="babysitting">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">Sharon has been caring for our twins since they were 9
                                    months old. She's patient, creative, and the kids absolutely adore her. We couldn't
                                    ask for better care!</p>
                                <p class="testimonial-author">— Jen M.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">Finding Sharon was a blessing. She engages our son with
                                    educational activities and even helps with his homework. He's always excited when
                                    "Miss Sharon" is coming.</p>
                                <p class="testimonial-author">— Michael T.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">As a single parent, I rely heavily on Sharon's flexibility
                                    and reliability. She's become like family to us over the past three years.</p>
                                <p class="testimonial-author">— Sarah K.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="elderly">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">Sharon's care for my mother with dementia has been
                                    exceptional. She's patient, kind, and knows exactly how to redirect when needed. Mom
                                    lights up when Sharon arrives.</p>
                                <p class="testimonial-author">— Tom R.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">After my husband's stroke, Sharon provided the perfect
                                    balance of assistance and encouragement. She helped him regain confidence with daily
                                    activities.</p>
                                <p class="testimonial-author">— Margaret L.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p class="testimonial-text">Sharon doesn't just care for my dad - she truly cares about
                                    him. Their conversations and outings have improved his mood and outlook
                                    tremendously.</p>
                                <p class="testimonial-author">— David P.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Submission Form -->
            <div class="row mt-5">
                <div class="col-lg-8 mx-auto">
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="text-center mb-4">Share Your Experience</h3>
                            <form id="testimonialForm">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" id="name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="serviceType" class="form-label">Service Type</label>
                                    <select class="form-select" id="serviceType">
                                        <option value="babysitting">Babysitting</option>
                                        <option value="elderly">Elderly Care</option>
                                    </select>
                                </div>
                                <!-- <div id="message-counter">0/500 characters</div> -->
                                <div class="mb-3">
                                    <label for="testimonial" class="form-label">Your Testimonial</label>
                                    <textarea class="form-control" id="testimonial" rows="4" required></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">Submit Testimonial</button>
                                    <div class="form-message" id="testimonialMessage"></div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section id="booking" class="booking-section section">
        <div class="container">
            <h2 class="section-title text-center">Schedule a Consultation</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="booking-card">
                        <div class="row align-items-center">
                            <div class="col-md-6 mb-4 mb-md-0">
                                <h3>Ready to Get Started?</h3>
                                <p>Book a free 30-minute consultation to discuss your family's needs and how I can help.
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> No
                                        obligation</li>
                                    <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> Discuss
                                        care options</li>
                                    <li><i class="fas fa-check-circle me-2" style="color: var(--primary);"></i> Get all
                                        your questions answered</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <!-- Calendly placeholder-->
                                <!-- <div class="calendly-placeholder" style="background-color: #f5f5f5; border-radius: 10px; padding: 20px; text-align: center;">
                                    <i class="fas fa-calendar-alt fa-3x mb-3" style="color: var(--primary);"></i>
                                    <p>Book your consultation through Calendly</p>
                                    <a href="#" class="btn btn-primary">View Availability</a>
                                </div> -->
                                <div class="calendly-inline-widget" data-url="https://calendly.com/sharonndungu73"
                                    style="min-width:320px;height:630px;"></div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section section">
        <div class="container">
            <h2 class="section-title text-center">Let's Connect!</h2>
            <div class="row">
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <div class="contact-form">
                        <form id="contactForm">
                            <div class="mb-3">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" required pattern="[^@\s]+@[^@\s]+\.[^@\s]+" class="form-control"
                                    id="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone">
                            </div>
                            <div class="mb-3">
                                <label for="service" class="form-label">Service Needed</label>
                                <select class="form-select" id="service">
                                    <option value="">Select a service</option>
                                    <option value="babysitting">Babysitting</option>
                                    <option value="elderly-care">Elderly Care</option>
                                    <option value="both">Both</option>
                                    <option value="consultation">Consultation</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Message</button>
                            <div class="form-message" id="contactMessage"></div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-info h-100">
                        <h3 class="mb-4">Contact Information</h3>
                        <div class="d-flex align-items-start mb-4">
                            <i class="contact-icon fas fa-phone-alt"></i>
                            <div>
                                <h5>Phone</h5>
                                <p class="mb-0">0792660829</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-4">
                            <i class="contact-icon fas fa-envelope"></i>
                            <div>
                                <h5>Email</h5>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                        <div class="d-flex align-items-start mb-4">
                            <i class="contact-icon fas fa-map-marker-alt"></i>
                            <div>
                                <h5>Service Area</h5>
                                <p class="mb-0">Nairobi, Kenya</p>
                            </div>
                        </div>
                        <div class="mt-5">
                            <h5 class="mb-3">Connect With Me</h5>
                            <!-- <a href="#" class="btn btn-outline-secondary me-2"><i class="fab fa-facebook-f me-2"></i>Facebook</a>
                            <a href="#" class="btn btn-outline-secondary me-2"><i class="fab fa-instagram me-2"></i>Instagram</a> -->
                            <a href="https://wa.me/0792660829?text=Hi%20Sharon,%20I%20need%20information%20about%20your%20services"
                                class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>Chat on WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <div class="footer-logo">Sharon Cares</div>
                    <p class="text-light">Providing compassionate, personalized care for your family's most precious
                        members.</p>
                    <!-- <div class="mt-3">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div> -->
                </div>
                <div class="col-lg-2 col-md-4 mb-4 mb-md-0">
                    <h5 class="text-white mb-4">Quick Links</h5>
                    <a href="#home" class="footer-link">Home</a>
                    <a href="#about" class="footer-link">About</a>
                    <a href="#services" class="footer-link">Services</a>
                    <a href="#testimonials" class="footer-link">Testimonials</a>
                    <a href="#contact" class="footer-link">Contact</a>
                </div>
                <div class="col-lg-3 col-md-4 mb-4 mb-md-0">
                    <h5 class="text-white mb-4">Services</h5>
                    <a href="#services" class="footer-link">Babysitting</a>
                    <a href="#services" class="footer-link">Elderly Care</a>
                    <a href="#booking" class="footer-link">Consultation</a>
                    <a href="#contact" class="footer-link">Emergency Care</a>
                </div>
                <div class="col-lg-3 col-md-4">
                    <h5 class="text-white mb-4">Contact</h5>
                    <p class="text-light mb-2"><i class="fas fa-phone-alt me-2"></i> +254792660829</p>
                    <p class="text-light mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p class="text-light"><i class="fas fa-map-marker-alt me-2"></i> Nairobi, Kenya</p>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2025 Sharon Cares. All rights reserved.</p>
                </div>
                <!-- <div class="col-md-6 text-center text-md-end">
                    <a href="#" class="footer-link me-3">Privacy Policy</a>
                    <a href="#" class="footer-link">Terms of Service</a>
                </div> -->
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Create floating bubbles
        function createBubbles() {
            const colors = [
                'rgba(255, 126, 95, 0.2)',
                'rgba(78, 205, 196, 0.2)',
                'rgba(255, 179, 71, 0.2)',
                'rgba(255, 183, 178, 0.2)',
                'rgba(162, 217, 164, 0.2)'
            ];

            const sections = document.querySelectorAll('section:not(.contact-section, .footer)');

            sections.forEach(section => {
                // Get all protected images in this section
                const protectedImages = section.querySelectorAll('.no-bubble-overlap');
                const protectedAreas = [];

                // Calculate protected zones
                protectedImages.forEach(img => {
                    const rect = img.getBoundingClientRect();
                    protectedAreas.push({
                        top: rect.top - 50,    // 50px buffer
                        bottom: rect.bottom + 50,
                        left: rect.left - 50,
                        right: rect.right + 50
                    });
                });

                // Create bubbles (fewer for better spacing)
                const bubbleCount = Math.floor(Math.random() * 2) + 2; // 2-3 bubbles

                for (let i = 0; i < bubbleCount; i++) {
                    const bubble = document.createElement('div');
                    bubble.className = 'bubble';

                    const size = Math.floor(Math.random() * 80) + 50; // 50-130px
                    const color = colors[Math.floor(Math.random() * colors.length)];

                    bubble.style.width = `${size}px`;
                    bubble.style.height = `${size}px`;
                    bubble.style.backgroundColor = color;

                    // Find safe position
                    let safePosition = false;
                    let attempts = 0;
                    let top, left;

                    while (!safePosition && attempts < 20) {
                        attempts++;
                        top = Math.random() * 80 + 10; // 10%-90%
                        left = Math.random() * 80 + 10;

                        // Check if this position overlaps protected areas
                        safePosition = true;
                        const bubbleTop = window.innerHeight * (top / 100);
                        const bubbleLeft = window.innerWidth * (left / 100);

                        for (const area of protectedAreas) {
                            if (bubbleTop > area.top && bubbleTop < area.bottom &&
                                bubbleLeft > area.left && bubbleLeft < area.right) {
                                safePosition = false;
                                break;
                            }
                        }
                    }

                    if (safePosition) {
                        bubble.style.top = `${top}%`;
                        bubble.style.left = `${left}%`;

                        // Animation
                        const duration = Math.random() * 10 + 10;
                        bubble.style.animation = `float ${duration}s ease-in-out infinite`;

                        section.appendChild(bubble);
                    }
                }
            });
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function () {
            createBubbles();

            // Add wiggle animation to service icons
            document.querySelectorAll('.service-icon').forEach(icon => {
                icon.classList.add('wiggle');
            });
        });
    </script>
    <script type="module" src="js/firebase.js"></script>
    <script type="module" src="js/app.js"></script>

</body>

</html>