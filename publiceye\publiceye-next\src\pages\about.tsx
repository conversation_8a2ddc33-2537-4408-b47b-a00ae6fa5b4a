import React, { useState } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import Layout from '@/components/layout/Layout';
import { FaPlay, FaExclamationTriangle } from 'react-icons/fa';
import Cookies from 'js-cookie';

const LandingPage: NextPage = () => {
  const router = useRouter();
  const [activeVideo, setActiveVideo] = useState(0);
  const [showGraphicWarning, setShowGraphicWarning] = useState(true);

  const videos = [
    {
      id: '06MYDzo94no',
      start: 36,
      title: 'The Reality of Injustice',
      description: 'A glimpse into the harsh reality of unchecked power'
    },
    {
      id: 'mJ7Hv4xjhUM',
      title: 'Graphic Content Warning',
      description: 'This video contains graphic content that may be disturbing'
    }
  ];

  const handleJoinWatch = () => {
    // Set cookie to remember user has seen the landing page
    Cookies.set('hasSeenLanding', 'true', { expires: 365 }); // Expires in 1 year
    router.push('/');
  };

  return (
    <Layout>
      {/* Hero Section with Videos */}
      <section className="relative min-h-screen bg-gradient-to-b from-gray-900 to-black text-white">
        <div className="container mx-auto px-4 py-16">
          {/* Video Carousel */}
          <div className="relative max-w-4xl mx-auto mb-12">
            <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden shadow-2xl">
              {showGraphicWarning && activeVideo === 1 ? (
                <div className="absolute inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center p-8 text-center z-20">
                  <FaExclamationTriangle className="text-red-500 text-5xl mb-4" />
                  <h3 className="text-2xl font-bold mb-4">Graphic Content Warning</h3>
                  <p className="text-lg mb-6">
                    The following video contains graphic content that may be disturbing.
                    Viewer discretion is advised.
                  </p>
                  <button
                    onClick={() => setShowGraphicWarning(false)}
                    className="px-8 py-3 bg-red-600 hover:bg-red-700 rounded-full font-medium transition-colors"
                  >
                    I Understand, Proceed
                  </button>
                </div>
              ) : (
                <iframe
                  src={`https://www.youtube.com/embed/${videos[activeVideo].id}?si=p0bJ5YfKKptCQouJ&start=${videos[activeVideo].start || 0}&autoplay=1`}
                  title={videos[activeVideo].title}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                  className="w-full h-full"
                />
              )}
            </div>

            {/* Video Navigation Dots */}
            <div className="flex justify-center gap-4 mt-6">
              {videos.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setActiveVideo(index);
                    if (index === 1) setShowGraphicWarning(true);
                  }}
                  className={`w-3 h-3 rounded-full transition-all ${
                    activeVideo === index ? 'bg-red-600 scale-125' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Mission Statement */}
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              When Injustice Fades, We Remember
            </h1>
            <p className="text-xl leading-relaxed mb-8 text-gray-300">
              In a world where powerful perpetrators slip through the cracks of justice,
              where cases are buried in bureaucratic delays, and where public outrage
              fades with time - we stand as the unwavering memory of these injustices.
              Public Eye is not just a platform; it's a collective voice that refuses
              to let these stories die in silence.
            </p>
            <p className="text-xl leading-relaxed mb-8 text-gray-300">
              We track. We document. We remember. Because forgetting is not an option
              when justice remains unserved. Every case, every victim, every perpetrator
              - we keep them in the public eye until justice is served.
            </p>
            <button
              onClick={handleJoinWatch}
              className="px-8 py-4 bg-gradient-to-r from-red-600 to-red-800 rounded-full text-lg font-medium hover:from-red-700 hover:to-red-900 transition-all transform hover:scale-105"
            >
              Join the Watch
            </button>
          </div>

          {/* Video Credits */}
          <div className="text-center text-sm text-gray-400">
            <p>Video Credits: Original content from respective sources</p>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default LandingPage; 