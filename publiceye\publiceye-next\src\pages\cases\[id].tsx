import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { FaMapMarkerAlt, FaCalendarAlt, FaEye, FaEyeSlash, FaShareAlt, FaHeart, FaPlus } from 'react-icons/fa';
import { format } from 'date-fns';
import Layout from '@/components/layout/Layout';
import CaseTimeline from '@/components/cases/CaseTimeline';
import AddContribution from '@/components/cases/AddContribution';
import { useCases } from '@/hooks/useCases';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/components/ui/Toast';
import { Case, GroupedContributions, CaseStage, Contribution } from '@/types/case';
import ProgressUpdateModal from '@/components/cases/ProgressUpdateModal';
import CaseUpdates from '@/components/cases/CaseUpdates';
import { getDatabase } from 'firebase-admin/database';

const CaseDetails: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;

  const [caseData, setCaseData] = useState<Case | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [groupedContributions, setGroupedContributions] = useState<GroupedContributions>({});
  const [followerCount, setFollowerCount] = useState(0);
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);

  const { getCaseById, groupContributionsByStage, addContribution } = useCases();
  const { user } = useAuth();
  const { showToast } = useToast();

    const fetchCase = async () => {
    if (!id) return;
    
    setIsLoading(true);
    try {
      const fetchedCase = await getCaseById(id as string);

      if (fetchedCase) {
        setCaseData(fetchedCase);

        // Group contributions by stage
        if (fetchedCase.contributions) {
          // Convert contributions object to array if it's not already
          const contributionsArray = Array.isArray(fetchedCase.contributions) 
            ? fetchedCase.contributions 
            : Object.values(fetchedCase.contributions);
            
          const grouped = groupContributionsByStage(contributionsArray);
          setGroupedContributions(grouped);
        }

        // Check if user is following this case
        if (user && user.followedCases && typeof user.followedCases === 'object') {
          const followedCases = user.followedCases as Record<string, boolean>;
          setIsFollowing(!!followedCases[fetchedCase.id]);
        }

        setFollowerCount(Object.keys(fetchedCase.followers || {}).length);
      }
    } catch (error) {
      console.error('Error fetching case:', error);
      showToast('Error loading case details', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch case data
  useEffect(() => {
    fetchCase();
  }, [id, user]);

  // Handle follow/unfollow
  const handleFollow = async () => {
    if (!user) {
      // Redirect to sign in if not logged in
      showToast('Please sign in to follow this case', 'info');
      router.push('/auth/signin');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/cases/${id}/follow`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          action: isFollowing ? 'unfollow' : 'follow'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setIsFollowing(data.isFollowing);
        setFollowerCount(data.followersCount);
        showToast(
          data.isFollowing ? 'Following case' : 'Unfollowed case',
          'success'
        );
      }
    } catch (error) {
      console.error('Error following case:', error);
      showToast('Error updating follow status', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle share
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: caseData?.title || 'Public Eye Case',
        text: caseData?.description || 'Check out this case on Public Eye',
        url: window.location.href,
      }).then(() => {
        showToast('Case shared successfully!', 'success');
      }).catch((error) => {
        console.error('Error sharing:', error);
      });
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href);
      showToast('Link copied to clipboard!', 'success');
    }
  };

  // Handle contribution added
  const handleContributionAdded = async () => {
    if (id) {
      try {
        // Show loading toast
        showToast('Refreshing case data...', 'info');

        // Refresh case data
        const refreshedCase = await getCaseById(id as string);

        if (refreshedCase) {
          setCaseData(refreshedCase);

          // Re-group contributions
          if (refreshedCase.contributions && Object.keys(refreshedCase.contributions).length > 0) {
            const grouped = groupContributionsByStage(refreshedCase.contributions);
            setGroupedContributions(grouped);
          }

          // Show success toast
          showToast('Case updated successfully!', 'success');
        } else {
          showToast('Failed to refresh case data', 'error');
        }
      } catch (error) {
        console.error('Error refreshing case:', error);
        showToast('Error refreshing case data', 'error');
      }
    }
  };

  // Format date
  const formatDate = (dateInput: string | number | Date): string => {
    try {
      const date = typeof dateInput === 'number' ? new Date(dateInput) : new Date(dateInput);
      return format(date, 'MMMM d, yyyy');
    } catch (error) {
      return 'Unknown date';
    }
  };

  const handleProgressUpdate = async (data: { 
    stage: CaseStage; 
    description: string; 
    mediaUrls: string[];
    sourceUrl?: string;
  }) => {
    try {
      const response = await fetch(`/api/cases/${id}/progress`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          userId: user?.id
        })
      });

      if (response.ok) {
        setIsProgressModalOpen(false);
        showToast('Progress updated successfully', 'success');
        fetchCase(); // Refresh case data
      }
    } catch (error) {
      console.error('Error adding progress update:', error);
      showToast('Error updating progress', 'error');
    }
  };

  const handleAddContribution = async (contribution: Omit<Contribution, 'id' | 'createdAt' | 'isVerified' | 'isFlagged' | 'flagReason'>, mediaFiles: File[]) => {
    if (!user) {
      showToast('Please sign in to add a contribution', 'error');
      return;
    }

    try {
      const success = await addContribution(id as string, {
        ...contribution,
        userId: user.id
      }, mediaFiles);

      if (success) {
        showToast('Contribution added successfully', 'success');
        // Refresh case data
        await fetchCase();
      } else {
        showToast('Failed to add contribution', 'error');
      }
    } catch (error) {
      console.error('Error adding contribution:', error);
      showToast('Failed to add contribution', 'error');
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-md w-3/4 mb-6"></div>
            <div className="h-80 bg-gray-200 rounded-lg mb-8"></div>
            <div className="h-8 bg-gray-200 rounded-md w-1/2 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded-md w-full mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-md w-full mb-2"></div>
            <div className="h-4 bg-gray-200 rounded-md w-3/4 mb-8"></div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!caseData) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12 text-center">
          <h2 className="text-2xl font-bold text-gray-700 mb-4">Case not found</h2>
          <p className="text-gray-500 mb-8">The case you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium"
          >
            Back to Home
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`${caseData.title} - Public Eye`}>
      {/* Case Header */}
      <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{caseData.title}</h1>

          <div className="flex flex-wrap gap-6 mb-4">
            {caseData.location && (
              <div className="flex items-center">
                <FaMapMarkerAlt className="mr-2" />
                <span>{caseData.location}</span>
              </div>
            )}

            <div className="flex items-center">
              <FaCalendarAlt className="mr-2" />
              <span>Reported {formatDate(caseData.createdAt)}</span>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Case Image */}
            {caseData.mediaUrls && caseData.mediaUrls.length > 0 && (
              <div className="relative w-full h-[500px] mb-6 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={caseData.mediaUrls[0]}
                  alt={caseData.title}
                  fill
                  style={{ objectFit: 'cover' }}
                  sizes="(max-width: 768px) 100vw, 1200px"
                  priority
                  className="hover:scale-105 transition-transform duration-300"
                />
              </div>
            )}

            {/* Case Actions */}
            <div className="flex flex-wrap gap-4 mb-8">
              <button
                onClick={handleFollow}
                className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium ${
                  isFollowing
                    ? 'bg-white text-red-700 border border-red-200'
                    : 'bg-gradient-to-r from-red-600 to-red-800 text-white'
                }`}
              >
                <FaHeart className={isFollowing ? 'text-red-600' : 'text-gray-400'} />
                <span>{isFollowing ? 'Unfollow' : 'Follow Case'}</span>
                <span className="ml-1">({followerCount})</span>
              </button>

              <button
                onClick={handleShare}
                className="flex items-center gap-2 px-6 py-3 bg-white text-gray-700 rounded-full font-medium border border-gray-200"
              >
                <FaShareAlt />
                <span>Share</span>
              </button>
            </div>

            {/* Case Description */}
            <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-md p-6 mb-8">
              <h2 className="text-xl font-bold mb-4">Case Description</h2>
              <div className="max-w-none text-gray-700 leading-relaxed">
                <p>{caseData.description}</p>

                {caseData.sourceUrl && (
                  <div className="mt-4">
                    <a
                      href={caseData.sourceUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline inline-flex items-center"
                    >
                      <span>Source</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Case Timeline */}
            <CaseTimeline
              currentStage={caseData.currentStage}
              groupedContributions={groupedContributions}
            />

            {/* Case Updates */}
            {caseData.updates && Object.keys(caseData.updates).length > 0 && (
              <CaseUpdates updates={caseData.updates} />
            )}
          </div>

          <div>
            {/* Add Contribution */}
            <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-md p-6 mb-8 sticky top-24">
              <h2 className="text-xl font-bold mb-4">Contribute to this Case</h2>
              <p className="text-gray-700 mb-6">
                Help keep this case in the public eye by adding updates, evidence, or information.
              </p>

              <AddContribution
                caseId={caseData.id}
                onSuccess={handleContributionAdded}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Progress Update Modal */}
      {isProgressModalOpen && (
        <ProgressUpdateModal
          isOpen={isProgressModalOpen}
          onClose={() => setIsProgressModalOpen(false)}
          onSubmit={handleProgressUpdate}
          currentStage={caseData.currentStage}
        />
      )}
    </Layout>
  );
};

export default CaseDetails;
