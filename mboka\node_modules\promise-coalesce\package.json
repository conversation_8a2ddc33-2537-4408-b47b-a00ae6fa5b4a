{"name": "promise-coalesce", "version": "1.1.2", "description": "Coalesces multiple promises for the same identifier into a single request", "keywords": ["promise", "coalesce", "async"], "author": {"name": "<PERSON>", "url": "https://github.com/douglascayers"}, "repository": {"url": "git+https://github.com/douglascayers/promise-coalesce.git"}, "bugs": {"url": "https://github.com/douglascayers/promise-coalesce/issues"}, "homepage": "https://github.com/douglascayers/promise-coalesce#readme", "license": "BSD-3-<PERSON><PERSON>", "scripts": {"clean": "rm -rf dist && rm -rf coverage", "prettier": "prettier --check --ignore-unknown --no-error-on-unmatched-pattern .", "prettier:fix": "yarn prettier --write", "lint": "eslint --ext .ts,.tsx,.js,.json .", "lint:fix": "yarn lint --fix", "lint:staged": "lint-staged --concurrent 1", "format": "yarn prettier:fix && yarn lint:fix", "build": "yarn clean && tsc", "prepare": "yarn build && npx husky install", "test": "jest", "semantic-release": "semantic-release"}, "files": ["dist"], "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "exports": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "dependencies": {}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/jest": "^29.5.6", "@types/node": "^18.17.19", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.0.2", "prettier": "^3.0.3", "semantic-release": "^22.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{ts,tsx,js,json}": "yarn lint:fix", "*": "yarn prettier:fix"}}