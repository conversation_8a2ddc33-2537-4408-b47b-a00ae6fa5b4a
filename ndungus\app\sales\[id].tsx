import { MaterialIcons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Sale, formatSaleDate, getPaymentMethodName } from '@/models/Sale';
import { cancelSale, getSaleById } from '@/services/sales';

export default function SaleDetailScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const { id } = useLocalSearchParams<{ id: string }>();
  const [sale, setSale] = useState<Sale | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load sale data
  useEffect(() => {
    const loadSale = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const saleData = await getSaleById(id);
        setSale(saleData);
      } catch (error) {
        console.error('Error loading sale:', error);
        Alert.alert('Error', 'Failed to load sale details');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSale();
  }, [id]);

  // Handle cancel sale
  const handleCancelSale = async () => {
    if (!sale) return;
    
    Alert.alert(
      'Cancel Sale',
      'Are you sure you want to cancel this sale? This will restore the inventory.',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await cancelSale(sale.id);
              
              if (success) {
                Alert.alert('Success', 'Sale cancelled successfully', [
                  {
                    text: 'OK',
                    onPress: () => router.back(),
                  },
                ]);
              } else {
                Alert.alert('Error', 'Failed to cancel sale');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel sale');
            }
          },
        },
      ]
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text
          style={[
            styles.loadingText,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Loading...
        </Text>
      </View>
    );
  }

  if (!sale) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons
          name="error"
          size={48}
          color={colorScheme === 'dark' ? Colors.error : Colors.error}
        />
        <Text
          style={[
            styles.errorText,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Sale not found
        </Text>
        <Button
          title="Go Back"
          variant="primary"
          onPress={() => router.back()}
          style={{ marginTop: Spacing.md }}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerContent}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <MaterialIcons
              name="arrow-back"
              size={24}
              color={colorScheme === 'dark' ? Colors.white : Colors.black}
            />
          </Pressable>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Sale Details
          </Text>
        </View>
      </GlassCard>

      {/* Sale Info */}
      <GlassCard style={styles.infoCard}>
        <View style={styles.saleHeader}>
          <View>
            <Text
              style={[
                styles.saleDate,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {formatSaleDate(sale.createdAt)}
            </Text>
            <Text
              style={[
                styles.saleId,
                { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
              ]}>
              ID: {sale.id}
            </Text>
          </View>
          <View
            style={[
              styles.statusBadge,
              {
                backgroundColor: sale.status === 'completed'
                  ? 'rgba(52, 199, 89, 0.2)'
                  : sale.status === 'cancelled'
                    ? 'rgba(255, 59, 48, 0.2)'
                    : 'rgba(255, 204, 0, 0.2)',
              },
            ]}>
            <Text
              style={[
                styles.statusText,
                {
                  color: sale.status === 'completed'
                    ? Colors.success
                    : sale.status === 'cancelled'
                      ? Colors.error
                      : Colors.warning,
                },
              ]}>
              {sale.status.charAt(0).toUpperCase() + sale.status.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        {/* Payment Info */}
        <View style={styles.infoSection}>
          <Text
            style={[
              styles.sectionTitle,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Payment Information
          </Text>
          
          <View style={styles.infoRow}>
            <Text
              style={[
                styles.infoLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Payment Method:
            </Text>
            <Text
              style={[
                styles.infoValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {getPaymentMethodName(sale.paymentMethod)}
            </Text>
          </View>
          
          {sale.customerName && (
            <View style={styles.infoRow}>
              <Text
                style={[
                  styles.infoLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Customer:
              </Text>
              <Text
                style={[
                  styles.infoValue,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                {sale.customerName}
              </Text>
            </View>
          )}
          
          {sale.customerPhone && (
            <View style={styles.infoRow}>
              <Text
                style={[
                  styles.infoLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Phone:
              </Text>
              <Text
                style={[
                  styles.infoValue,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                {sale.customerPhone}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.divider} />

        {/* Items */}
        <View style={styles.infoSection}>
          <Text
            style={[
              styles.sectionTitle,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Items ({sale.items.length})
          </Text>
          
          {sale.items.map((item, index) => (
            <View key={index} style={styles.itemCard}>
              <View style={styles.itemInfo}>
                <Text
                  style={[
                    styles.itemName,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  {item.productName}
                </Text>
                <Text
                  style={[
                    styles.itemDetails,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  {item.quantity} x {formatCurrency(item.unitPrice)}
                </Text>
              </View>
              <Text
                style={[
                  styles.itemTotal,
                  { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                ]}>
                {formatCurrency(item.totalPrice)}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.divider} />

        {/* Totals */}
        <View style={styles.totalsSection}>
          <View style={styles.totalRow}>
            <Text
              style={[
                styles.totalLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Subtotal:
            </Text>
            <Text
              style={[
                styles.totalValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {formatCurrency(sale.subtotal)}
            </Text>
          </View>
          
          {sale.discount > 0 && (
            <View style={styles.totalRow}>
              <Text
                style={[
                  styles.totalLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Discount:
              </Text>
              <Text
                style={[
                  styles.totalValue,
                  { color: Colors.error },
                ]}>
                -{formatCurrency(sale.discount)}
              </Text>
            </View>
          )}
          
          {sale.tax > 0 && (
            <View style={styles.totalRow}>
              <Text
                style={[
                  styles.totalLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Tax:
              </Text>
              <Text
                style={[
                  styles.totalValue,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                {formatCurrency(sale.tax)}
              </Text>
            </View>
          )}
          
          <View style={styles.totalRow}>
            <Text
              style={[
                styles.grandTotalLabel,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Total:
            </Text>
            <Text
              style={[
                styles.grandTotalValue,
                { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
              ]}>
              {formatCurrency(sale.total)}
            </Text>
          </View>
          
          <View style={styles.totalRow}>
            <Text
              style={[
                styles.totalLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Profit:
            </Text>
            <Text
              style={[
                styles.totalValue,
                { color: Colors.success },
              ]}>
              {formatCurrency(sale.profit)}
            </Text>
          </View>
        </View>

        {/* Notes */}
        {sale.notes && (
          <>
            <View style={styles.divider} />
            <View style={styles.infoSection}>
              <Text
                style={[
                  styles.sectionTitle,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                Notes
              </Text>
              <Text
                style={[
                  styles.notesText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {sale.notes}
              </Text>
            </View>
          </>
        )}

        {/* Actions */}
        {sale.status === 'completed' && (
          <View style={styles.actionsContainer}>
            <Button
              title="Cancel Sale"
              variant="danger"
              icon={<MaterialIcons name="cancel" size={16} color={Colors.white} />}
              onPress={handleCancelSale}
            />
          </View>
        )}
      </GlassCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.medium,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  infoCard: {
    padding: Spacing.md,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  saleDate: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  saleId: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    marginTop: Spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  statusText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150, 150, 150, 0.2)',
    marginVertical: Spacing.md,
  },
  infoSection: {
    marginBottom: Spacing.sm,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.sm,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  infoLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  infoValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  itemCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.1)',
  },
  itemInfo: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  itemName: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: 2,
  },
  itemDetails: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  itemTotal: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
  },
  totalsSection: {
    marginBottom: Spacing.md,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  totalLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  totalValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  grandTotalLabel: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  grandTotalValue: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  notesText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  actionsContainer: {
    marginTop: Spacing.md,
  },
});
