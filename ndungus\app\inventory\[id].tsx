import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Product, calculateProfitMargin } from '@/models/Product';
import { deleteProduct, getProductById } from '@/services/products';

export default function ProductDetailScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const { id } = useLocalSearchParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const productData = await getProductById(id);
        setProduct(productData);
      } catch (error) {
        console.error('Error loading product:', error);
        Alert.alert('Error', 'Failed to load product details');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProduct();
  }, [id]);

  // Handle delete product
  const handleDeleteProduct = async () => {
    if (!product) return;
    
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteProduct(product.id);
              if (success) {
                Alert.alert('Success', 'Product deleted successfully', [
                  {
                    text: 'OK',
                    onPress: () => router.back(),
                  },
                ]);
              } else {
                Alert.alert('Error', 'Failed to delete product');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete product');
            }
          },
        },
      ]
    );
  };

  // Handle edit product
  const handleEditProduct = () => {
    if (!product) return;
    router.push(`/inventory/edit/${product.id}`);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };

  // Get status badge props
  const getStatusProps = (status: string) => {
    switch (status) {
      case 'in-stock':
        return { status: 'success' as const, label: 'In Stock' };
      case 'low-stock':
        return { status: 'warning' as const, label: 'Low Stock' };
      case 'out-of-stock':
        return { status: 'error' as const, label: 'Out of Stock' };
      default:
        return { status: 'info' as const };
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text
          style={[
            styles.loadingText,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Loading...
        </Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons
          name="error"
          size={48}
          color={colorScheme === 'dark' ? Colors.error : Colors.error}
        />
        <Text
          style={[
            styles.errorText,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Product not found
        </Text>
        <Button
          title="Go Back"
          variant="primary"
          onPress={() => router.back()}
          style={{ marginTop: Spacing.md }}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerContent}>
          <Pressable onPress={() => router.back()} style={styles.backButton}>
            <MaterialIcons
              name="arrow-back"
              size={24}
              color={colorScheme === 'dark' ? Colors.white : Colors.black}
            />
          </Pressable>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Product Details
          </Text>
        </View>
      </GlassCard>

      {/* Product Image */}
      <GlassCard style={styles.imageCard}>
        {product.image ? (
          <Image
            source={{ uri: product.image }}
            style={styles.productImage}
            contentFit="cover"
          />
        ) : (
          <View
            style={[
              styles.imagePlaceholder,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}>
            <MaterialIcons
              name="image"
              size={48}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray500}
            />
          </View>
        )}
      </GlassCard>

      {/* Product Info */}
      <GlassCard style={styles.infoCard}>
        <View style={styles.productHeader}>
          <View>
            <Text
              style={[
                styles.productName,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {product.name}
            </Text>
            <Text
              style={[
                styles.productCode,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {product.code}
            </Text>
          </View>
          <StatusBadge {...getStatusProps(product.status)} />
        </View>

        <View style={styles.divider} />

        {/* Price Info */}
        <View style={styles.priceContainer}>
          <View style={styles.priceRow}>
            <Text
              style={[
                styles.priceLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Selling Price:
            </Text>
            <Text
              style={[
                styles.priceValue,
                { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
              ]}>
              {formatCurrency(product.sellingPrice)}
            </Text>
          </View>
          <View style={styles.priceRow}>
            <Text
              style={[
                styles.priceLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Buying Price:
            </Text>
            <Text
              style={[
                styles.priceValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {formatCurrency(product.buyingPrice)}
            </Text>
          </View>
          <View style={styles.priceRow}>
            <Text
              style={[
                styles.priceLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Profit Margin:
            </Text>
            <Text
              style={[
                styles.priceValue,
                { color: colorScheme === 'dark' ? Colors.success : Colors.success },
              ]}>
              {calculateProfitMargin(product).toFixed(2)}%
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        {/* Inventory Info */}
        <View style={styles.inventoryContainer}>
          <View style={styles.inventoryRow}>
            <Text
              style={[
                styles.inventoryLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Units in Stock:
            </Text>
            <Text
              style={[
                styles.inventoryValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {product.units}
            </Text>
          </View>
          <View style={styles.inventoryRow}>
            <Text
              style={[
                styles.inventoryLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Category:
            </Text>
            <Text
              style={[
                styles.inventoryValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {product.category}
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Edit Product"
            variant="primary"
            icon={<MaterialIcons name="edit" size={16} color={Colors.black} />}
            style={{ flex: 1 }}
            onPress={handleEditProduct}
          />
          <Button
            title="Delete"
            variant="danger"
            icon={<MaterialIcons name="delete" size={16} color={Colors.white} />}
            style={{ flex: 1, marginLeft: Spacing.md }}
            onPress={handleDeleteProduct}
          />
        </View>
      </GlassCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.medium,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  imageCard: {
    marginBottom: Spacing.md,
    padding: 0,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 200,
  },
  imagePlaceholder: {
    width: '100%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoCard: {
    padding: Spacing.md,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  productName: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
    marginBottom: Spacing.xs,
  },
  productCode: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150, 150, 150, 0.2)',
    marginVertical: Spacing.md,
  },
  priceContainer: {
    marginBottom: Spacing.sm,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  priceLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  priceValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
  },
  inventoryContainer: {
    marginBottom: Spacing.lg,
  },
  inventoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  inventoryLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  inventoryValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: Spacing.md,
  },
});
