import { Link } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

export default function HomePage() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome to Ndungus Shop</Text>
      <Text style={styles.subtitle}>Home Page</Text>
      
      <Link href="/test" style={styles.link}>
        <Text style={styles.linkText}>Go to Test Page</Text>
      </Link>
      
      <Link href="/(tabs)" style={styles.link}>
        <Text style={styles.linkText}>Go to Tabs</Text>
      </Link>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 30,
  },
  link: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    width: 200,
    alignItems: 'center',
  },
  linkText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
