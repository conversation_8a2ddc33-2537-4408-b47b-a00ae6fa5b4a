import React from 'react';
import Link from 'next/link';
import { FaTwitter, FaFacebook, FaInstagram } from 'react-icons/fa';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-b from-gray-900 to-black text-white pt-12 pb-6 mt-12 rounded-t-3xl relative">
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-600 via-yellow-500 to-blue-600 opacity-80"></div>
      
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <h3 className="text-xl font-bold mb-4 inline-block relative">
              Public Eye
              <span className="absolute bottom-0 left-0 w-10 h-1 bg-gradient-to-r from-blue-500 to-blue-600"></span>
            </h3>
            <p className="text-gray-300 mb-4">
              Keeping cases visible and justice accountable.
            </p>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-4 inline-block relative">
              Quick Links
              <span className="absolute bottom-0 left-0 w-10 h-1 bg-gradient-to-r from-blue-500 to-blue-600"></span>
            </h3>
            <ul className="space-y-2">
              <li className="relative pl-4">
                <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li className="relative pl-4">
                <Link href="/cases/create" className="text-gray-300 hover:text-white transition-colors">
                  Create Case
                </Link>
              </li>
              <li className="relative pl-4">
                <Link href="/user/cases" className="text-gray-300 hover:text-white transition-colors">
                  Your Cases
                </Link>
              </li>
              <li className="relative pl-4">
                <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-4 inline-block relative">
              Connect
              <span className="absolute bottom-0 left-0 w-10 h-1 bg-gradient-to-r from-blue-500 to-blue-600"></span>
            </h3>
            <div className="flex space-x-4">
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-blue-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors"
                aria-label="Twitter"
              >
                <FaTwitter className="text-white" />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-blue-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors"
                aria-label="Facebook"
              >
                <FaFacebook className="text-white" />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-blue-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors"
                aria-label="Instagram"
              >
                <FaInstagram className="text-white" />
              </a>
            </div>
          </div>
        </div>
        
        <div className="text-center pt-6 border-t border-gray-800">
          <p className="text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} Public Eye. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
