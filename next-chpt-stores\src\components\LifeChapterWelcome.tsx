"use client";

import Link from 'next/link';

interface LifeChapterWelcomeProps {
  lifeChapter: string;
}

export default function LifeChapterWelcome({ lifeChapter }: LifeChapterWelcomeProps) {
  // Define welcome messages and CTAs for each life chapter
  const chapterContent = {
    "Independent Living": {
      title: "Starting Your Independent Journey!",
      message: "Whether you're moving into your first place, graduating, or simply beginning a new chapter on your own, we've got you covered! From kitchen essentials and bedroom comforts to professional attire and organizational tools, we've carefully curated everything you need to create a functional home and put your best foot forward. Quality items at budget-friendly prices to help you thrive in your independent life.",
      cta: "Explore Products Below",
      ctaLink: "#"
    },
    "Relocating": {
      title: "New City, New Adventures, New Beginnings!",
      message: "Moving to a new place brings both excitement and challenges. Our relocating collection includes everything you need for a smooth transition - from packing supplies to help you move efficiently to essential items that will help you settle in quickly. We've thought of everything so you can focus on embracing your new surroundings and creating memories in your new home.",
      cta: "Explore Products Below",
      ctaLink: "#"
    },
    "Couples": {
      title: "Two Lives Becoming One - Congratulations!",
      message: "Whether you're newlyweds or moving in together, creating a shared space is a beautiful journey. Our couples collection features thoughtfully selected items that blend style and functionality - from kitchen essentials for cooking together to bedroom and living room pieces that reflect your shared taste. Build a home that tells your unique story as a couple.",
      cta: "Explore Products Below",
      ctaLink: "#"
    },
    "Baby": {
      title: "A Tiny Person, A Huge Adventure Ahead!",
      message: "Preparing for a baby is one of life's most precious transitions. Our baby collection includes everything new parents need - from nursery essentials and feeding supplies to travel gear and safety items. We've researched the best products so you can focus on what matters most: welcoming and nurturing your little one with confidence and joy.",
      cta: "Explore Products Below",
      ctaLink: "#"
    }
  };

  // Get content for the current life chapter
  const content = chapterContent[lifeChapter as keyof typeof chapterContent] || {
    title: `Welcome to Your ${lifeChapter} Journey!`,
    message: "We've curated everything you need for this exciting new chapter in your life.",
    cta: "Explore Now",
    ctaLink: `/life-chapters/${lifeChapter.toLowerCase()}/categories`
  };

  return (
    <div className="relative overflow-hidden rounded-xl shadow-xl mb-12 bg-white border border-gray-200">
      {/* Background with subtle pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-50 to-white"></div>

      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-primary bg-opacity-5 rounded-full -mr-32 -mt-32 blur-xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-secondary bg-opacity-5 rounded-full -ml-32 -mb-32 blur-xl"></div>

      {/* Left border accent */}
      <div className="absolute left-0 top-0 bottom-0 w-3 bg-gradient-to-b from-primary to-secondary"></div>

      {/* Content */}
      <div className="relative z-10 p-10 backdrop-blur-sm bg-white/50">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary">{content.title}</h2>
          <p className="text-lg mb-8 leading-relaxed text-gray-900">{content.message}</p>
          <div className="flex justify-center md:justify-start">
            <button
              onClick={() => {
                document.querySelector('.categories-section')?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start'
                });
              }}
              className="inline-block bg-primary text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-opacity-90 transition-all transform hover:scale-105 hover:shadow-lg"
            >
              {content.cta}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
