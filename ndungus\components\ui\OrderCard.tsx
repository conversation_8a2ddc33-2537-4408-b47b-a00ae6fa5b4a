import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import { StyleSheet, Text, View, Pressable, ViewStyle } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type OrderStatus = 'pending' | 'picked' | 'fulfilled';

type OrderCardProps = {
  name: string;
  image?: string;
  description?: string;
  contact?: string;
  units?: number;
  category?: string;
  date: Date | string;
  status: OrderStatus;
  pickedBy?: string;
  dayCount?: number;
  onPress?: () => void;
  onPick?: () => void;
  onRelease?: () => void;
  onFulfill?: () => void;
  style?: ViewStyle;
};

/**
 * A card component for displaying customer orders
 */
export function OrderCard({
  name,
  image,
  description,
  contact,
  units,
  category,
  date,
  status,
  pickedBy,
  dayCount,
  onPress,
  onPick,
  onRelease,
  onFulfill,
  style,
}: OrderCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Format date
  const formatDate = (date: Date | string) => {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    return date.toLocaleDateString();
  };
  
  return (
    <Pressable onPress={onPress}>
      <GlassCard style={[styles.container, style]}>
        <View style={styles.header}>
          {image ? (
            <Image
              source={{ uri: image }}
              style={styles.image}
              contentFit="cover"
              transition={200}
            />
          ) : (
            <View
              style={[
                styles.imagePlaceholder,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="shopping-bag"
                size={24}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray500}
              />
            </View>
          )}
          
          <View style={styles.headerContent}>
            <Text
              style={[
                styles.name,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}
              numberOfLines={1}>
              {name}
            </Text>
            
            <View style={styles.metaContainer}>
              <StatusBadge status={status} />
              
              {dayCount !== undefined && (
                <View style={styles.dayCountContainer}>
                  <MaterialIcons
                    name="schedule"
                    size={12}
                    color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                  />
                  <Text
                    style={[
                      styles.dayCount,
                      { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
                    ]}>
                    {dayCount} {dayCount === 1 ? 'day' : 'days'}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
        
        {description && (
          <Text
            style={[
              styles.description,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}
            numberOfLines={2}>
            {description}
          </Text>
        )}
        
        <View style={styles.detailsContainer}>
          {contact && (
            <View style={styles.detailItem}>
              <MaterialIcons
                name="phone"
                size={14}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.detailText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {contact}
              </Text>
            </View>
          )}
          
          {category && (
            <View style={styles.detailItem}>
              <MaterialIcons
                name="category"
                size={14}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.detailText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {category}
              </Text>
            </View>
          )}
          
          {units !== undefined && (
            <View style={styles.detailItem}>
              <MaterialIcons
                name="inventory"
                size={14}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.detailText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {units} units
              </Text>
            </View>
          )}
          
          <View style={styles.detailItem}>
            <MaterialIcons
              name="event"
              size={14}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.detailText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {formatDate(date)}
            </Text>
          </View>
        </View>
        
        {status === 'picked' && pickedBy && (
          <View style={styles.pickedByContainer}>
            <MaterialIcons
              name="person"
              size={14}
              color={colorScheme === 'dark' ? Colors.secondary : Colors.secondary}
            />
            <Text
              style={[
                styles.pickedByText,
                { color: colorScheme === 'dark' ? Colors.secondary : Colors.secondary },
              ]}>
              Picked by {pickedBy}
            </Text>
          </View>
        )}
        
        <View style={styles.actions}>
          {status === 'pending' && onPick && (
            <Button
              title="Pick Order"
              variant="primary"
              size="sm"
              onPress={onPick}
              icon={<MaterialIcons name="check" size={16} color={Colors.black} />}
            />
          )}
          
          {status === 'picked' && onRelease && (
            <Button
              title="Release"
              variant="outline"
              size="sm"
              onPress={onRelease}
              icon={<MaterialIcons name="close" size={16} color={Colors.primary} />}
            />
          )}
          
          {status === 'picked' && onFulfill && (
            <Button
              title="Fulfill"
              variant="secondary"
              size="sm"
              onPress={onFulfill}
              icon={<MaterialIcons name="done-all" size={16} color={Colors.white} />}
            />
          )}
        </View>
      </GlassCard>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.xs,
  },
  header: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 4,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  dayCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dayCount: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  description: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    marginTop: Spacing.sm,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginTop: Spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  pickedByContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: Spacing.sm,
    padding: Spacing.xs,
    backgroundColor: 'rgba(0, 128, 128, 0.1)',
    borderRadius: BorderRadius.sm,
    alignSelf: 'flex-start',
  },
  pickedByText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
    marginTop: Spacing.md,
  },
});
