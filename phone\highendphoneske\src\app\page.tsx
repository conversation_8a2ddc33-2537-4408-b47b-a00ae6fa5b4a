'use client';

import { useEffect, useState } from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Navbar from '../components/Navbar';
import PhoneCard from '../components/PhoneCard';
import { phones } from '../data/phones';
import { Phone } from '../types';

export default function Home() {
  const [featuredPhones, setFeaturedPhones] = useState<Phone[]>([]);

  useEffect(() => {
    setFeaturedPhones(phones.filter(phone => phone.featured));
  }, []);

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
  };

  return (
    <main className="min-h-screen bg-gray-50">
      <Navbar />
      {/* Hero Section with Carousel */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <Slider {...sliderSettings}>
            {featuredPhones.map((phone) => (
              <div key={phone.id} className="relative h-[400px]">
                <div className="absolute inset-0">
                  <img
                    src={phone.image}
                    alt={phone.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40">
                    <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
                      <h2 className="text-3xl font-bold mb-2">{phone.name}</h2>
                      <p className="text-lg mb-4">{phone.description}</p>
                      <p className="text-2xl font-bold">KES {phone.price.toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>
      {/* Phone Listings */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Available Phones</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {phones.map((phone) => (
            <PhoneCard key={phone.id} phone={phone} />
          ))}
        </div>
      </div>
    </main>
  );
}
