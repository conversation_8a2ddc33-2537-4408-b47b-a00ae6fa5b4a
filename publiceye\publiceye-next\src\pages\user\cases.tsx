import { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { FaPlus } from 'react-icons/fa';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import CaseCard from '@/components/cases/CaseCard';
import { useCases } from '@/hooks/useCases';
import { useAuth } from '@/hooks/useAuth';
import { Case } from '@/types/case';

const UserCases: NextPage = () => {
  const [cases, setCases] = useState<Case[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { getUserCases } = useCases();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?returnUrl=/user/cases');
      return;
    }

    const fetchUserCases = async () => {
      setIsLoading(true);
      const userCases = await getUserCases();
      setCases(userCases);
      setIsLoading(false);
    };

    if (user) {
      fetchUserCases();
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
        </div>
      </Layout>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-16 md:py-24 relative">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full bg-black opacity-20"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl font-extrabold mb-6 leading-tight">
            Your Cases
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-10 font-light">
            Track and manage the cases you've created.
          </p>
        </div>
      </section>

      {/* Cases Grid */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white bg-opacity-70 rounded-xl h-96 animate-pulse"></div>
              ))}
            </div>
          ) : cases.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {cases.map((caseItem) => (
                <CaseCard key={caseItem.id} caseData={caseItem} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-gray-700 mb-4">No cases found</h3>
              <p className="text-gray-500 mb-8">Start by creating your first case.</p>
              <Link 
                href="/cases/create" 
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium hover:shadow-lg transition-all"
              >
                <FaPlus className="mr-2" />
                Create a Case
              </Link>
            </div>
          )}
        </div>
      </section>
    </Layout>
  );
};

export default UserCases; 