import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Mosaipad - Transform Notes into Collective Insights",
  description: "A platform that allows event attendees to upload notes and automatically generates beautiful, structured collaborative summaries.",
  keywords: ["event notes", "collaborative summary", "AI summarization", "conference notes", "workshop summary"],
  authors: [{ name: "Mosaipad Team" }],
  openGraph: {
    title: "Mosaipad - Transform Notes into Collective Insights",
    description: "Transform everyone's notes into one collective mosaic",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Mosaipad - Transform Notes into Collective Insights",
    description: "Transform everyone's notes into one collective mosaic",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-white text-gray-900">
        {children}
      </body>
    </html>
  );
}
