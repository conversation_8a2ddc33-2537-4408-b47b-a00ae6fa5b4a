{"name": "mosa<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx scripts/seed-demo.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.13", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.515.0", "multer": "^2.0.1", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "openai": "^5.3.0", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "resend": "^4.6.0", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}