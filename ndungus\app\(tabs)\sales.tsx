import { MaterialIcons } from '@expo/vector-icons';
import { <PERSON> } from 'expo-router';
import React, { useState } from 'react';
import { FlatList, Pressable, RefreshControl, StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock data for sales
const mockSales = [
  {
    id: '1',
    itemName: 'Samsung Galaxy S21',
    image: 'https://images.unsplash.com/photo-1610945415295-d9bbf067e59c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8c2Ftc3VuZyUyMHMyMXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
    sellingPrice: 85000,
    buyingPrice: 70000,
    units: 1,
    date: new Date(2023, 4, 17),
    paymentMethod: 'mpesa',
    notes: 'Customer also interested in phone case and screen protector.',
  },
  {
    id: '2',
    itemName: 'Nike Air Max 270',
    image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8bmlrZXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
    sellingPrice: 12000,
    buyingPrice: 8000,
    units: 1,
    date: new Date(2023, 4, 17),
    paymentMethod: 'cash',
  },
  {
    id: '3',
    itemName: 'Apple AirPods Pro',
    image: 'https://images.unsplash.com/photo-1588423771073-b8903fbb85b5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YWlycG9kc3xlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
    sellingPrice: 20000,
    buyingPrice: 15000,
    units: 2,
    date: new Date(2023, 4, 16),
    paymentMethod: 'mpesa',
  },
  {
    id: '4',
    itemName: 'Samsung 55" QLED TV',
    image: 'https://images.unsplash.com/photo-1593305841991-05c297ba4575?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8c2Ftc3VuZyUyMHR2fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
    sellingPrice: 85000,
    buyingPrice: 65000,
    units: 1,
    date: new Date(2023, 4, 15),
    paymentMethod: 'card',
    notes: 'Delivery scheduled for next week.',
  },
  {
    id: '5',
    itemName: 'Logitech MX Master 3',
    sellingPrice: 9000,
    buyingPrice: 6000,
    units: 1,
    date: new Date(2023, 4, 14),
    paymentMethod: 'cash',
  },
];

type TimeFilter = 'today' | 'week' | 'month' | 'all';

export default function SalesScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [refreshing, setRefreshing] = useState(false);
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all');
  const [sales, setSales] = useState<Sale[]>([]);
  const [stats, setStats] = useState({
    totalSales: 0,
    totalRevenue: 0,
    totalProfit: 0,
    salesCount: 0,
  });

  // Load sales data
  const loadData = async () => {
    try {
      setRefreshing(true);

      // Get sales
      const salesData = await getSales();
      setSales(salesData);

      // Get stats based on time filter
      const days = timeFilter === 'today' ? 1 :
                  timeFilter === 'week' ? 7 :
                  timeFilter === 'month' ? 30 : 0;

      const statsData = await getSalesStats(days);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading sales data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Load data on mount and when time filter changes
  useEffect(() => {
    loadData();
  }, [timeFilter]);

  // Handle refresh
  const onRefresh = React.useCallback(() => {
    loadData();
  }, [timeFilter]);

  // Handle cancel sale
  const handleCancelSale = async (id: string) => {
    Alert.alert(
      'Cancel Sale',
      'Are you sure you want to cancel this sale? This will restore the inventory.',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await cancelSale(id);

              if (success) {
                Alert.alert('Success', 'Sale cancelled successfully');
                loadData();
              } else {
                Alert.alert('Error', 'Failed to cancel sale');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel sale');
            }
          },
        },
      ]
    );
  };

  // Handle view sale details
  const handleViewSale = (id: string) => {
    router.push(`/sales/${id}`);
  };

  // Filter sales based on time filter
  const filteredSales = React.useMemo(() => {
    if (timeFilter === 'all') {
      return sales;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    return sales.filter(sale => {
      const saleDate = new Date(sale.createdAt);

      switch (timeFilter) {
        case 'today':
          return saleDate >= today;
        case 'week':
          return saleDate >= weekStart;
        case 'month':
          return saleDate >= monthStart;
        default:
          return true;
      }
    });
  }, [timeFilter, sales]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerTop}>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Sales
          </Text>
          <Link href="/sales/new" asChild>
            <Button
              title="Record Sale"
              variant="primary"
              size="sm"
              icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
              onPress={() => {}}
            />
          </Link>
        </View>

        {/* Time Filters */}
        <View style={styles.filtersContainer}>
          {(['today', 'week', 'month', 'all'] as const).map((filter) => (
            <Pressable
              key={filter}
              style={[
                styles.filterPill,
                timeFilter === filter && {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 215, 0, 0.2)'
                    : 'rgba(255, 215, 0, 0.1)',
                  borderColor: Colors.primary,
                  borderWidth: 1,
                },
              ]}
              onPress={() => setTimeFilter(filter)}>
              <Text
                style={[
                  styles.filterText,
                  {
                    color: timeFilter === filter
                      ? Colors.primary
                      : colorScheme === 'dark'
                        ? Colors.gray300
                        : Colors.gray700,
                    fontFamily: timeFilter === filter
                      ? Typography.fontFamily.semiBold
                      : Typography.fontFamily.regular,
                  },
                ]}>
                {filter === 'today'
                  ? 'Today'
                  : filter === 'week'
                    ? 'This Week'
                    : filter === 'month'
                      ? 'This Month'
                      : 'All Time'}
              </Text>
            </Pressable>
          ))}
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View
            style={[
              styles.summaryCard,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}>
            <Text
              style={[
                styles.summaryLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Total Revenue
            </Text>
            <Text
              style={[
                styles.summaryValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {formatCurrency(stats.totalRevenue)}
            </Text>
          </View>

          <View
            style={[
              styles.summaryCard,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(16, 185, 129, 0.2)'
                  : 'rgba(16, 185, 129, 0.1)',
              },
            ]}>
            <Text
              style={[
                styles.summaryLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Total Profit
            </Text>
            <Text
              style={[
                styles.summaryValue,
                { color: Colors.success },
              ]}>
              {formatCurrency(stats.totalProfit)}
            </Text>
          </View>

          <View
            style={[
              styles.summaryCard,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 215, 0, 0.2)'
                  : 'rgba(255, 215, 0, 0.1)',
              },
            ]}>
            <Text
              style={[
                styles.summaryLabel,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Sales Count
            </Text>
            <Text
              style={[
                styles.summaryValue,
                { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
              ]}>
              {stats.salesCount}
            </Text>
          </View>
        </View>
      </GlassCard>

      {/* Sales List */}
      <FlatList
        data={filteredSales}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <GlassCard style={styles.saleCard}>
            <Pressable
              style={styles.saleCardContent}
              onPress={() => handleViewSale(item.id)}>
              <View style={styles.saleHeader}>
                <View>
                  <Text
                    style={[
                      styles.saleDate,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    {formatSaleDate(item.createdAt)}
                  </Text>
                  <Text
                    style={[
                      styles.saleId,
                      { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
                    ]}>
                    ID: {item.id.slice(0, 8)}
                  </Text>
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    {
                      backgroundColor: item.status === 'completed'
                        ? 'rgba(52, 199, 89, 0.2)'
                        : item.status === 'cancelled'
                          ? 'rgba(255, 59, 48, 0.2)'
                          : 'rgba(255, 204, 0, 0.2)',
                    },
                  ]}>
                  <Text
                    style={[
                      styles.statusText,
                      {
                        color: item.status === 'completed'
                          ? Colors.success
                          : item.status === 'cancelled'
                            ? Colors.error
                            : Colors.warning,
                      },
                    ]}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </Text>
                </View>
              </View>

              <View style={styles.saleDetails}>
                <View style={styles.saleInfo}>
                  <Text
                    style={[
                      styles.saleInfoLabel,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    Items:
                  </Text>
                  <Text
                    style={[
                      styles.saleInfoValue,
                      { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                    ]}>
                    {item.items.length}
                  </Text>
                </View>

                <View style={styles.saleInfo}>
                  <Text
                    style={[
                      styles.saleInfoLabel,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    Payment:
                  </Text>
                  <Text
                    style={[
                      styles.saleInfoValue,
                      { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                    ]}>
                    {getPaymentMethodName(item.paymentMethod)}
                  </Text>
                </View>

                <View style={styles.saleInfo}>
                  <Text
                    style={[
                      styles.saleInfoLabel,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    Total:
                  </Text>
                  <Text
                    style={[
                      styles.saleTotal,
                      { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                    ]}>
                    {formatCurrency(item.total)}
                  </Text>
                </View>
              </View>

              {item.status === 'completed' && (
                <View style={styles.saleActions}>
                  <Button
                    title="Cancel"
                    variant="danger"
                    size="sm"
                    icon={<MaterialIcons name="cancel" size={16} color={Colors.white} />}
                    onPress={() => handleCancelSale(item.id)}
                  />
                </View>
              )}
            </Pressable>
          </GlassCard>
        )}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <GlassCard style={styles.emptyContainer}>
            <MaterialIcons
              name="point-of-sale"
              size={48}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.emptyText,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              No sales found
            </Text>
            <Text
              style={[
                styles.emptySubtext,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {timeFilter !== 'all'
                ? `No sales recorded ${timeFilter === 'today' ? 'today' : `this ${timeFilter}`}`
                : 'Record your first sale'}
            </Text>
            <Link href="/sales/new" asChild>
              <Button
                title="Record Sale"
                variant="primary"
                icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
                style={{ marginTop: Spacing.md }}
                onPress={() => {}}
              />
            </Link>
          </GlassCard>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.bold,
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginTop: Spacing.sm,
  },
  filterPill: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  filterText: {
    fontSize: Typography.fontSize.sm,
  },
  summaryContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginTop: Spacing.md,
  },
  summaryCard: {
    flex: 1,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  summaryValue: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
    marginTop: Spacing.xs,
  },
  listContainer: {
    paddingBottom: Spacing.xl,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
    marginTop: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
  saleCard: {
    marginBottom: Spacing.sm,
    padding: 0,
    overflow: 'hidden',
  },
  saleCardContent: {
    padding: Spacing.md,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  saleDate: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  saleId: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  saleDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  saleInfo: {
    alignItems: 'center',
  },
  saleInfoLabel: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: 2,
  },
  saleInfoValue: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  saleTotal: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.bold,
  },
  saleActions: {
    alignItems: 'flex-end',
  },
});
