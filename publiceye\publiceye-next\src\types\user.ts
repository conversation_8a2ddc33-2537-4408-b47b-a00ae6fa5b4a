export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  codeName?: string; // Privacy-preserving name chosen during onboarding
  colorCode?: string; // Color code for user's avatar
  createdAt?: Date | string;
  updatedAt?: Date | string;
  role?: 'user' | 'admin' | 'moderator';
  followedCases?: Record<string, boolean> | string[]; // Object with case IDs as keys or array of case IDs
  createdCases?: Record<string, boolean> | string[]; // Object with case IDs as keys or array of case IDs
  contributedCases?: Record<string, boolean> | string[]; // Object with case IDs as keys or array of case IDs
}

export interface UserSettings {
  userId: string;
  emailNotifications: boolean;
  privacyLevel: 'high' | 'medium' | 'low';
  theme: 'light' | 'dark' | 'system';
}
