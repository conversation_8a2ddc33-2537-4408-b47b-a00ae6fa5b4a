/* Base Styles */
:root {
    --pastel-pink: #FFD1DC;
    --dusty-rose: #E8A7B5;
    --soft-lilac: #E2D4F2;
    --pale-mint: #D9ECE5;
    --off-white: #FDF8F5;
    --slate-gray: #6B7280;
    --dark-gray: #4B5563;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--slate-gray);
    background-color: var(--off-white);
    line-height: 1.6;
}

h1, h2, h3, h4 {
    font-family: 'Playfair Display', serif;
    color: var(--dark-gray);
    font-weight: 700;
}

h1 {
    font-size: 3rem;
    line-height: 1.2;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
}

a {
    text-decoration: none;
    color: var(--soft-lilac);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--dusty-rose);
}

.btn {
    display: inline-block;
    background-color: var(--pastel-pink);
    color: var(--dark-gray);
    padding: 0.8rem 1.5rem;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: 'Inter', sans-serif;
}

.btn:hover {
    background-color: var(--dusty-rose);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--pastel-pink), var(--soft-lilac));
    margin: 1rem auto;
    border-radius: 2px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 1.5rem 0;
    z-index: 1000;
    transition: all 0.3s ease;
    background-color: rgba(253, 248, 245, 0.9);
    backdrop-filter: blur(10px);
}

.nav-container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-links {
    display: flex;
    justify-content: center;
    list-style: none;
}

.nav-links li {
    margin: 0 1.5rem;
}

.nav-links a {
    color: var(--dark-gray);
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--dusty-rose);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Hamburger Menu Styles */
.nav-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 1100;
}

.nav-toggle .bar {
    width: 28px;
    height: 3px;
    background: var(--dark-gray);
    margin: 4px 0;
    border-radius: 2px;
    transition: 0.3s;
}

@media (max-width: 900px) {
    .nav-toggle {
        display: flex;
    }
    .nav-links {
        position: fixed;
        top: 70px;
        right: 0;
        width: 220px;
        height: calc(100% - 70px);
        background: white;
        flex-direction: column;
        align-items: flex-start;
        padding: 2rem 1.5rem;
        box-shadow: -2px 0 12px rgba(0,0,0,0.07);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 1050;
    }
    .nav-links.open {
        transform: translateX(0);
    }
    .nav-links li {
        margin: 1.2rem 0;
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 8rem 0 4rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    z-index: 1;
}

.hero h1 span {
    color: var(--dusty-rose);
}

.hero h2 {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 1.5rem;
    color: var(--slate-gray);
    margin: 1rem 0 2rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
}

.circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.3;
}

.circle.pink {
    width: 400px;
    height: 400px;
    background-color: var(--pastel-pink);
    top: -100px;
    right: -100px;
}

.circle.lilac {
    width: 300px;
    height: 300px;
    background-color: var(--soft-lilac);
    top: 150px;
    right: 200px;
}

.circle.mint {
    width: 200px;
    height: 200px;
    background-color: var(--pale-mint);
    bottom: 100px;
    right: 300px;
}

.hero-description {
    max-width: 800px;
    margin: 2rem 0;
}

.hero-description p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--slate-gray);
    margin-bottom: 1rem;
}

.hero-description p:last-child {
    margin-bottom: 2rem;
}

/* Skills Section */
.skills {
    padding: 5rem 0;
    background-color: white;
}

.skills-container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-category {
    background-color: var(--off-white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-10px);
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    margin-top: 1.5rem;
}

.skill-tag {
    background-color: var(--pastel-pink);
    color: var(--dark-gray);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background-color: var(--dusty-rose);
    color: white;
}

/* Projects Section */
.projects {
    padding: 5rem 0;
}

.projects-container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.project-card {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.project-card h3 {
    color: var(--dusty-rose);
    margin-bottom: 1rem;
}

.project-card p {
    flex-grow: 1;
    margin-bottom: 1.5rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    color: var(--soft-lilac);
    font-weight: 500;
}

.project-link i {
    margin-right: 0.5rem;
}

.project-link:hover {
    color: var(--dusty-rose);
}

.project-image {
    width: 100%;
    height: 220px;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    object-fit: cover;
}

.private-badge {
    display: inline-block;
    background: var(--dusty-rose);
    color: #fff;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 12px;
    padding: 0.2em 0.8em;
    margin-left: 0.5em;
    vertical-align: middle;
    letter-spacing: 0.5px;
}

/* Ideas Section */
.ideas {
    padding: 5rem 0;
    background-color: white;
}

.idea-form {
    width: 90%;
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--off-white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-gray);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Inter', sans-serif;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--soft-lilac);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-message {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 5px;
    text-align: center;
    display: none;
}

.form-message.success {
    background-color: rgba(217, 236, 229, 0.5);
    color: var(--dark-gray);
    display: block;
}

.form-message.error {
    background-color: rgba(232, 167, 181, 0.5);
    color: var(--dark-gray);
    display: block;
}

/* About Section (About Me) */
.about {
    padding: 5rem 0;
}

.about-container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-image {
    position: relative;
    height: 400px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: cover;
    background-image:url('assets/IMG_1667.jpg');
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
    font-weight: 500;
}

.about-content h2 {
    text-align: left;
}

.about-content .section-divider {
    margin: 1rem 0;
}

.about-content p {
    font-size: 1.05rem;
    color: var(--slate-gray);
    margin-bottom: 1.1rem;
    line-height: 1.7;
}

@media (max-width: 900px) {
    .about-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    .about-image {
        height: 220px;
    }
    .image-placeholder {
        height: 220px;
    }
    .about-content h2 {
        text-align: center;
    }
}

.about-minilinks {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.about-minilinks a {
    color: var(--dusty-rose);
    font-weight: 600;
    font-size: 1.05rem;
    padding: 0.4em 1em;
    border-radius: 20px;
    background: rgba(232, 167, 181, 0.08);
    transition: background 0.2s, color 0.2s;
    text-decoration: none;
}

.about-minilinks a:hover, .about-minilinks a:focus {
    background: var(--dusty-rose);
    color: #fff;
}

@media (max-width: 600px) {
    .about-minilinks {
        gap: 0.7rem;
        font-size: 0.95rem;
    }
    .about-minilinks a {
        padding: 0.3em 0.7em;
        font-size: 0.98rem;
    }
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background-color: white;
    text-align: center;
}

.contact-content {
    width: 90%;
    max-width: 600px;
    margin: 0 auto;
}

.email-btn {
    margin-top: 2rem;
    display: inline-flex;
    align-items: center;
}

.email-btn i {
    margin-right: 0.5rem;
}

/* Footer */
.footer {
    padding: 2rem 0;
    text-align: center;
    background-color: var(--off-white);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.social-links a {
    color: var(--slate-gray);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--dusty-rose);
}

/* Experience Section */
.experience {
    padding: 5rem 0;
    background-color: white;
}

.experience-container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.experience-card {
    background-color: var(--off-white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.experience-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.experience-card h3 {
    color: var(--dusty-rose);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.experience-card .company {
    color: var(--dark-gray);
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.experience-card .date,
.experience-card .location {
    color: var(--slate-gray);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.experience-card .achievements {
    list-style-type: none;
    padding: 0;
    margin-top: 1rem;
}

.experience-card .achievements li {
    color: var(--slate-gray);
    margin-bottom: 0.8rem;
    position: relative;
    padding-left: 1.5rem;
}

.experience-card .achievements li::before {
    content: "•";
    color: var(--dusty-rose);
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.experience-card .company-link {
    color: var(--slate-gray);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
}

.experience-card .company-link i {
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.experience-card .company-link:hover {
    color: var(--dusty-rose);
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .nav-links {
        flex-wrap: wrap;
    }
    
    .nav-links li {
        margin: 0.5rem 1rem;
    }
    
    .hero-decoration {
        display: none;
    }
    
    .about-container {
        grid-template-columns: 1fr;
    }
    
    .about-image {
        height: 300px;
        margin-bottom: 2rem;
    }
    
    .about-content h2 {
        text-align: center;
    }
    
    .about-content .divider {
        margin: 1rem auto;
    }
    
    .experience-container {
        grid-template-columns: 1fr;
    }
    
    .experience-card {
        padding: 1.5rem;
    }
    
    .hero-description {
        margin: 1.5rem 0;
    }
    
    .hero-description p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .nav-links li {
        margin: 0.5rem;
    }
    
    .skills-container,
    .projects-container {
        grid-template-columns: 1fr;
    }
}

.awards-list, .education-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0 2rem 0;
}

.awards-list li, .education-list li {
    margin-bottom: 0.8rem;
    font-size: 1.08rem;
    color: var(--slate-gray);
    line-height: 1.6;
}

.awards-list a {
    color: var(--dusty-rose);
    text-decoration: underline;
    font-size: 0.98em;
}

.about__photo-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.about__photo {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    object-fit: cover;
    background: #fff;
}

.talk-content {
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    text-align: center;
}

.talk-content p {
    margin-bottom: 1.1rem;
    color: var(--slate-gray);
    font-size: 1.08rem;
}

@media (max-width: 600px) {
    .about__photo-container {
        gap: 0.5rem;
    }
    .about__photo {
        height: 100px !important;
        width: 100px !important;
    }
}

/* About Me Section */
.aboutme-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}
.aboutme-text {
    flex: 1 1 320px;
    min-width: 260px;
}
.aboutme-text h2 {
    margin-bottom: 1.2rem;
}
.aboutme-image {
    flex: 0 0 220px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.aboutme-image img {
    width: 220px;
    height: 220px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(232,167,181,0.13);
    background: #fff;
}

/* Education Section */
.education-section {
    margin-bottom: 3rem;
    text-align: center;
}
.education-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}
.education-icons img {
    width: 110px;
    height: 110px;
    object-fit: contain;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

/* Awards Section */
.awards-section {
    max-width: 1000px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
}
.awards-flex {
    display: flex;
    align-items: flex-start;
    gap: 3rem;
    flex-wrap: wrap;
    justify-content: center;
}
.trophy-icon {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-width: 120px;
}
.awards-list-card {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(232,167,181,0.10);
    padding: 2.2rem 2rem;
    min-width: 280px;
    max-width: 500px;
    flex: 1 1 320px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.awards-list {
    list-style: none;
    padding: 0;
    margin: 0;
}
.awards-list li {
    margin-bottom: 1.3rem;
    font-size: 1.13rem;
    color: var(--slate-gray);
    line-height: 1.7;
}
.awards-list a {
    color: #FF7900;
    text-decoration: underline;
    font-size: 0.98em;
    font-weight: 600;
    transition: color 0.2s;
}
.awards-list a:hover {
    color: var(--dusty-rose);
}
@media (max-width: 900px) {
    .awards-flex {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }
    .trophy-icon {
        margin-bottom: 1.2rem;
    }
    .awards-list-card {
        padding: 1.5rem 1rem;
        min-width: 0;
        max-width: 100%;
    }
}

/* Certifications Section */
.certifications-section {
    margin-bottom: 3rem;
    text-align: center;
}
.cert-images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}
.cert-images img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

@media (max-width: 900px) {
    .aboutme-section {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    .aboutme-image img {
        width: 160px;
        height: 160px;
    }
    .education-icons img, .cert-images img {
        width: 90px;
        height: 90px;
    }
    .trophy-icon {
        margin-bottom: 1rem;
    }
    .awards-flex {
        flex-direction: column;
        align-items: center;
        gap: 1.2rem;
    }
}

/* Section Titles and Dividers */
.section-title {
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 2.3rem;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}
.section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--pastel-pink), var(--soft-lilac));
    margin: 1.2rem auto 2.2rem auto;
    border-radius: 2px;
}

/* About Me Grid */
.aboutme-grid {
    display: grid;
    grid-template-columns: 1fr 1.1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
}
.aboutme-img-col {
    display: flex;
    justify-content: center;
    align-items: center;
}
.aboutme-img {
    width: 340px;
    height: 340px;
    object-fit: cover;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(232,167,181,0.18);
    background: #fff;
}
.aboutme-text-col {
    padding: 0 1.5rem;
}
.aboutme-text-col p {
    font-size: 1.18rem;
    color: var(--slate-gray);
    margin-bottom: 1.2rem;
    line-height: 1.8;
}

/* Education Section */
.edu-section {
    max-width: 900px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
}
.edu-list {
    display: flex;
    gap: 2.5rem;
    justify-content: center;
    align-items: stretch;
    flex-wrap: wrap;
}
.edu-item {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(232,167,181,0.10);
    padding: 2.2rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 220px;
    max-width: 340px;
    flex: 1 1 220px;
}
.edu-icon {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 1.2rem;
}
.edu-desc {
    font-size: 1.13rem;
    color: var(--slate-gray);
    text-align: center;
}

/* Awards Section */
.awards-theme {
    max-width: 1000px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
}
.awards-flex-theme {
    display: flex;
    align-items: flex-start;
    gap: 3rem;
    flex-wrap: wrap;
    justify-content: center;
}
.awards-trophy-col {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-width: 120px;
}
.awards-list-theme {
    flex: 1 1 320px;
    list-style: none;
    padding: 0;
    margin: 0;
}
.awards-list-theme li {
    margin-bottom: 1.3rem;
    font-size: 1.13rem;
    color: var(--slate-gray);
    line-height: 1.7;
}
.awards-list-theme a {
    color: var(--dusty-rose);
    text-decoration: underline;
    font-size: 0.98em;
}

/* Certifications Section */
.certs-section {
    max-width: 900px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
    text-align: center;
}
.certs-img-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}
.cert-img {
    width: 180px;
    height: 180px;
    object-fit: contain;
    border-radius: 14px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

/* Experience Section in About */
.about-exp-section {
    max-width: 1100px;
    margin: 0 auto 4rem auto;
    padding: 2.5rem 0;
}

@media (max-width: 900px) {
    .aboutme-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    .aboutme-img {
        width: 220px;
        height: 220px;
    }
    .edu-list {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }
    .awards-flex-theme {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }
    .awards-trophy-col {
        margin-bottom: 1.2rem;
    }
    .certs-img-row {
        gap: 1.2rem;
    }
    .cert-img {
        width: 110px;
        height: 110px;
    }
}

.talks-section {
    max-width: 1200px;
    margin: 0 auto 3rem auto;
    padding: 2.5rem 0;
}
.talks-grid {
    display: grid;
    grid-template-columns: 1fr 1.1fr;
    gap: 3rem;
    align-items: center;
}
.talks-img-col {
    width: 100%;
    max-width: 100%;
}
.talks-img {
    width: 100%;
    max-width: 540px;
    height: auto;
    object-fit: cover;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(255, 121, 0, 1);
    background: #FF7900;
    display: block;
}
.talks-text-col {
    padding: 0 1.5rem;
}
.talks-text-col h2 {
    text-align: left;
}
.talks-text-col .section-divider {
    margin: 1rem 0;
}
.talks-text-col p {
    font-size: 1.08rem;
    color: var(--slate-gray);
    margin-bottom: 1.1rem;
    line-height: 1.7;
}
.talks-text-col a {
    color: #FF7900;
    font-weight: 600;
    transition: color 0.2s;
}
.talks-text-col a:hover {
    color: var(--dusty-rose);
}
@media (max-width: 900px) {
    .talks-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    .talks-img-col {
        width: 100%;
        max-width: 100%;
    }
    .talks-img {
        width: 100%;
        max-width: 100%;
        height: auto;
        margin-bottom: 1.5rem;
        display: block;
    }
    .talks-text-col h2 {
        text-align: center;
    }
    .nav-toggle {
        display: flex;
    }
}