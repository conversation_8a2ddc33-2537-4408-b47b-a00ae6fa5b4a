{"name": "publiceye-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/formidable": "^3.4.5", "@types/js-cookie": "^3.0.6", "axios": "^1.6.2", "date-fns": "^2.30.0", "firebase": "^10.7.1", "firebase-admin": "^13.3.0", "formidable": "^3.5.4", "js-cookie": "^3.0.5", "next": "^14.0.4", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-toastify": "^9.1.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}