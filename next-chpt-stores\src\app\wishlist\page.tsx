"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

// Mock wishlist data - in a real app, this would come from a state management solution
const initialWishlistItems = [
  {
    id: "bathroom-5",
    name: "Bathrobe",
    price: 2000,
    image: "/products/bathrobe.jpg",
    category: "Bathroom"
  },
  {
    id: "electronics-3",
    name: "Headphones",
    price: 2000,
    image: "/products/headphones.jpg",
    category: "Electronics"
  },
  {
    id: "crib-3",
    name: "Games (Monopoly)",
    price: 1800,
    image: "/products/monopoly.jpg",
    category: "Crib"
  }
];

export default function WishlistPage() {
  const [wishlistItems, setWishlistItems] = useState(initialWishlistItems);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const removeItem = (id: string) => {
    setWishlistItems(prevItems => prevItems.filter(item => item.id !== id));

    // Show success message
    setSuccessMessage("Item removed from wishlist!");

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const addToCart = (id: string) => {
    // This would be replaced with actual cart functionality
    console.log(`Added ${id} to cart`);

    // Show success message
    setSuccessMessage("Item added to cart!");

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const moveAllToCart = () => {
    // In a real app, this would add all items to the cart
    console.log('All items moved to cart!');

    // Show success message
    setSuccessMessage("All items added to cart!");

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  return (
    <>
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-primary mb-8">Your Wishlist</h1>

          {/* Success Message */}
          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 flex items-center shadow-md animate-fadeIn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {successMessage}
            </div>
          )}

          {wishlistItems.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-secondary mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
              <h2 className="text-2xl font-semibold text-primary mb-4">Your wishlist is empty</h2>
              <p className="text-secondary mb-6">Save items you love for later by adding them to your wishlist.</p>
              <Link
                href="/life-chapters"
                className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition-colors shadow-md"
              >
                Start Shopping
              </Link>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6 border-b flex justify-between items-center">
                <h2 className="text-xl font-semibold text-primary">Saved Items ({wishlistItems.length})</h2>
                <button
                  onClick={moveAllToCart}
                  className="bg-accent text-white px-4 py-2 rounded hover:bg-opacity-90 transition-colors"
                >
                  Move All to Cart
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {wishlistItems.map(item => (
                  <div key={item.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    <div className="relative h-48">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-contain p-4"
                      />
                    </div>

                    <div className="p-4">
                      <Link
                        href={`/products/${item.id}`}
                        className="text-lg font-semibold text-primary hover:text-accent transition-colors"
                      >
                        {item.name}
                      </Link>

                      <p className="text-sm text-secondary mt-1">
                        {item.category}
                      </p>

                      <p className="text-accent font-bold mt-2 mb-4">
                        KSh {item.price.toFixed(2)}
                      </p>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => addToCart(item.id)}
                          className="flex-grow bg-primary text-white py-2 rounded-lg text-sm font-medium hover:bg-secondary transition-colors shadow-md"
                        >
                          Add to Cart
                        </button>

                        <button
                          onClick={() => removeItem(item.id)}
                          className="p-2 text-red-500 hover:text-red-700 transition-colors border border-gray-200 rounded-lg"
                          title="Remove from Wishlist"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-6 border-t border-gray-200 text-center">
                <Link
                  href="/life-chapters"
                  className="text-primary hover:text-accent transition-colors inline-flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Continue Shopping
                </Link>
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </>
  );
}
