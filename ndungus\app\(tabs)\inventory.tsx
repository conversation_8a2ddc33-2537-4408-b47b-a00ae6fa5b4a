import { MaterialIcons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import { FlatList, Pressable, RefreshControl, ScrollView, StyleSheet, Text, TextInput, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { ItemCard } from '@/components/ui/ItemCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';



export default function InventoryScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);

  // Load products and categories
  const loadData = async () => {
    try {
      setRefreshing(true);
      const productData = await getProducts();
      setProducts(productData);

      const categoryData = await getCategories();
      setCategories(categoryData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    loadData();
  }, []);

  const onRefresh = React.useCallback(() => {
    loadData();
  }, []);

  // Handle delete product
  const handleDeleteProduct = async (id: string) => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteProduct(id);
              if (success) {
                // Refresh products list
                loadData();
              } else {
                Alert.alert('Error', 'Failed to delete product');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete product');
            }
          },
        },
      ]
    );
  };

  // Handle edit product
  const handleEditProduct = (id: string) => {
    router.push(`/inventory/edit/${id}`);
  };

  // Handle view product details
  const handleViewProduct = (id: string) => {
    router.push(`/inventory/${id}`);
  };

  // Filter items based on search query and selected category
  const filteredItems = products.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory ? item.category === selectedCategory : true;

    return matchesSearch && matchesCategory;
  });

  // Render category filter pill
  const renderCategoryPill = (category: string) => {
    const isSelected = selectedCategory === category;

    return (
      <Pressable
        key={category}
        style={[
          styles.categoryPill,
          {
            backgroundColor: isSelected
              ? Colors.primary
              : colorScheme === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.05)',
          },
        ]}
        onPress={() => setSelectedCategory(isSelected ? null : category)}>
        <Text
          style={[
            styles.categoryPillText,
            {
              color: isSelected
                ? Colors.black
                : colorScheme === 'dark'
                  ? Colors.white
                  : Colors.black,
            },
          ]}>
          {category}
        </Text>
        {isSelected && (
          <MaterialIcons name="close" size={16} color={Colors.black} />
        )}
      </Pressable>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerTop}>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Inventory
          </Text>
          <Link href="/inventory/add" asChild>
            <Button
              title="Add Item"
              variant="primary"
              size="sm"
              icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
              onPress={() => {}}
            />
          </Link>
        </View>

        {/* Search Bar */}
        <View
          style={[
            styles.searchContainer,
            {
              backgroundColor: colorScheme === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.05)',
            },
          ]}>
          <MaterialIcons
            name="search"
            size={20}
            color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
          />
          <TextInput
            style={[
              styles.searchInput,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}
            placeholder="Search inventory..."
            placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <Pressable onPress={() => setSearchQuery('')}>
              <MaterialIcons
                name="close"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
            </Pressable>
          )}
        </View>

        {/* Category Filters */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}>
          {categories.map(renderCategoryPill)}
        </ScrollView>
      </GlassCard>

      {/* Inventory List */}
      <FlatList
        data={filteredItems}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ItemCard
            name={item.name}
            image={item.image}
            code={item.code}
            category={item.category}
            buyingPrice={item.buyingPrice}
            sellingPrice={item.sellingPrice}
            units={item.units}
            status={item.status}
            onPress={() => handleViewProduct(item.id)}
            onEdit={() => handleEditProduct(item.id)}
            onDelete={() => handleDeleteProduct(item.id)}
          />
        )}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <GlassCard style={styles.emptyContainer}>
            <MaterialIcons
              name="inventory"
              size={48}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.emptyText,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              No items found
            </Text>
            <Text
              style={[
                styles.emptySubtext,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {searchQuery || selectedCategory
                ? 'Try adjusting your filters'
                : 'Add your first inventory item'}
            </Text>
            {!searchQuery && !selectedCategory && (
              <Link href="/inventory/add" asChild>
                <Button
                  title="Add Item"
                  variant="primary"
                  icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
                  style={{ marginTop: Spacing.md }}
                  onPress={() => {}}
                />
              </Link>
            )}
          </GlassCard>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.bold,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  categoriesContainer: {
    flexDirection: 'row',
    paddingVertical: Spacing.xs,
  },
  categoryPill: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginRight: Spacing.sm,
  },
  categoryPillText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginRight: Spacing.xs,
  },
  listContainer: {
    paddingBottom: Spacing.xl,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
    marginTop: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
});
