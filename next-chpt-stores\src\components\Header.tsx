"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { useCart } from '@/context/CartContext';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { getCartCount } = useCart();

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <Link href="/" className="flex items-center">
          <Image
            src="/logo.png"
            alt="Next Chapter Stores"
            width={50}
            height={50}
            className="mr-2"
          />
          <span className="text-primary font-bold text-xl">Next Chapter Stores</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link href="/life-chapters/first-home" className="text-gray-700 font-medium hover:text-accent transition-colors">
            First Home
          </Link>
          <Link href="/life-chapters/adulting" className="text-gray-700 font-medium hover:text-accent transition-colors">
            Adulting
          </Link>
          <Link href="/life-chapters/relocating" className="text-gray-700 font-medium hover:text-accent transition-colors">
            Relocating
          </Link>
          <Link href="/life-chapters/couples" className="text-gray-700 font-medium hover:text-accent transition-colors">
            Couples
          </Link>
          <Link href="/life-chapters/baby" className="text-gray-700 font-medium hover:text-accent transition-colors">
            Baby
          </Link>
        </nav>

        <div className="flex items-center space-x-4">
          <Link href="/wishlist" className="text-gray-700 hover:text-accent transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </Link>
          <Link href="/cart" className="text-gray-700 hover:text-accent transition-colors relative">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            {getCartCount() > 0 && (
              <span className="absolute -top-2 -right-2 bg-accent text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                {getCartCount()}
              </span>
            )}
          </Link>
          <Link href="/account" className="text-gray-700 hover:text-accent transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </Link>

          {/* Mobile menu button */}
          <button
            className="md:hidden text-gray-700 hover:text-accent"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-white py-4 px-4 shadow-lg">
          <nav className="flex flex-col space-y-4">
            <Link
              href="/life-chapters/first-home"
              className="text-gray-700 font-medium hover:text-accent transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              First Home
            </Link>
            <Link
              href="/life-chapters/adulting"
              className="text-gray-700 font-medium hover:text-accent transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Adulting
            </Link>
            <Link
              href="/life-chapters/relocating"
              className="text-gray-700 font-medium hover:text-accent transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Relocating
            </Link>
            <Link
              href="/life-chapters/couples"
              className="text-gray-700 font-medium hover:text-accent transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Couples
            </Link>
            <Link
              href="/life-chapters/baby"
              className="text-gray-700 font-medium hover:text-accent transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Baby
            </Link>
          </nav>
        </div>
      )}
    </header>
  );
}
