"use client";

import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getLifeChapters } from '@/data/products';

export default function LifeChaptersPage() {
  const lifeChapters = getLifeChapters();

  // Define chapter details
  const chapterDetails = {
    "Independent Living": {
      description: "Everything you need when starting out on your own",
      image: "/life-chapters/first-home.jpg", // We'll keep using the first-home image for now
      color: "from-blue-500 to-blue-700"
    },
    "Relocating": {
      description: "Must-haves for moving to a new city or country",
      image: "/life-chapters/relocating.jpg",
      color: "from-purple-500 to-purple-700"
    },
    "Couples": {
      description: "Build your shared space together",
      image: "/life-chapters/couples.jpg",
      color: "from-red-500 to-red-700"
    },
    "Baby": {
      description: "Everything you need for your new arrival",
      image: "/life-chapters/baby.jpg",
      color: "from-yellow-500 to-yellow-700"
    }
  };

  return (
    <>
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h1 className="text-4xl font-bold text-primary mb-4">Life Chapters</h1>
            <p className="text-gray-700 text-lg max-w-3xl mx-auto">
              We've organized our products by life's major transitions to help you find exactly what you need for your next chapter.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {lifeChapters.map(chapter => {
              const details = chapterDetails[chapter as keyof typeof chapterDetails] || {
                description: `Products for your ${chapter} journey`,
                image: "/placeholder.jpg",
                color: "from-primary to-secondary"
              };

              return (
                <Link
                  key={chapter}
                  href={`/life-chapters/${chapter.toLowerCase().replace(/\s+/g, '-')}`}
                  className="group"
                >
                  <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full">
                    <div className="relative h-80">
                      <div className={`absolute inset-0 bg-gradient-to-t ${details.color} opacity-90 z-10`}></div>
                      <Image
                        src={details.image}
                        alt={chapter}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 z-20 flex items-center justify-center">
                        <h2 className="text-3xl font-bold text-white text-center px-4 drop-shadow-lg">{chapter}</h2>
                      </div>
                    </div>
                    <div className="p-6">
                      <p className="text-gray-700 mb-4">
                        {details.description}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-primary font-semibold">Explore Chapter</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-accent group-hover:translate-x-1 transition-transform"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
}
