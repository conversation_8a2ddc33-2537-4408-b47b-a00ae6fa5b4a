import { MaterialIcons } from '@expo/vector-icons';
import { <PERSON> } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, View, ScrollView, Pressable } from 'react-native';

import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Menu items for the More screen
const menuItems = [
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'View sales trends and performance metrics',
    icon: 'analytics',
    route: '/analytics',
  },
  {
    id: 'daily-notes',
    title: 'Daily Notes',
    description: 'Add and view important daily notes',
    icon: 'event-note',
    route: '/daily-notes',
  },
  {
    id: 'potential-sales',
    title: 'Potential Sales',
    description: 'Track customer requests and potential sales',
    icon: 'lightbulb',
    route: '/potential-sales',
  },
  {
    id: 'settings',
    title: 'Settings',
    description: 'Configure app preferences and account settings',
    icon: 'settings',
    route: '/settings',
  },
  {
    id: 'sync',
    title: 'Sync Data',
    description: 'Sync offline data with the server',
    icon: 'sync',
    route: '/sync',
  },
  {
    id: 'help',
    title: 'Help & Support',
    description: 'Get help and contact support',
    icon: 'help',
    route: '/help',
  },
];

export default function MoreScreen() {
  const colorScheme = useColorScheme() ?? 'light';

  // Render a menu item
  const renderMenuItem = (item: typeof menuItems[0]) => {
    return (
      <Link key={item.id} href={item.route} asChild>
        <Pressable>
          {({ pressed }) => (
            <GlassCard
              style={[
                styles.menuItem,
                {
                  opacity: pressed ? 0.8 : 1,
                  transform: [{ scale: pressed ? 0.98 : 1 }],
                },
              ]}>
              <View
                style={[
                  styles.iconContainer,
                  {
                    backgroundColor: colorScheme === 'dark'
                      ? 'rgba(255, 215, 0, 0.2)'
                      : 'rgba(255, 215, 0, 0.1)',
                  },
                ]}>
                <MaterialIcons name={item.icon as any} size={24} color={Colors.primary} />
              </View>
              <View style={styles.menuItemContent}>
                <Text
                  style={[
                    styles.menuItemTitle,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  {item.title}
                </Text>
                <Text
                  style={[
                    styles.menuItemDescription,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  {item.description}
                </Text>
              </View>
              <MaterialIcons
                name="chevron-right"
                size={24}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
            </GlassCard>
          )}
        </Pressable>
      </Link>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <View style={styles.header}>
        <Text
          style={[
            styles.title,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          More
        </Text>
      </View>

      {/* User Profile Card */}
      <GlassCard style={styles.profileCard}>
        <View style={styles.profileHeader}>
          <View
            style={[
              styles.profileImageContainer,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}>
            <MaterialIcons
              name="person"
              size={40}
              color={colorScheme === 'dark' ? Colors.gray300 : Colors.gray700}
            />
          </View>
          <View style={styles.profileInfo}>
            <Text
              style={[
                styles.profileName,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              John Doe
            </Text>
            <Text
              style={[
                styles.profileEmail,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              <EMAIL>
            </Text>
          </View>
        </View>
        <Link href="/profile" asChild>
          <Pressable
            style={[
              styles.editProfileButton,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(0, 0, 0, 0.05)',
              },
            ]}>
            <Text
              style={[
                styles.editProfileText,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Edit Profile
            </Text>
            <MaterialIcons
              name="edit"
              size={16}
              color={colorScheme === 'dark' ? Colors.gray300 : Colors.gray700}
            />
          </Pressable>
        </Link>
      </GlassCard>

      {/* Connection Status */}
      <View
        style={[
          styles.connectionStatus,
          {
            backgroundColor: Colors.success + (colorScheme === 'dark' ? '30' : '15'),
          },
        ]}>
        <MaterialIcons name="wifi" size={16} color={Colors.success} />
        <Text
          style={[
            styles.connectionText,
            { color: Colors.success },
          ]}>
          Online - All data is synced
        </Text>
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        {menuItems.map(renderMenuItem)}
      </View>

      {/* App Info */}
      <View style={styles.appInfo}>
        <Text
          style={[
            styles.appVersion,
            { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
          ]}>
          Ndungus Shop v1.0.0
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.bold,
  },
  profileCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  profileImageContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
    marginBottom: Spacing.xs,
  },
  profileEmail: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    gap: Spacing.xs,
  },
  editProfileText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.lg,
    gap: Spacing.xs,
  },
  connectionText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  menuContainer: {
    gap: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 2,
  },
  menuItemDescription: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  appInfo: {
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  appVersion: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
});
