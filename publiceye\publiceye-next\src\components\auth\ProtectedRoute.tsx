import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('ProtectedRoute - Auth state:', { user, loading });
    if (!loading && !user) {
      console.log('User not authenticated, redirecting to sign in...');
      // Redirect to sign in page with return URL
      const returnUrl = encodeURIComponent(router.asPath);
      console.log('Return URL:', returnUrl);

      // Use window.location for more reliable redirect
      window.location.href = `/auth/signin?returnUrl=${returnUrl}`;
    }
  }, [user, loading, router]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
      </div>
    );
  }

  // If user is authenticated, render children
  return user ? <>{children}</> : null;
};

export default ProtectedRoute;
