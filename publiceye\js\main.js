// Main JavaScript file for Public Eye

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    console.log('Public Eye application initialized');

    // Set up event listeners
    setupEventListeners();

    // Load initial cases
    loadCases('popular');
});

// Set up event listeners for interactive elements
function setupEventListeners() {
    // Category tab switching
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Load cases for the selected category
            const category = this.getAttribute('data-category');
            loadCases(category);
        });
    });

    // Load more button
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            loadMoreCases();
        });
    }

    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    const searchInput = document.querySelector('.search-container input');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', function() {
            searchCases(searchInput.value);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCases(searchInput.value);
            }
        });
    }
}

// Load cases based on category
function loadCases(category) {
    const casesContainer = document.getElementById('cases-container');

    if (!casesContainer) return;

    // Clear current cases
    casesContainer.innerHTML = '';

    // Show loading state
    casesContainer.innerHTML = '<div class="loading">Loading cases...</div>';

    // In a real application, this would be an API call
    // For now, we'll use sample data
    setTimeout(() => {
        casesContainer.innerHTML = '';

        // Get sample cases (in a real app, this would come from an API)
        const cases = getSampleCases(category);

        // Render each case
        cases.forEach(caseItem => {
            const caseCard = createCaseCard(caseItem);
            casesContainer.appendChild(caseCard);
        });
    }, 500); // Simulate network delay
}

// Load more cases (pagination)
function loadMoreCases() {
    const casesContainer = document.getElementById('cases-container');

    if (!casesContainer) return;

    // In a real application, this would load the next page of results
    // For now, we'll just add more sample cases
    const moreCases = getSampleCases('more');

    moreCases.forEach(caseItem => {
        const caseCard = createCaseCard(caseItem);
        casesContainer.appendChild(caseCard);
    });
}

// Search cases
function searchCases(query) {
    if (!query) return;

    console.log(`Searching for: ${query}`);

    const casesContainer = document.getElementById('cases-container');

    if (!casesContainer) return;

    // Clear current cases
    casesContainer.innerHTML = '';

    // Show loading state
    casesContainer.innerHTML = `<div class="loading">Searching for "${query}"...</div>`;

    // In a real application, this would be an API call with the search query
    // For now, we'll use sample data and filter it
    setTimeout(() => {
        casesContainer.innerHTML = '';

        // Get sample cases and filter them (in a real app, the server would do this)
        const allCases = [...getSampleCases('popular'), ...getSampleCases('recent')];
        const filteredCases = allCases.filter(caseItem =>
            caseItem.title.toLowerCase().includes(query.toLowerCase()) ||
            caseItem.excerpt.toLowerCase().includes(query.toLowerCase())
        );

        if (filteredCases.length === 0) {
            casesContainer.innerHTML = `<div class="no-results">No cases found matching "${query}"</div>`;
            return;
        }

        // Render each case
        filteredCases.forEach(caseItem => {
            const caseCard = createCaseCard(caseItem);
            casesContainer.appendChild(caseCard);
        });
    }, 500); // Simulate network delay
}

// Create a case card element
function createCaseCard(caseItem) {
    const card = document.createElement('div');
    card.className = 'case-card';

    // Create status badge
    const statusBadge = document.createElement('div');
    statusBadge.className = 'case-status-badge';
    statusBadge.setAttribute('data-status', caseItem.status);
    statusBadge.textContent = getStatusText(caseItem.status);

    // Create title
    const title = document.createElement('h3');
    title.textContent = caseItem.title;

    // Create media preview
    const mediaPreview = document.createElement('div');
    mediaPreview.className = 'case-media-preview';

    const image = document.createElement('img');
    image.src = caseItem.image;
    image.alt = caseItem.title;
    mediaPreview.appendChild(image);

    // Create excerpt
    const excerpt = document.createElement('p');
    excerpt.className = 'case-excerpt';
    excerpt.textContent = caseItem.excerpt;

    // Create meta information
    const meta = document.createElement('div');
    meta.className = 'case-meta';

    const followers = document.createElement('span');
    followers.className = 'followers';
    followers.innerHTML = `<i class="fas fa-user-friends"></i> ${caseItem.followers} followers`;

    const updates = document.createElement('span');
    updates.className = 'updates';
    updates.innerHTML = `<i class="fas fa-history"></i> ${caseItem.updates} updates`;

    meta.appendChild(followers);
    meta.appendChild(updates);

    // Create progress bar
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';

    const progressStep = document.createElement('div');
    progressStep.className = 'progress-step';
    progressStep.setAttribute('data-step', caseItem.status);
    progressStep.style.width = getProgressWidth(caseItem.status);

    progressBar.appendChild(progressStep);

    // Add click event to navigate to case details
    card.addEventListener('click', function() {
        window.location.href = `case-details.html?id=${caseItem.id}`;
    });

    // Assemble the card
    card.appendChild(statusBadge);
    card.appendChild(mediaPreview);
    card.appendChild(title);
    card.appendChild(excerpt);
    card.appendChild(meta);
    card.appendChild(progressBar);

    return card;
}

// Get status text based on status code
function getStatusText(status) {
    const statusMap = {
        'report': 'Reported',
        'investigate': 'Under Investigation',
        'arrest': 'Arrest Made',
        'pretrial': 'Pre-Trial',
        'trial': 'Trial Ongoing',
        'verdict': 'Verdict Delivered'
    };

    return statusMap[status] || 'Unknown Status';
}

// Get progress width percentage based on status
function getProgressWidth(status) {
    const progressMap = {
        'report': '16%',
        'investigate': '32%',
        'arrest': '48%',
        'pretrial': '64%',
        'trial': '82%',
        'verdict': '100%'
    };

    return progressMap[status] || '0%';
}
