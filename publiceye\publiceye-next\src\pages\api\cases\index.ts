import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';
import { Case } from '@/types/case';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests for creating cases
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { 
      title, 
      location, 
      description, 
      sourceUrl, 
      currentStage, 
      createdBy, 
      mediaUrls 
    } = req.body;

    // Validate required fields
    if (!title || !description || !currentStage || !createdBy) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Validate mediaUrls if provided
    if (mediaUrls && !Array.isArray(mediaUrls)) {
      return res.status(400).json({ error: 'mediaUrls must be an array' });
    }

    // Create case data object
    const newCase: Omit<Case, 'id'> = {
      title,
      location: location || '',
      description,
      sourceUrl: sourceUrl || '',
      currentStage,
      createdBy,
      mediaUrls: mediaUrls || [], // These should be Cloudinary URLs
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      followers: {},
      contributions: {},
      isVerified: false,
      isFlagged: false,
    };

    const db = getAdminDatabase();
    const casesRef = db.ref('cases');
    const newCaseRef = casesRef.push();

    // Set the data at the new reference
    await newCaseRef.set(newCase);

    // Return the new case ID and the created case data
    return res.status(201).json({
      success: true,
      caseId: newCaseRef.key,
      case: {
        id: newCaseRef.key,
        ...newCase
      }
    });
  } catch (error) {
    console.error('Error creating case:', error);
    return res.status(500).json({ error: 'Failed to create case' });
  }
}
