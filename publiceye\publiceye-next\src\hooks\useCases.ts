import { useState } from 'react';
import axios from 'axios';
import { Case, Contribution, CaseStage, GroupedContributions } from '@/types/case';
import { uploadMultipleImages } from '@/lib/cloudinary';
import { ref, get, query, orderByChild, equalTo } from 'firebase/database';
import { db } from '@/lib/firebase';
import { auth } from '@/lib/firebase';

export const useCases = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Get popular cases
  const getPopularCases = async (limitCount = 10): Promise<Case[]> => {
    setLoading(true);
    setError(null);

    try {
      const casesRef = ref(db, 'cases');
      const snapshot = await get(casesRef);
      
      if (snapshot.exists()) {
        const casesData = snapshot.val();
        const cases = Object.entries(casesData).map(([id, data]) => ({
          id,
          ...(data as Omit<Case, 'id'>)
        }));
        
        // Sort by number of followers (popularity)
        return cases
          .sort((a, b) => {
            const followersA = Object.keys(a.followers || {}).length;
            const followersB = Object.keys(b.followers || {}).length;
            return followersB - followersA;
          })
          .slice(0, limitCount);
      }
      return [];
    } catch (err) {
      setError('Failed to fetch popular cases');
      console.error(err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Get recent cases
  const getRecentCases = async (limitCount = 10): Promise<Case[]> => {
    setLoading(true);
    setError(null);

    try {
      const casesRef = ref(db, 'cases');
      const snapshot = await get(casesRef);
      
      if (snapshot.exists()) {
        const casesData = snapshot.val();
        const cases = Object.entries(casesData).map(([id, data]) => ({
          id,
          ...(data as Omit<Case, 'id'>)
        }));
        
        // Sort by creation date (newest first)
        return cases
          .sort((a, b) => {
            const dateA = new Date(a.createdAt).getTime();
            const dateB = new Date(b.createdAt).getTime();
            return dateB - dateA;
          })
          .slice(0, limitCount);
      }
      return [];
    } catch (err) {
      setError('Failed to fetch recent cases');
      console.error(err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Get case by ID
  const getCaseById = async (caseId: string): Promise<Case | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`/api/cases/${caseId}`);

      if (response.data) {
        return response.data as Case;
      } else {
        setError('Case not found');
        return null;
      }
    } catch (err) {
      setError('Failed to fetch case');
      console.error(err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Create a new case
  const createCase = async (
    caseData: Omit<Case, 'id' | 'createdAt' | 'updatedAt' | 'followers' | 'contributions' | 'isVerified' | 'isFlagged'>,
    mediaFiles: File[]
  ): Promise<string | null> => {
    setLoading(true);
    setError(null);

    try {
      // Upload media files to Cloudinary
      let mediaUrls: string[] = [];

      if (mediaFiles.length > 0) {
        // Upload all media files in parallel
        mediaUrls = await uploadMultipleImages(mediaFiles);
      }

      // Create case via API
      const response = await axios.post('/api/cases', {
        ...caseData,
        mediaUrls
      });

      if (response.data && response.data.success && response.data.caseId) {
        return response.data.caseId;
      } else {
        throw new Error('Failed to create case');
      }
    } catch (err) {
      setError('Failed to create case');
      console.error(err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Add a contribution to a case
  const addContribution = async (
    caseId: string,
    contribution: Omit<Contribution, 'id' | 'createdAt' | 'isVerified' | 'isFlagged' | 'flagReason'>,
    mediaFiles: File[]
  ): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // Upload media files to Cloudinary first
      let mediaUrls: string[] = [];
      if (mediaFiles.length > 0) {
        mediaUrls = await uploadMultipleImages(mediaFiles);
      }

      // Send contribution data with media URLs
      const response = await axios.post(`/api/cases/${caseId}/contribution`, {
        ...contribution,
        mediaUrls
      });

      return response.data.success;
    } catch (err) {
      setError('Failed to add contribution');
      console.error(err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Group contributions by stage
  const groupContributionsByStage = (contributionsObj: Record<string, Contribution> | Contribution[]): GroupedContributions => {
    const grouped: GroupedContributions = {};

    // Convert contributions object to array if needed
    const contributions = Array.isArray(contributionsObj)
      ? contributionsObj
      : Object.values(contributionsObj || {});

    contributions.forEach((contribution) => {
      if (!grouped[contribution.stage]) {
        grouped[contribution.stage] = [];
      }

      grouped[contribution.stage]?.push(contribution);
    });

    // Sort contributions within each stage by date (newest first)
    Object.keys(grouped).forEach((stage) => {
      const stageKey = stage as CaseStage;
      grouped[stageKey]?.sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });
    });

    return grouped;
  };

  const getUserCases = async (): Promise<Case[]> => {
    setLoading(true);
    setError(null);

    try {
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await axios.get('/api/cases/user', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data as Case[];
    } catch (err) {
      setError('Failed to fetch user cases');
      console.error(err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getAllCases = async (): Promise<Case[]> => {
    setLoading(true);
    setError(null);

    try {
      const casesRef = ref(db, 'cases');
      const snapshot = await get(casesRef);
      
      if (snapshot.exists()) {
        const casesData = snapshot.val();
        const cases = Object.entries(casesData).map(([id, data]) => ({
          id,
          ...(data as Omit<Case, 'id'>)
        }));
        return cases;
      }
      return [];
    } catch (err) {
      setError('Failed to fetch cases');
      console.error(err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getPopularCases,
    getRecentCases,
    getCaseById,
    createCase,
    addContribution,
    groupContributionsByStage,
    getUserCases,
    getAllCases
  };
};
