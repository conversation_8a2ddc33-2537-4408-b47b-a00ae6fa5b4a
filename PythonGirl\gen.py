import pandas as pd
import numpy as np
from random import choices, randint, uniform

# Set random seed for reproducibility
np.random.seed(42)

# Define parameters
n_rows = 5000
weights_range = (1, 15)  # tons
distance_range = (10, 600)  # miles
weather_options = ['Clear', 'Rain', 'Snow']
time_options = ['Morning', 'Afternoon', 'Night']

# Generate base data
data = {
    'shipment_id': range(1, n_rows + 1),
    'distance_miles': [randint(*distance_range) if np.random.random() > 0.05 else np.nan for _ in range(n_rows)],
    'cargo_weight_tons': [round(uniform(*weights_range), 1) if np.random.random() > 0.03 else np.nan for _ in range(n_rows)],
    'weather': choices(weather_options, weights=[0.6, 0.3, 0.1], k=n_rows),
    'time_of_day': choices(time_options, weights=[0.4, 0.35, 0.25], k=n_rows)
}

# Introduce missing values in weather and time (2% each)
for i in range(n_rows):
    if np.random.random() < 0.02:
        data['weather'][i] = np.nan
    if np.random.random() < 0.02:
        data['time_of_day'][i] = np.nan

# Generate delivery status based on rules with some noise
delivery_status = []
for i in range(n_rows):
    distance = data['distance_miles'][i] if not pd.isna(data['distance_miles'][i]) else distance_range[1]
    weight = data['cargo_weight_tons'][i] if not pd.isna(data['cargo_weight_tons'][i]) else weights_range[1]
    weather = data['weather'][i]
    time = data['time_of_day'][i]
    
    # Base probabilities
    if pd.isna(weather) or pd.isna(time):
        # If critical data is missing, make it 50/50
        prob_delayed = 0.5
    else:
        # Strong rule: Snow + Night + Heavy = Almost always delayed
        if weather == 'Snow' and time == 'Night' and weight > 8:
            prob_delayed = 0.95
        
        # Strong rule: Clear + Morning + Light = Almost always on-time
        elif weather == 'Clear' and time == 'Morning' and distance < 100 and weight < 5:
            prob_delayed = 0.05
        
        # Other rules
        else:
            prob_delayed = 0.3  # base
            prob_delayed += (distance / 1000)  # longer distance increases risk
            prob_delayed += (weight / 50)  # heavier cargo increases risk
            if weather == 'Rain': prob_delayed += 0.15
            if weather == 'Snow': prob_delayed += 0.3
            if time == 'Afternoon': prob_delayed += 0.1
            if time == 'Night': prob_delayed += 0.2
    
    # Add some randomness
    prob_delayed = min(max(prob_delayed + (np.random.randn() * 0.1), 0.05), 0.95)
    
    # Determine status
    delivery_status.append('Delayed' if np.random.random() < prob_delayed else 'On-Time')

data['delivery_status'] = delivery_status

# Create DataFrame
df = pd.DataFrame(data)

# Save to CSV
df.to_csv('trucking_shipments.csv', index=False)

print("Dataset generated successfully!")