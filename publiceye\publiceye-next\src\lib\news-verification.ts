import axios from 'axios';

// Types for verification results
export interface VerificationResult {
  isReliable: boolean;
  confidenceScore: number;
  reasons: string[];
  sourceReliability: 'high' | 'medium' | 'low' | 'unknown';
  needsHumanReview: boolean;
}

// List of known reliable news sources
const reliableSources = [
  'bbc.com', 'bbc.co.uk', 'reuters.com', 'apnews.com', 'npr.org',
  'washingtonpost.com', 'nytimes.com', 'wsj.com', 'economist.com',
  'theguardian.com', 'cnn.com', 'nbcnews.com', 'cbsnews.com', 'abcnews.go.com'
];

// List of known unreliable news sources
const unreliableSources = [
  'infowars.com', 'breitbart.com', 'naturalnews.com', 'dailycaller.com',
  'worldtruth.tv', 'beforeitsnews.com', 'zerohedge.com'
];

// Keywords that might indicate fake news
const suspiciousKeywords = [
  'shocking truth', 'they don\'t want you to know', 'government cover-up',
  'conspiracy', 'what the media isn\'t telling you', 'secret cure',
  'doctors hate this', 'miracle', 'this will change everything'
];

// Check if a URL is from a known reliable source
const isReliableSource = (url: string): boolean => {
  try {
    const hostname = new URL(url).hostname;
    return reliableSources.some(source => hostname.includes(source));
  } catch {
    return false;
  }
};

// Check if a URL is from a known unreliable source
const isUnreliableSource = (url: string): boolean => {
  try {
    const hostname = new URL(url).hostname;
    return unreliableSources.some(source => hostname.includes(source));
  } catch {
    return false;
  }
};

// Check if text contains suspicious keywords
const containsSuspiciousKeywords = (text: string): string[] => {
  const lowerText = text.toLowerCase();
  return suspiciousKeywords.filter(keyword => lowerText.includes(keyword.toLowerCase()));
};

// Basic text analysis for sensationalism
const analyzeTextSensationalism = (text: string): number => {
  // Count exclamation marks
  const exclamationCount = (text.match(/!/g) || []).length;
  
  // Count all caps words (as a sign of sensationalism)
  const allCapsCount = (text.match(/\b[A-Z]{2,}\b/g) || []).length;
  
  // Count question marks (rhetorical questions are common in sensationalist content)
  const questionCount = (text.match(/\?/g) || []).length;
  
  // Calculate a sensationalism score (0-1)
  const wordCount = text.split(/\s+/).length;
  const sensationalismScore = Math.min(
    1, 
    (exclamationCount * 0.2 + allCapsCount * 0.3 + questionCount * 0.1) / (wordCount / 10)
  );
  
  return sensationalismScore;
};

// Verify content using Google Fact Check API (if available)
const checkWithFactCheckAPI = async (text: string): Promise<any> => {
  try {
    // This is a placeholder - in a real implementation, you would use an actual fact-checking API
    // For example, Google's Fact Check API: https://developers.google.com/fact-check/tools/api
    
    // For now, we'll simulate an API response
    return {
      claims: [],
      factChecks: []
    };
  } catch (error) {
    console.error('Error checking with fact check API:', error);
    return null;
  }
};

// Main verification function
export const verifyContent = async (
  text: string, 
  sourceUrl?: string
): Promise<VerificationResult> => {
  const result: VerificationResult = {
    isReliable: true,
    confidenceScore: 0.5,
    reasons: [],
    sourceReliability: 'unknown',
    needsHumanReview: false
  };
  
  // Check source URL reliability
  if (sourceUrl) {
    if (isReliableSource(sourceUrl)) {
      result.sourceReliability = 'high';
      result.confidenceScore += 0.3;
      result.reasons.push('Content is from a reliable source');
    } else if (isUnreliableSource(sourceUrl)) {
      result.sourceReliability = 'low';
      result.confidenceScore -= 0.3;
      result.isReliable = false;
      result.reasons.push('Content is from a known unreliable source');
    }
  }
  
  // Check for suspicious keywords
  const suspiciousWords = containsSuspiciousKeywords(text);
  if (suspiciousWords.length > 0) {
    result.confidenceScore -= 0.1 * Math.min(suspiciousWords.length, 5);
    result.reasons.push(`Contains suspicious phrases: ${suspiciousWords.join(', ')}`);
  }
  
  // Analyze text for sensationalism
  const sensationalismScore = analyzeTextSensationalism(text);
  if (sensationalismScore > 0.3) {
    result.confidenceScore -= sensationalismScore * 0.2;
    result.reasons.push('Content contains sensationalist language');
  }
  
  // Check with fact-checking API (if implemented)
  const factCheckResult = await checkWithFactCheckAPI(text);
  
  // Determine final reliability
  result.confidenceScore = Math.max(0, Math.min(1, result.confidenceScore));
  result.isReliable = result.confidenceScore >= 0.5;
  
  // Determine if human review is needed
  result.needsHumanReview = (
    result.confidenceScore > 0.3 && 
    result.confidenceScore < 0.7
  );
  
  return result;
};
