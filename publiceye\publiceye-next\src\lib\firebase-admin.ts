import * as admin from 'firebase-admin';
import * as fs from 'fs';
import * as path from 'path';

// Initialize Firebase Admin SDK for server-side operations
export function getServerFirebase() {
  if (admin.apps.length > 0) {
    return admin.apps[0];
  }

  try {
    // Try to use the service account file
    const serviceAccountPath = path.join(process.cwd(), 'firebase-service-account.json');
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));

    const firebaseAdminConfig = {
      credential: admin.credential.cert(serviceAccount),
      databaseURL: process.env.FIREBASE_DATABASE_URL,
    };

    return admin.initializeApp(firebaseAdminConfig);
  } catch (error) {
    console.error('Failed to initialize Firebase Admin:', error);
    throw error;
  }
}

// Get Firebase Admin Database instance
export function getAdminDatabase() {
  const app = getServerFirebase();
  return admin.database(app);
}
