import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Pressable, RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native';

import { GlassCard } from '@/components/ui/GlassCard';
import { MetricCard } from '@/components/ui/MetricCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
    CategoryBreakdown,
    DashboardSummary,
    getCategoryBreakdown,
    getDailySalesTrend,
    getDashboardSummary,
    getTopProducts,
    SalesTrend,
    TopProduct
} from '@/services/analytics';

type TimeRange = 'week' | 'month' | 'quarter' | 'year';

// Map time range to days
const timeRangeToDays = {
  week: 7,
  month: 30,
  quarter: 90,
  year: 365,
};

export default function AnalyticsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [timeRange, setTimeRange] = useState<TimeRange>('week');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [summary, setSummary] = useState<DashboardSummary>({
    totalSales: 0,
    totalRevenue: 0,
    totalProfit: 0,
    salesCount: 0,
    averageOrderValue: 0,
    mostProfitableDay: 'N/A',
    inventoryValue: 0,
    lowStockCount: 0,
  });
  const [salesTrend, setSalesTrend] = useState<SalesTrend[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [categories, setCategories] = useState<CategoryBreakdown[]>([]);

  // Load analytics data
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);

      // Get data based on selected time range
      const days = timeRangeToDays[timeRange];

      // Load data in parallel
      const [summaryData, trendData, productsData, categoriesData] = await Promise.all([
        getDashboardSummary(days),
        getDailySalesTrend(days),
        getTopProducts(days),
        getCategoryBreakdown(days),
      ]);

      // Update state
      setSummary(summaryData);
      setSalesTrend(trendData);
      setTopProducts(productsData);
      setCategories(categoriesData);

    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load data when time range changes
  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  // Handle refresh
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadAnalyticsData().finally(() => {
      setRefreshing(false);
    });
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };

  // Render time range selector
  const renderTimeRangeSelector = () => {
    return (
      <View style={styles.timeRangeContainer}>
        {(['week', 'month', 'quarter', 'year'] as const).map((range) => (
          <Pressable
            key={range}
            style={[
              styles.timeRangeButton,
              timeRange === range && {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(255, 215, 0, 0.2)'
                  : 'rgba(255, 215, 0, 0.1)',
                borderColor: Colors.primary,
                borderWidth: 1,
              },
            ]}
            onPress={() => setTimeRange(range)}>
            <Text
              style={[
                styles.timeRangeText,
                {
                  color: timeRange === range
                    ? Colors.primary
                    : colorScheme === 'dark'
                      ? Colors.gray300
                      : Colors.gray700,
                  fontFamily: timeRange === range
                    ? Typography.fontFamily.semiBold
                    : Typography.fontFamily.regular,
                },
              ]}>
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </Text>
          </Pressable>
        ))}
      </View>
    );
  };

  // Render bar chart
  const renderBarChart = () => {
    if (salesTrend.length === 0) {
      return (
        <View style={styles.emptyChartContainer}>
          <Text
            style={[
              styles.emptyText,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            No sales data available
          </Text>
        </View>
      );
    }

    const maxAmount = Math.max(...salesTrend.map(item => item.amount));

    return (
      <View style={styles.chartContainer}>
        <Text
          style={[
            styles.chartTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Daily Sales
        </Text>
        <View style={styles.barChartContainer}>
          {salesTrend.map((item, index) => {
            const barHeight = maxAmount > 0 ? (item.amount / maxAmount) * 150 : 0;

            return (
              <View key={index} style={styles.barContainer}>
                <View
                  style={[
                    styles.bar,
                    {
                      height: barHeight || 5, // Minimum height for visibility
                      backgroundColor: Colors.primary,
                    },
                  ]}
                />
                <Text
                  style={[
                    styles.barLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  {item.label}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // Render top categories
  const renderTopCategories = () => {
    if (categories.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text
            style={[
              styles.emptyText,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            No category data available
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.topCategoriesContainer}>
        <Text
          style={[
            styles.sectionTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Top Categories
        </Text>
        {categories.map((item, index) => (
          <View key={index} style={styles.categoryRow}>
            <Text
              style={[
                styles.categoryName,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {item.category}
            </Text>
            <View style={styles.categoryBarContainer}>
              <View
                style={[
                  styles.categoryBar,
                  {
                    width: `${item.percentage}%`,
                    backgroundColor: Colors.primary,
                  },
                ]}
              />
            </View>
            <Text
              style={[
                styles.categoryPercentage,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {item.percentage}%
            </Text>
          </View>
        ))}
      </View>
    );
  };

  // Render top products
  const renderTopProducts = () => {
    if (topProducts.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text
            style={[
              styles.emptyText,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            No product data available
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.topProductsContainer}>
        <Text
          style={[
            styles.sectionTitle,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          Top Products
        </Text>
        {topProducts.map((item, index) => (
          <GlassCard key={index} style={styles.productCard}>
            <View style={styles.productCardHeader}>
              <Text
                style={[
                  styles.productName,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                {item.name}
              </Text>
              <Text
                style={[
                  styles.productUnits,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {item.units} units
              </Text>
            </View>
            <Text
              style={[
                styles.productProfit,
                { color: Colors.success },
              ]}>
              {formatCurrency(item.profit)}
            </Text>
          </GlassCard>
        ))}
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Analytics',
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.white,
          },
          headerTintColor: colorScheme === 'dark' ? Colors.white : Colors.black,
          headerShadowVisible: false,
        }}
      />
      <ScrollView
        style={[
          styles.container,
          { backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.gray50 },
        ]}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        {/* Time Range Selector */}
        {renderTimeRangeSelector()}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
          </View>
        ) : (
          <>
            {/* Summary Metrics */}
            <View style={styles.metricsContainer}>
              <MetricCard
                title="Total Sales"
                value={formatCurrency(summary.totalRevenue)}
                icon="attach-money"
                style={{ flex: 1 }}
              />
              <MetricCard
                title="Total Profit"
                value={formatCurrency(summary.totalProfit)}
                icon="trending-up"
                iconColor={Colors.success}
                style={{ flex: 1 }}
              />
            </View>

            <View style={styles.metricsContainer}>
              <MetricCard
                title="Total Orders"
                value={summary.salesCount.toString()}
                icon="shopping-bag"
                style={{ flex: 1 }}
              />
              <MetricCard
                title="Avg. Order Value"
                value={formatCurrency(summary.averageOrderValue)}
                icon="receipt"
                style={{ flex: 1 }}
              />
            </View>

            {/* Bar Chart */}
            <GlassCard style={styles.chartCard}>
              {renderBarChart()}
            </GlassCard>

            {/* Top Categories */}
            <GlassCard style={styles.categoriesCard}>
              {renderTopCategories()}
            </GlassCard>

            {/* Top Products */}
            {renderTopProducts()}

            {/* Most Profitable Day */}
            <GlassCard style={styles.profitableDayCard}>
              <View style={styles.profitableDayContent}>
                <MaterialIcons
                  name="emoji-events"
                  size={32}
                  color={Colors.primary}
                />
                <View style={styles.profitableDayTextContainer}>
                  <Text
                    style={[
                      styles.profitableDayLabel,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    Most Profitable Day
                  </Text>
                  <Text
                    style={[
                      styles.profitableDayValue,
                      { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                    ]}>
                    {summary.mostProfitableDay}
                  </Text>
                </View>
              </View>
            </GlassCard>
          </>
        )}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingBottom: Spacing['2xl'],
  },
  timeRangeContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: BorderRadius.md,
  },
  timeRangeText: {
    fontSize: Typography.fontSize.sm,
  },
  loadingContainer: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: Spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  emptyChartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 150,
  },
  emptyText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    textAlign: 'center',
  },
  metricsContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  chartCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  chartContainer: {
    marginBottom: Spacing.sm,
  },
  chartTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  barChartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 180,
    justifyContent: 'space-between',
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 20,
    borderRadius: BorderRadius.sm,
  },
  barLabel: {
    marginTop: Spacing.xs,
    fontSize: Typography.fontSize.xs,
  },
  categoriesCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  topCategoriesContainer: {
    gap: Spacing.sm,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.sm,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  categoryName: {
    width: 100,
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  categoryBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: BorderRadius.full,
    overflow: 'hidden',
  },
  categoryBar: {
    height: '100%',
  },
  categoryPercentage: {
    width: 40,
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'right',
  },
  topProductsContainer: {
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  productCard: {
    padding: Spacing.sm,
  },
  productCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  productName: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
  },
  productUnits: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  productProfit: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  profitableDayCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  profitableDayContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  profitableDayTextContainer: {
    flex: 1,
  },
  profitableDayLabel: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  profitableDayValue: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
});
