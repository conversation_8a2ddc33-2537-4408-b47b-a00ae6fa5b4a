"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useCart } from '@/context/CartContext';

export default function CartPage() {
  const { cartItems, updateQuantity, removeFromCart, getCartTotal, clearCart } = useCart();
  const [promoCode, setPromoCode] = useState('');
  const [promoApplied, setPromoApplied] = useState(false);
  const [discount, setDiscount] = useState(0);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handleUpdateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;

    updateQuantity(id, newQuantity);

    // Show success message
    setSuccessMessage("Cart updated successfully!");

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleRemoveItem = (id: string) => {
    removeFromCart(id);

    // Show success message
    setSuccessMessage("Item removed from cart!");

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleApplyPromo = () => {
    if (promoCode.toLowerCase() === 'welcome10') {
      setPromoApplied(true);
      setDiscount(0.1); // 10% discount

      // Show success message
      setSuccessMessage("Promo code applied successfully!");

      // Clear the success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } else {
      alert('Invalid promo code');
    }
  };

  const handleCheckout = () => {
    alert('Proceeding to checkout...');
    // This would redirect to a checkout page in a real implementation
  };

  // Calculate totals
  const subtotal = getCartTotal();
  const discountAmount = promoApplied ? subtotal * discount : 0;
  const shipping = subtotal > 0 ? 350 : 0; // Free shipping over a certain amount could be implemented
  const total = subtotal - discountAmount + shipping;

  return (
    <>
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-primary mb-8">Your Shopping Cart</h1>

          {/* Success Message */}
          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 flex items-center shadow-md animate-fadeIn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {successMessage}
            </div>
          )}

          {cartItems.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-secondary mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <h2 className="text-2xl font-semibold text-primary mb-4">Your cart is empty</h2>
              <p className="text-secondary mb-6">Looks like you haven't added any items to your cart yet.</p>
              <Link
                href="/categories"
                className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition-colors"
              >
                Continue Shopping
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6 border-b">
                    <h2 className="text-xl font-semibold text-primary">Cart Items ({cartItems.length})</h2>
                  </div>

                  <ul>
                    {cartItems.map(item => (
                      <li key={item.id} className="p-6 border-b flex flex-col sm:flex-row items-center sm:items-start gap-6">
                        <div className="relative w-24 h-24 flex-shrink-0">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-contain"
                          />
                        </div>

                        <div className="flex-grow text-center sm:text-left">
                          <Link href={`/products/${item.id}`}>
                            <h3 className="text-lg font-semibold text-primary hover:text-accent transition-colors">
                              {item.name}
                            </h3>
                          </Link>
                          <p className="text-accent font-bold mt-1">KSh {item.price.toFixed(2)}</p>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                            className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded"
                          >
                            -
                          </button>
                          <span className="w-10 text-center">{item.quantity}</span>
                          <button
                            onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                            className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded"
                          >
                            +
                          </button>
                        </div>

                        <div className="text-right">
                          <p className="font-bold text-primary">KSh {(item.price * item.quantity).toFixed(2)}</p>
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-red-500 hover:text-red-700 text-sm mt-2"
                          >
                            Remove
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>

                  <div className="p-6 flex justify-between">
                    <Link
                      href="/categories"
                      className="text-primary hover:text-accent transition-colors flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                      </svg>
                      Continue Shopping
                    </Link>

                    <button
                      onClick={() => {
                        clearCart();
                        setSuccessMessage("Cart cleared successfully!");
                        setTimeout(() => {
                          setSuccessMessage(null);
                        }, 3000);
                      }}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      Clear Cart
                    </button>
                  </div>
                </div>
              </div>

              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-semibold text-primary mb-6">Order Summary</h2>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between">
                      <span className="text-secondary">Subtotal</span>
                      <span className="font-semibold">KSh {subtotal.toFixed(2)}</span>
                    </div>

                    {promoApplied && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount (10%)</span>
                        <span>-KSh {discountAmount.toFixed(2)}</span>
                      </div>
                    )}

                    <div className="flex justify-between">
                      <span className="text-secondary">Shipping</span>
                      <span className="font-semibold">KSh {shipping.toFixed(2)}</span>
                    </div>
                    <div className="border-t pt-4 flex justify-between">
                      <span className="font-semibold">Total</span>
                      <span className="font-bold text-xl text-primary">KSh {total.toFixed(2)}</span>
                    </div>
                  </div>

                  {/* Promo Code */}
                  <div className="mb-6">
                    <label htmlFor="promo" className="block text-sm font-medium text-secondary mb-2">Promo Code</label>
                    <div className="flex">
                      <input
                        type="text"
                        id="promo"
                        placeholder="Enter promo code"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        disabled={promoApplied}
                        className="flex-grow border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                      />
                      <button
                        onClick={handleApplyPromo}
                        disabled={promoApplied || !promoCode}
                        className={`px-4 py-2 rounded-r-lg font-medium ${
                          promoApplied
                            ? 'bg-green-500 text-white cursor-not-allowed'
                            : !promoCode
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-primary text-white hover:bg-secondary transition-colors'
                        }`}
                      >
                        {promoApplied ? 'Applied' : 'Apply'}
                      </button>
                    </div>
                    {promoApplied && (
                      <p className="text-green-600 text-sm mt-1">Promo code applied successfully!</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Try "WELCOME10" for 10% off your order</p>
                  </div>

                  <button
                    onClick={handleCheckout}
                    className="w-full bg-accent text-white py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors shadow-md"
                  >
                    Proceed to Checkout
                  </button>
                  <p className="text-xs text-gray-500 mt-2 text-center">Secure checkout process</p>

                  <div className="mt-6 text-center text-sm text-secondary">
                    <p>We accept:</p>
                    <div className="flex justify-center space-x-2 mt-2">
                      <span className="bg-gray-100 px-2 py-1 rounded">M-Pesa</span>
                      <span className="bg-gray-100 px-2 py-1 rounded">Visa</span>
                      <span className="bg-gray-100 px-2 py-1 rounded">MasterCard</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </>
  );
}
