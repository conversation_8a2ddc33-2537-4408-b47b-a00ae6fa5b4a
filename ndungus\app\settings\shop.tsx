import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getObject, setObject, StorageKey } from '@/services/storage';
import { FOLDERS } from '@/services/cloudinary';
import { pickAndUploadImage } from '@/services/imageUpload';

// Shop settings interface
interface ShopSettings {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  currency: string;
  taxRate: string;
  receiptFooter: string;
  receiptHeader: string;
}

// Default settings
const DEFAULT_SETTINGS: ShopSettings = {
  name: 'Ndungus Shop',
  address: '',
  phone: '',
  email: '',
  website: '',
  logo: '',
  currency: 'KES',
  taxRate: '16',
  receiptFooter: 'Thank you for your business!',
  receiptHeader: 'Receipt',
};

// Currency options
const CURRENCY_OPTIONS = [
  { label: 'KES (Kenyan Shilling)', value: 'KES' },
  { label: 'USD (US Dollar)', value: 'USD' },
  { label: 'EUR (Euro)', value: 'EUR' },
  { label: 'GBP (British Pound)', value: 'GBP' },
];

export default function ShopSettingsScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  // Form state
  const [settings, setSettings] = useState<ShopSettings>(DEFAULT_SETTINGS);
  
  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await getObject<ShopSettings>(StorageKey.SHOP_SETTINGS);
        
        if (savedSettings) {
          setSettings(savedSettings);
        }
      } catch (error) {
        console.error('Error loading shop settings:', error);
      }
    };
    
    loadSettings();
  }, []);
  
  // Handle form changes
  const handleChange = (key: keyof ShopSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };
  
  // Handle logo upload
  const handleLogoUpload = async () => {
    try {
      setIsUploading(true);
      
      // Pick and upload image
      const result = await pickAndUploadImage('library', FOLDERS.PRODUCTS);
      
      if (result) {
        // Update logo URL
        handleChange('logo', result.secure_url);
        Alert.alert('Success', 'Logo uploaded successfully');
      }
    } catch (error: any) {
      Alert.alert('Upload Error', error.message || 'Failed to upload logo');
    } finally {
      setIsUploading(false);
    }
  };
  
  // Save settings
  const handleSave = async () => {
    setIsLoading(true);
    
    try {
      // Validate tax rate
      const taxRate = parseFloat(settings.taxRate);
      if (isNaN(taxRate) || taxRate < 0 || taxRate > 100) {
        Alert.alert('Error', 'Tax rate must be a number between 0 and 100');
        setIsLoading(false);
        return;
      }
      
      // Save settings
      await setObject(StorageKey.SHOP_SETTINGS, settings);
      
      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <GlassCard style={styles.header}>
          <View style={styles.headerContent}>
            <Pressable onPress={() => router.back()} style={styles.backButton}>
              <MaterialIcons
                name="arrow-back"
                size={24}
                color={colorScheme === 'dark' ? Colors.white : Colors.black}
              />
            </Pressable>
            <Text
              style={[
                styles.title,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Shop Settings
            </Text>
          </View>
        </GlassCard>
        
        {/* Shop Information */}
        <GlassCard style={styles.formCard}>
          <Text
            style={[
              styles.sectionTitle,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Shop Information
          </Text>
          
          {/* Logo */}
          <View style={styles.logoContainer}>
            {settings.logo ? (
              <Image
                source={{ uri: settings.logo }}
                style={styles.logo}
                contentFit="cover"
              />
            ) : (
              <View
                style={[
                  styles.logoPlaceholder,
                  {
                    backgroundColor: colorScheme === 'dark'
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}>
                <MaterialIcons
                  name="store"
                  size={48}
                  color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray500}
                />
              </View>
            )}
            
            <Button
              title="Upload Logo"
              variant="secondary"
              size="sm"
              icon={<MaterialIcons name="upload" size={16} color={colorScheme === 'dark' ? Colors.white : Colors.black} />}
              isLoading={isUploading}
              onPress={handleLogoUpload}
              style={styles.uploadButton}
            />
          </View>
          
          {/* Shop Name */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Shop Name*
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter shop name"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.name}
              onChangeText={(value) => handleChange('name', value)}
            />
          </View>
          
          {/* Address */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Address
            </Text>
            <TextInput
              style={[
                styles.textArea,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter shop address"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
              value={settings.address}
              onChangeText={(value) => handleChange('address', value)}
            />
          </View>
          
          {/* Contact Information */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Phone
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter phone number"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              keyboardType="phone-pad"
              value={settings.phone}
              onChangeText={(value) => handleChange('phone', value)}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Email
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter email address"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              keyboardType="email-address"
              autoCapitalize="none"
              value={settings.email}
              onChangeText={(value) => handleChange('email', value)}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Website
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter website URL"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              keyboardType="url"
              autoCapitalize="none"
              value={settings.website}
              onChangeText={(value) => handleChange('website', value)}
            />
          </View>
        </GlassCard>
        
        {/* Financial Settings */}
        <GlassCard style={styles.formCard}>
          <Text
            style={[
              styles.sectionTitle,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Financial Settings
          </Text>
          
          {/* Currency */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Currency
            </Text>
            <View style={styles.currencyOptions}>
              {CURRENCY_OPTIONS.map((option) => (
                <Pressable
                  key={option.value}
                  style={[
                    styles.currencyOption,
                    {
                      backgroundColor: settings.currency === option.value
                        ? Colors.primary
                        : colorScheme === 'dark'
                          ? 'rgba(255, 255, 255, 0.1)'
                          : 'rgba(0, 0, 0, 0.05)',
                    },
                  ]}
                  onPress={() => handleChange('currency', option.value)}>
                  <Text
                    style={[
                      styles.currencyText,
                      {
                        color: settings.currency === option.value
                          ? Colors.black
                          : colorScheme === 'dark'
                            ? Colors.white
                            : Colors.black,
                      },
                    ]}>
                    {option.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          </View>
          
          {/* Tax Rate */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Tax Rate (%)
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter tax rate (e.g., 16)"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              keyboardType="numeric"
              value={settings.taxRate}
              onChangeText={(value) => handleChange('taxRate', value)}
            />
          </View>
        </GlassCard>
        
        {/* Receipt Settings */}
        <GlassCard style={styles.formCard}>
          <Text
            style={[
              styles.sectionTitle,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Receipt Settings
          </Text>
          
          {/* Receipt Header */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Receipt Header
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter receipt header"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={settings.receiptHeader}
              onChangeText={(value) => handleChange('receiptHeader', value)}
            />
          </View>
          
          {/* Receipt Footer */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Receipt Footer
            </Text>
            <TextInput
              style={[
                styles.textArea,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter receipt footer"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
              value={settings.receiptFooter}
              onChangeText={(value) => handleChange('receiptFooter', value)}
            />
          </View>
        </GlassCard>
        
        {/* Save Button */}
        <Button
          title="Save Settings"
          variant="primary"
          isLoading={isLoading}
          onPress={handleSave}
          style={styles.saveButton}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  formCard: {
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.sm,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  uploadButton: {
    marginTop: Spacing.xs,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  input: {
    height: 48,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  textArea: {
    minHeight: 80,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  currencyOptions: {
    flexDirection: 'column',
    gap: Spacing.xs,
  },
  currencyOption: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  currencyText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  saveButton: {
    marginTop: Spacing.sm,
  },
});
