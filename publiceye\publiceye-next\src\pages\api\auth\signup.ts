import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
getServerFirebase();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, password, displayName } = req.body;

    if (!email || !password || !displayName) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create user with Firebase Auth
    const userRecord = await admin.auth().createUser({
      email,
      password,
      displayName,
    });

    // Create user profile in Realtime Database
    const db = getAdminDatabase();
    const userRef = db.ref(`users/${userRecord.uid}`);
    
    await userRef.set({
      email,
      displayName,
      createdAt: new Date().toISOString(),
      photoURL: userRecord.photoURL || '',
      role: 'user',
      followedCases: {},
      contributedCases: {},
    });

    // Return success with user data (excluding sensitive info)
    return res.status(201).json({
      success: true,
      user: {
        id: userRecord.uid,
        email: userRecord.email,
        displayName: userRecord.displayName,
        photoURL: userRecord.photoURL,
      },
    });
  } catch (error: any) {
    console.error('Error creating user:', error);
    
    // Handle Firebase Auth specific errors
    if (error.code === 'auth/email-already-exists') {
      return res.status(400).json({ error: 'Email already in use' });
    } else if (error.code === 'auth/invalid-email') {
      return res.status(400).json({ error: 'Invalid email format' });
    } else if (error.code === 'auth/weak-password') {
      return res.status(400).json({ error: 'Password is too weak' });
    }
    
    return res.status(500).json({ error: 'Failed to create user' });
  }
}
