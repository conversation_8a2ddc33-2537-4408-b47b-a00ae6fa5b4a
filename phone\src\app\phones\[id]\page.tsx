'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import Navbar from '@/components/Navbar';
import { phones } from '@/data/phones';
import { Phone } from '@/types';

export default function PhonePage({ params }: { params: { id: string } }) {
  const [phone, setPhone] = useState<Phone | null>(null);

  useEffect(() => {
    const foundPhone = phones.find(p => p.id === params.id);
    setPhone(foundPhone || null);
  }, [params.id]);

  const handleWhatsAppClick = () => {
    if (!phone) return;
    const message = `I am interested in ${phone.name}. Is it still available?`;
    const whatsappUrl = `https://wa.me/+254XXXXXXXXX?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!phone) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <p className="text-center text-gray-600">Phone not found</p>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8">
            <div className="relative h-[400px]">
              <Image
                src={phone.image}
                alt={phone.name}
                fill
                className="object-cover rounded-lg"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{phone.name}</h1>
              <p className="text-lg text-gray-600 mb-4">{phone.storage}</p>
              <p className="text-2xl font-bold text-gray-900 mb-6">
                KES {phone.price.toLocaleString()}
              </p>
              <p className="text-gray-600 mb-8">{phone.description}</p>
              <button
                onClick={handleWhatsAppClick}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors text-lg font-semibold"
              >
                Buy Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
} 