import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { PaymentMethod } from '@/models/Sale';
import { Product } from '@/models/Product';
import { getProducts } from '@/services/products';
import { createSale } from '@/services/sales';

// Cart item interface
interface CartItem {
  product: Product;
  quantity: number;
}

export default function NewSaleScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [cart, setCart] = useState<CartItem[]>([]);
  
  // Customer info
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  
  // Discount and tax
  const [discount, setDiscount] = useState('');
  const [tax, setTax] = useState('');
  
  // Load products
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const productData = await getProducts();
        setProducts(productData);
        setFilteredProducts(productData);
      } catch (error) {
        console.error('Error loading products:', error);
        Alert.alert('Error', 'Failed to load products');
      }
    };
    
    loadProducts();
  }, []);
  
  // Filter products based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredProducts(products);
      return;
    }
    
    const filtered = products.filter(product => 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setFilteredProducts(filtered);
  }, [searchQuery, products]);
  
  // Add product to cart
  const addToCart = (product: Product) => {
    // Check if product is already in cart
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      // Increment quantity if already in cart
      const updatedCart = cart.map(item => 
        item.product.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      );
      setCart(updatedCart);
    } else {
      // Add new item to cart
      setCart([...cart, { product, quantity: 1 }]);
    }
  };
  
  // Remove product from cart
  const removeFromCart = (productId: string) => {
    const updatedCart = cart.filter(item => item.product.id !== productId);
    setCart(updatedCart);
  };
  
  // Update product quantity in cart
  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }
    
    const product = cart.find(item => item.product.id === productId)?.product;
    
    if (product && quantity > product.units) {
      Alert.alert('Error', `Only ${product.units} units available in stock`);
      return;
    }
    
    const updatedCart = cart.map(item => 
      item.product.id === productId
        ? { ...item, quantity }
        : item
    );
    
    setCart(updatedCart);
  };
  
  // Calculate subtotal
  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + (item.product.sellingPrice * item.quantity), 0);
  };
  
  // Calculate total
  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discountAmount = parseFloat(discount) || 0;
    const taxAmount = parseFloat(tax) || 0;
    
    return subtotal - discountAmount + taxAmount;
  };
  
  // Handle checkout
  const handleCheckout = async () => {
    if (cart.length === 0) {
      Alert.alert('Error', 'Cart is empty');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Create sale items
      const saleItems = cart.map(item => ({
        productId: item.product.id,
        quantity: item.quantity,
      }));
      
      // Create sale
      await createSale({
        items: saleItems,
        discount: parseFloat(discount) || 0,
        tax: parseFloat(tax) || 0,
        paymentMethod,
        customerName: customerName || undefined,
        customerPhone: customerPhone || undefined,
        notes: notes || undefined,
      });
      
      // Show success message
      Alert.alert(
        'Success',
        'Sale recorded successfully',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to record sale');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}>
      <View style={styles.container}>
        {/* Header */}
        <GlassCard style={styles.header}>
          <View style={styles.headerContent}>
            <Pressable onPress={() => router.back()} style={styles.backButton}>
              <MaterialIcons
                name="arrow-back"
                size={24}
                color={colorScheme === 'dark' ? Colors.white : Colors.black}
              />
            </Pressable>
            <Text
              style={[
                styles.title,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              New Sale
            </Text>
          </View>
        </GlassCard>
        
        <View style={styles.content}>
          {/* Products Section */}
          <View style={styles.productsSection}>
            {/* Search Bar */}
            <View
              style={[
                styles.searchContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="search"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <TextInput
                style={[
                  styles.searchInput,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Search products..."
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <Pressable onPress={() => setSearchQuery('')}>
                  <MaterialIcons
                    name="close"
                    size={20}
                    color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                  />
                </Pressable>
              )}
            </View>
            
            {/* Products List */}
            <FlatList
              data={filteredProducts}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <Pressable
                  style={[
                    styles.productItem,
                    {
                      backgroundColor: colorScheme === 'dark'
                        ? 'rgba(255, 255, 255, 0.1)'
                        : 'rgba(0, 0, 0, 0.05)',
                    },
                  ]}
                  onPress={() => addToCart(item)}
                  disabled={item.units <= 0}>
                  <View style={styles.productInfo}>
                    <Text
                      style={[
                        styles.productName,
                        { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                        item.units <= 0 ? styles.outOfStock : null,
                      ]}
                      numberOfLines={1}>
                      {item.name}
                    </Text>
                    <Text
                      style={[
                        styles.productDetails,
                        { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                      ]}>
                      {item.code} • {item.units} in stock
                    </Text>
                  </View>
                  <View style={styles.productPrice}>
                    <Text
                      style={[
                        styles.priceText,
                        { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                        item.units <= 0 ? styles.outOfStock : null,
                      ]}>
                      {formatCurrency(item.sellingPrice)}
                    </Text>
                    {item.units > 0 ? (
                      <MaterialIcons
                        name="add-shopping-cart"
                        size={20}
                        color={colorScheme === 'dark' ? Colors.primary : Colors.primary}
                      />
                    ) : (
                      <Text
                        style={[
                          styles.outOfStockText,
                          { color: Colors.error },
                        ]}>
                        Out of stock
                      </Text>
                    )}
                  </View>
                </Pressable>
              )}
              contentContainerStyle={styles.productsList}
            />
          </View>
          
          {/* Cart Section */}
          <GlassCard style={styles.cartSection}>
            <Text
              style={[
                styles.sectionTitle,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Cart
            </Text>
            
            {/* Cart Items */}
            <ScrollView style={styles.cartItems}>
              {cart.length === 0 ? (
                <Text
                  style={[
                    styles.emptyCartText,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  No items in cart
                </Text>
              ) : (
                cart.map((item) => (
                  <View key={item.product.id} style={styles.cartItem}>
                    <View style={styles.cartItemInfo}>
                      <Text
                        style={[
                          styles.cartItemName,
                          { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                        ]}
                        numberOfLines={1}>
                        {item.product.name}
                      </Text>
                      <Text
                        style={[
                          styles.cartItemPrice,
                          { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                        ]}>
                        {formatCurrency(item.product.sellingPrice)}
                      </Text>
                    </View>
                    
                    <View style={styles.cartItemActions}>
                      <Pressable
                        style={[
                          styles.quantityButton,
                          {
                            backgroundColor: colorScheme === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.05)',
                          },
                        ]}
                        onPress={() => updateQuantity(item.product.id, item.quantity - 1)}>
                        <MaterialIcons
                          name="remove"
                          size={16}
                          color={colorScheme === 'dark' ? Colors.white : Colors.black}
                        />
                      </Pressable>
                      
                      <TextInput
                        style={[
                          styles.quantityInput,
                          { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                        ]}
                        value={item.quantity.toString()}
                        onChangeText={(text) => {
                          const quantity = parseInt(text);
                          if (!isNaN(quantity)) {
                            updateQuantity(item.product.id, quantity);
                          }
                        }}
                        keyboardType="numeric"
                      />
                      
                      <Pressable
                        style={[
                          styles.quantityButton,
                          {
                            backgroundColor: colorScheme === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.05)',
                          },
                        ]}
                        onPress={() => updateQuantity(item.product.id, item.quantity + 1)}>
                        <MaterialIcons
                          name="add"
                          size={16}
                          color={colorScheme === 'dark' ? Colors.white : Colors.black}
                        />
                      </Pressable>
                      
                      <Pressable
                        style={[
                          styles.removeButton,
                          {
                            backgroundColor: colorScheme === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.05)',
                          },
                        ]}
                        onPress={() => removeFromCart(item.product.id)}>
                        <MaterialIcons
                          name="delete"
                          size={16}
                          color={Colors.error}
                        />
                      </Pressable>
                    </View>
                  </View>
                ))
              )}
            </ScrollView>
            
            {/* Cart Summary */}
            <View style={styles.cartSummary}>
              <View style={styles.summaryRow}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  Subtotal:
                </Text>
                <Text
                  style={[
                    styles.summaryValue,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  {formatCurrency(calculateSubtotal())}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  Discount:
                </Text>
                <TextInput
                  style={[
                    styles.summaryInput,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}
                  placeholder="0.00"
                  placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                  keyboardType="numeric"
                  value={discount}
                  onChangeText={setDiscount}
                />
              </View>
              
              <View style={styles.summaryRow}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  Tax:
                </Text>
                <TextInput
                  style={[
                    styles.summaryInput,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}
                  placeholder="0.00"
                  placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                  keyboardType="numeric"
                  value={tax}
                  onChangeText={setTax}
                />
              </View>
              
              <View style={styles.divider} />
              
              <View style={styles.summaryRow}>
                <Text
                  style={[
                    styles.totalLabel,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  Total:
                </Text>
                <Text
                  style={[
                    styles.totalValue,
                    { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                  ]}>
                  {formatCurrency(calculateTotal())}
                </Text>
              </View>
            </View>
            
            {/* Payment Method */}
            <View style={styles.paymentMethodContainer}>
              <Text
                style={[
                  styles.paymentMethodLabel,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Payment Method:
              </Text>
              <View style={styles.paymentMethods}>
                {(['cash', 'mpesa', 'card', 'bank'] as PaymentMethod[]).map((method) => (
                  <Pressable
                    key={method}
                    style={[
                      styles.paymentMethodButton,
                      {
                        backgroundColor: paymentMethod === method
                          ? Colors.primary
                          : colorScheme === 'dark'
                            ? 'rgba(255, 255, 255, 0.1)'
                            : 'rgba(0, 0, 0, 0.05)',
                      },
                    ]}
                    onPress={() => setPaymentMethod(method)}>
                    <Text
                      style={[
                        styles.paymentMethodText,
                        {
                          color: paymentMethod === method
                            ? Colors.black
                            : colorScheme === 'dark'
                              ? Colors.white
                              : Colors.black,
                        },
                      ]}>
                      {method === 'cash' ? 'Cash' :
                        method === 'mpesa' ? 'M-Pesa' :
                        method === 'card' ? 'Card' : 'Bank'}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>
            
            {/* Customer Info */}
            <View style={styles.customerInfo}>
              <TextInput
                style={[
                  styles.customerInput,
                  { 
                    color: colorScheme === 'dark' ? Colors.white : Colors.black,
                    backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}
                placeholder="Customer Name (Optional)"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                value={customerName}
                onChangeText={setCustomerName}
              />
              
              <TextInput
                style={[
                  styles.customerInput,
                  { 
                    color: colorScheme === 'dark' ? Colors.white : Colors.black,
                    backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}
                placeholder="Customer Phone (Optional)"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                keyboardType="phone-pad"
                value={customerPhone}
                onChangeText={setCustomerPhone}
              />
              
              <TextInput
                style={[
                  styles.notesInput,
                  { 
                    color: colorScheme === 'dark' ? Colors.white : Colors.black,
                    backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                  },
                ]}
                placeholder="Notes (Optional)"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                multiline
                numberOfLines={2}
                textAlignVertical="top"
                value={notes}
                onChangeText={setNotes}
              />
            </View>
            
            {/* Checkout Button */}
            <Button
              title="Complete Sale"
              variant="primary"
              icon={<MaterialIcons name="check" size={20} color={Colors.black} />}
              isLoading={isLoading}
              onPress={handleCheckout}
              style={styles.checkoutButton}
            />
          </GlassCard>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: Spacing.md,
    marginBottom: Spacing.sm,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    padding: Spacing.sm,
  },
  productsSection: {
    flex: 3,
    marginRight: Spacing.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  productsList: {
    paddingBottom: Spacing.md,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.xs,
  },
  productInfo: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  productName: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 2,
  },
  productDetails: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  productPrice: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.bold,
    marginBottom: 2,
  },
  outOfStock: {
    opacity: 0.5,
  },
  outOfStockText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  cartSection: {
    flex: 2,
    padding: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
    marginBottom: Spacing.sm,
  },
  cartItems: {
    maxHeight: 200,
    marginBottom: Spacing.md,
  },
  emptyCartText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginVertical: Spacing.md,
  },
  cartItem: {
    marginBottom: Spacing.sm,
  },
  cartItemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  cartItemName: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    flex: 1,
    marginRight: Spacing.sm,
  },
  cartItemPrice: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
  },
  cartItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: BorderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityInput: {
    width: 40,
    textAlign: 'center',
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  removeButton: {
    width: 28,
    height: 28,
    borderRadius: BorderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  cartSummary: {
    marginBottom: Spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  summaryValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  summaryInput: {
    width: 80,
    height: 32,
    borderRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    textAlign: 'right',
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150, 150, 150, 0.2)',
    marginVertical: Spacing.sm,
  },
  totalLabel: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  totalValue: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  paymentMethodContainer: {
    marginBottom: Spacing.md,
  },
  paymentMethodLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  paymentMethods: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
  },
  paymentMethodButton: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  paymentMethodText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  customerInfo: {
    marginBottom: Spacing.md,
  },
  customerInput: {
    height: 40,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: Spacing.xs,
  },
  notesInput: {
    minHeight: 60,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  checkoutButton: {
    marginTop: Spacing.sm,
  },
});
