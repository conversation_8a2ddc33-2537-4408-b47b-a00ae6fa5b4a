import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';

// Initialize Firebase Admin on the server side
getServerFirebase();
const db = getAdminDatabase();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid case ID' });
  }

  // Handle GET request to retrieve a case
  if (req.method === 'GET') {
    try {
      const caseRef = db.ref(`cases/${id}`);
      const snapshot = await caseRef.once('value');

      if (!snapshot.exists()) {
        return res.status(404).json({ error: 'Case not found' });
      }

      const caseData = snapshot.val();
      return res.status(200).json({
        ...caseData,
        id
      });
    } catch (error) {
      console.error('Error retrieving case:', error);
      return res.status(500).json({ error: 'Failed to retrieve case' });
    }
  }

  // Method not allowed
  return res.status(405).json({ error: 'Method not allowed' });
}
