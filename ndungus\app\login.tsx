import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, Pressable, ScrollView, StyleSheet, Text, TextInput, View } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from './_layout';

export default function LoginScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Get auth context
  const { signIn, isLoading, error: authError } = useAuth();

  // Clear field errors when inputs change
  useEffect(() => {
    if (email) setEmailError('');
  }, [email]);

  useEffect(() => {
    if (password) setPasswordError('');
  }, [password]);

  // Handle login
  const handleLogin = async () => {
    // Validate form
    const validation = validateLoginForm(email, password);

    if (!validation.isValid) {
      setEmailError(validation.emailError || '');
      setPasswordError(validation.passwordError || '');
      return;
    }

    // Call sign in from auth context
    await signIn(email, password, rememberMe);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}>
        {/* Logo and Header */}
        <View style={styles.header}>
          <Image
            source={require('../assets/images/icon.png')}
            style={styles.logo}
            contentFit="contain"
          />
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Ndungus Shop
          </Text>
          <Text
            style={[
              styles.subtitle,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Login to your account
          </Text>
        </View>

        {/* Login Form */}
        <GlassCard style={styles.formCard}>
          {authError ? (
            <View
              style={[
                styles.errorContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(239, 68, 68, 0.2)'
                    : 'rgba(239, 68, 68, 0.1)',
                },
              ]}>
              <MaterialIcons name="error" size={20} color={Colors.error} />
              <Text style={[styles.errorText, { color: Colors.error }]}>
                {authError}
              </Text>
            </View>
          ) : null}

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Email
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
                emailError ? styles.inputError : null,
              ]}>
              <MaterialIcons
                name="email"
                size={20}
                color={emailError ? Colors.error : (colorScheme === 'dark' ? Colors.gray400 : Colors.gray600)}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Enter your email"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                keyboardType="email-address"
                autoCapitalize="none"
                value={email}
                onChangeText={setEmail}
              />
            </View>
            {emailError ? (
              <Text style={styles.fieldErrorText}>{emailError}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Password
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
                passwordError ? styles.inputError : null,
              ]}>
              <MaterialIcons
                name="lock"
                size={20}
                color={passwordError ? Colors.error : (colorScheme === 'dark' ? Colors.gray400 : Colors.gray600)}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}
                placeholder="Enter your password"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={setPassword}
              />
              <Pressable
                onPress={() => setShowPassword(!showPassword)}
                style={styles.passwordToggle}>
                <MaterialIcons
                  name={showPassword ? 'visibility-off' : 'visibility'}
                  size={20}
                  color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                />
              </Pressable>
            </View>
            {passwordError ? (
              <Text style={styles.fieldErrorText}>{passwordError}</Text>
            ) : null}
          </View>

          <View style={styles.rememberForgotContainer}>
            <Pressable
              style={styles.rememberMeContainer}
              onPress={() => setRememberMe(!rememberMe)}
            >
              <View style={[
                styles.checkbox,
                rememberMe ? { backgroundColor: Colors.primary } : null
              ]}>
                {rememberMe && (
                  <MaterialIcons name="check" size={16} color={Colors.white} />
                )}
              </View>
              <Text
                style={[
                  styles.rememberMeText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Remember Me
              </Text>
            </Pressable>

            <Pressable style={styles.forgotPassword}>
              <Text
                style={[
                  styles.forgotPasswordText,
                  { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                ]}>
                Forgot Password?
              </Text>
            </Pressable>
          </View>

          <Button
            title="Login"
            variant="primary"
            onPress={handleLogin}
            loading={isLoading}
            style={styles.loginButton}
            fullWidth
          />

          <View style={styles.registerContainer}>
            <Text
              style={[
                styles.registerText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Don't have an account?
            </Text>
            <Link href="/register" asChild>
              <Pressable>
                <Text
                  style={[
                    styles.registerLink,
                    { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                  ]}>
                  Register
                </Text>
              </Pressable>
            </Link>
          </View>
        </GlassCard>

        {/* Demo Mode */}
        <View style={styles.demoContainer}>
          <Text
            style={[
              styles.demoText,
              { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
            ]}>
            Demo Mode: Click Login to continue
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: Spacing.md,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontFamily: Typography.fontFamily.bold,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.regular,
  },
  formCard: {
    padding: Spacing.lg,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
  },
  errorText: {
    marginLeft: Spacing.xs,
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  fieldErrorText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    borderWidth: 1,
    borderColor: 'transparent',
    height: 50,
  },
  inputError: {
    borderColor: Colors.error,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 48,
    fontFamily: Typography.fontFamily.regular,
    fontSize: Typography.fontSize.md,
  },
  passwordToggle: {
    padding: Spacing.xs,
  },
  rememberForgotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.xs,
    borderWidth: 1,
    borderColor: Colors.gray400,
    marginRight: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rememberMeText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
  },
  forgotPasswordText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  loginButton: {
    marginBottom: Spacing.md,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.xs,
  },
  registerText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  registerLink: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semiBold,
  },
  demoContainer: {
    marginTop: Spacing.xl,
    alignItems: 'center',
  },
  demoText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.italic,
  },
});
