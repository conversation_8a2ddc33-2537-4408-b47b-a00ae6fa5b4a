/**
 * Authentication service
 * Handles user authentication, registration, and session management
 */

import { api, ApiError, ErrorType, getAuthToken, setAuthToken, clearAuthToken } from './api';
import { getObject, setObject, StorageKey } from './storage';

// User interface
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'employee';
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

// Auth state interface
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  lastLogin?: string;
  rememberMe: boolean;
}

// Default auth state
const defaultAuthState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  rememberMe: false,
};

// Get current auth state
export const getAuthState = async (): Promise<AuthState> => {
  const state = await getObject<AuthState>(StorageKey.AUTH_STATE);
  return state || defaultAuthState;
};

// Set auth state
export const setAuthState = async (state: Partial<AuthState>): Promise<void> => {
  const currentState = await getAuthState();
  const newState = { ...currentState, ...state };
  await setObject(StorageKey.AUTH_STATE, newState);
};

// Login
export const login = async (
  email: string,
  password: string,
  rememberMe: boolean = false
): Promise<User> => {
  try {
    // Call API to login
    const response = await api.auth.login(email, password);
    
    // Get user profile
    const user = await getUserProfile();
    
    // Update auth state
    await setAuthState({
      isAuthenticated: true,
      user,
      token: response.token,
      lastLogin: new Date().toISOString(),
      rememberMe,
    });
    
    return user;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Login failed', ErrorType.UNKNOWN);
  }
};

// Register
export const register = async (
  name: string,
  email: string,
  password: string
): Promise<User> => {
  try {
    // Call API to register
    const response = await api.auth.register({ name, email, password });
    
    // Set auth token
    await setAuthToken(response.token);
    
    // Get user profile
    const user = await getUserProfile();
    
    // Update auth state
    await setAuthState({
      isAuthenticated: true,
      user,
      token: response.token,
      lastLogin: new Date().toISOString(),
      rememberMe: false,
    });
    
    return user;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Registration failed', ErrorType.UNKNOWN);
  }
};

// Logout
export const logout = async (): Promise<void> => {
  try {
    // Call API to logout
    await api.auth.logout();
  } catch (error) {
    console.error('Error during logout:', error);
  } finally {
    // Clear auth token and state
    await clearAuthToken();
    await setAuthState(defaultAuthState);
  }
};

// Get user profile
export const getUserProfile = async (): Promise<User> => {
  try {
    // For demo purposes, return a mock user
    // In a real app, this would call the API
    return {
      id: '1',
      name: 'Demo User',
      email: '<EMAIL>',
      role: 'admin',
      avatar: 'https://ui-avatars.com/api/?name=Demo+User&background=FFD700&color=fff',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // In a real app:
    // return await api.users.getProfile();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Failed to get user profile', ErrorType.UNKNOWN);
  }
};

// Check if user is authenticated
export const checkAuth = async (): Promise<boolean> => {
  try {
    const token = await getAuthToken();
    if (!token) {
      return false;
    }
    
    const state = await getAuthState();
    if (!state.isAuthenticated) {
      return false;
    }
    
    // In a real app, verify the token with the server
    // For demo purposes, just check if token exists
    return true;
  } catch (error) {
    console.error('Error checking auth:', error);
    return false;
  }
};
