import { NextApiRequest, NextApiResponse } from 'next';
import { getDatabase } from 'firebase-admin/database';
import { CaseStage } from '@/types/case';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { id } = req.query;
  const { stage, description, mediaUrls, sourceUrl, userId } = req.body;

  if (!id || !stage || !description || !userId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const db = getDatabase();
    const caseRef = db.ref(`cases/${id}`);
    
    // Create new contribution with unique ID
    const contributionId = Date.now().toString();
    const newContribution = {
      id: contributionId,
      stage,
      description,
      mediaUrls: mediaUrls || [],
      sourceUrl: sourceUrl || null,
      userId,
      createdAt: new Date().toISOString(),
      isVerified: false,
      isFlagged: false,
    };

    // Add contribution to case
    const contributionsRef = db.ref(`cases/${id}/contributions/${contributionId}`);
    await contributionsRef.set(newContribution);

    // Update the case's current stage if the contribution's stage is newer
    const caseSnapshot = await caseRef.get();
    const caseData = caseSnapshot.val();
    
    if (caseData) {
      const currentStageIndex = Object.values(CaseStage).indexOf(caseData.currentStage);
      const newStageIndex = Object.values(CaseStage).indexOf(stage);
      
      if (newStageIndex > currentStageIndex) {
        await caseRef.update({
          currentStage: stage,
          updatedAt: Date.now()
        });
      } else {
        // Just update the timestamp if stage hasn't changed
        await caseRef.update({
          updatedAt: Date.now()
        });
      }
    }

    return res.status(200).json({
      success: true,
      contributionId,
      contribution: newContribution
    });
  } catch (error) {
    console.error('Error adding contribution:', error);
    return res.status(500).json({ error: 'Failed to add contribution' });
  }
} 