<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Cases - Public Eye</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Additional styles specific to the your cases page */
        .page-header {
            background-color: var(--primary-color);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }

        .tab {
            padding: 1rem 2rem;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .case-card {
            position: relative;
        }

        .case-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            z-index: 10;
        }

        .case-action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: white;
            color: var(--dark-text);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .case-action-btn:hover {
            background-color: var(--light-bg);
        }

        .case-action-btn.edit:hover {
            color: var(--primary-color);
        }

        .case-action-btn.delete:hover {
            color: var(--danger-color);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .empty-state i {
            font-size: 3rem;
            color: var(--medium-text);
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
        }

        .empty-state p {
            color: var(--medium-text);
            margin-bottom: 2rem;
        }

        .create-case-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            text-decoration: none;
        }

        .create-case-btn:hover {
            background-color: #1d4ed8;
            color: white;
        }

        /* Modal styles for delete confirmation */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            padding: 2rem;
            text-align: center;
        }

        .modal-content h3 {
            margin-bottom: 1rem;
        }

        .modal-content p {
            margin-bottom: 2rem;
            color: var(--medium-text);
        }

        .modal-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .modal-btn {
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background-color: white;
            color: var(--dark-text);
            border: 1px solid #d1d5db;
        }

        .cancel-btn:hover {
            background-color: #f3f4f6;
        }

        .delete-btn {
            background-color: var(--danger-color);
            color: white;
            border: none;
        }

        .delete-btn:hover {
            background-color: #dc2626;
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
                border-bottom: none;
            }

            .tab {
                border-bottom: 1px solid #e5e7eb;
            }

            .tab.active {
                border-bottom: 1px solid var(--primary-color);
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Public Eye</h1>
                <p class="tagline">Justice Remembered, Cases Tracked</p>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="create-case.html">Create Case</a></li>
                    <li><a href="your-cases.html" class="active">Your Cases</a></li>
                    <li><a href="#" class="btn-primary">Sign In</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="page-header">
            <div class="container">
                <h2>Your Cases</h2>
                <p>Manage the cases you've created and contributed to.</p>
            </div>
        </section>

        <section>
            <div class="container">
                <div class="tabs">
                    <div class="tab active" data-tab="created">Cases You Created</div>
                    <div class="tab" data-tab="contributed">Cases You Contributed To</div>
                    <div class="tab" data-tab="following">Cases You're Following</div>
                    <div class="tab" data-tab="drafts">Drafts</div>
                </div>

                <div class="tab-content active" id="created-tab">
                    <div class="cases-grid" id="created-cases">
                        <!-- Cases will be inserted here -->
                    </div>
                </div>

                <div class="tab-content" id="contributed-tab">
                    <div class="cases-grid" id="contributed-cases">
                        <!-- Cases will be inserted here -->
                    </div>
                </div>

                <div class="tab-content" id="following-tab">
                    <div class="cases-grid" id="following-cases">
                        <!-- Cases will be inserted here -->
                    </div>
                </div>

                <div class="tab-content" id="drafts-tab">
                    <div class="cases-grid" id="draft-cases">
                        <!-- Cases will be inserted here -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <h3>Delete Case</h3>
            <p>Are you sure you want to delete this case? This action cannot be undone.</p>
            <div class="modal-actions">
                <button class="modal-btn cancel-btn" id="cancel-delete">Cancel</button>
                <button class="modal-btn delete-btn" id="confirm-delete">Delete</button>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Public Eye</h3>
                    <p>Keeping cases visible and justice accountable.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="create-case.html">Create Case</a></li>
                        <li><a href="your-cases.html">Your Cases</a></li>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 Public Eye. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/cases.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching functionality
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(`${tabId}-tab`).classList.add('active');

                    // Load cases for the selected tab
                    loadCasesForTab(tabId);
                });
            });

            // Delete modal functionality
            const deleteModal = document.getElementById('delete-modal');
            const cancelDelete = document.getElementById('cancel-delete');
            const confirmDelete = document.getElementById('confirm-delete');
            let caseToDelete = null;

            // Close modal when cancel is clicked
            cancelDelete.addEventListener('click', function() {
                deleteModal.style.display = 'none';
                caseToDelete = null;
            });

            // Handle delete confirmation
            confirmDelete.addEventListener('click', function() {
                if (caseToDelete) {
                    // In a real application, this would send a delete request to the server
                    // For now, we'll just remove the element from the DOM
                    const caseElement = document.getElementById(`case-${caseToDelete}`);
                    if (caseElement) {
                        caseElement.remove();
                    }

                    // Check if there are any cases left in the current tab
                    const currentTab = document.querySelector('.tab.active').getAttribute('data-tab');
                    const casesContainer = document.getElementById(`${currentTab}-cases`);

                    if (casesContainer.children.length === 0) {
                        showEmptyState(casesContainer, currentTab);
                    }
                }

                deleteModal.style.display = 'none';
                caseToDelete = null;
            });

            // Load cases for the initial tab
            loadCasesForTab('created');

            // Function to load cases for a tab
            function loadCasesForTab(tabId) {
                const casesContainer = document.getElementById(`${tabId}-cases`);

                if (!casesContainer) return;

                // Clear current cases
                casesContainer.innerHTML = '';

                // In a real application, this would be an API call
                // For now, we'll use sample data
                let cases;

                switch(tabId) {
                    case 'created':
                        cases = getSampleCases('popular').slice(0, 2);
                        break;
                    case 'contributed':
                        cases = getSampleCases('recent').slice(0, 1);
                        break;
                    case 'following':
                        cases = getSampleCases('followed').slice(0, 3);
                        break;
                    case 'drafts':
                        cases = []; // No drafts for demo
                        break;
                    default:
                        cases = [];
                }

                if (cases.length === 0) {
                    showEmptyState(casesContainer, tabId);
                    return;
                }

                // Render each case
                cases.forEach(caseItem => {
                    const caseCard = createYourCaseCard(caseItem, tabId);
                    casesContainer.appendChild(caseCard);
                });
            }

            // Function to create a case card for the Your Cases page
            function createYourCaseCard(caseItem, tabType) {
                const card = document.createElement('div');
                card.className = 'case-card';
                card.id = `case-${caseItem.id}`;

                // Add action buttons for created cases and drafts
                if (tabType === 'created' || tabType === 'drafts') {
                    const actions = document.createElement('div');
                    actions.className = 'case-actions';

                    const editBtn = document.createElement('button');
                    editBtn.className = 'case-action-btn edit';
                    editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                    editBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        // In a real application, this would redirect to an edit page
                        alert(`Edit case: ${caseItem.title}`);
                    });

                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'case-action-btn delete';
                    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                    deleteBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        // Show delete confirmation modal
                        deleteModal.style.display = 'flex';
                        caseToDelete = caseItem.id;
                    });

                    actions.appendChild(editBtn);
                    actions.appendChild(deleteBtn);
                    card.appendChild(actions);
                }

                // Create status badge
                const statusBadge = document.createElement('div');
                statusBadge.className = 'case-status-badge';
                statusBadge.setAttribute('data-status', caseItem.status);
                statusBadge.textContent = getStatusText(caseItem.status);

                // Create title
                const title = document.createElement('h3');
                title.textContent = caseItem.title;

                // Create media preview
                const mediaPreview = document.createElement('div');
                mediaPreview.className = 'case-media-preview';

                const image = document.createElement('img');
                image.src = caseItem.image;
                image.alt = caseItem.title;
                mediaPreview.appendChild(image);

                // Create excerpt
                const excerpt = document.createElement('p');
                excerpt.className = 'case-excerpt';
                excerpt.textContent = caseItem.excerpt;

                // Create meta information
                const meta = document.createElement('div');
                meta.className = 'case-meta';

                const followers = document.createElement('span');
                followers.className = 'followers';
                followers.textContent = `${caseItem.followers} followers`;

                const updates = document.createElement('span');
                updates.className = 'updates';
                updates.textContent = `${caseItem.updates} updates`;

                meta.appendChild(followers);
                meta.appendChild(updates);

                // Create progress bar
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';

                const progressStep = document.createElement('div');
                progressStep.className = 'progress-step';
                progressStep.setAttribute('data-step', caseItem.status);
                progressStep.style.width = getProgressWidth(caseItem.status);

                progressBar.appendChild(progressStep);

                // Add click event to navigate to case details
                card.addEventListener('click', function() {
                    window.location.href = `case-details.html?id=${caseItem.id}`;
                });

                // Assemble the card
                card.appendChild(statusBadge);
                card.appendChild(mediaPreview);
                card.appendChild(title);
                card.appendChild(excerpt);
                card.appendChild(meta);
                card.appendChild(progressBar);

                return card;
            }

            // Function to show empty state
            function showEmptyState(container, tabType) {
                const emptyState = document.createElement('div');
                emptyState.className = 'empty-state';

                let message, icon, buttonText;

                switch(tabType) {
                    case 'created':
                        icon = 'fa-file-circle-plus';
                        message = "You haven't created any cases yet";
                        buttonText = "Create Your First Case";
                        break;
                    case 'contributed':
                        icon = 'fa-comments';
                        message = "You haven't contributed to any cases yet";
                        buttonText = "Find Cases to Contribute";
                        break;
                    case 'following':
                        icon = 'fa-eye';
                        message = "You aren't following any cases yet";
                        buttonText = "Discover Cases to Follow";
                        break;
                    case 'drafts':
                        icon = 'fa-file-lines';
                        message = "You don't have any drafts";
                        buttonText = "Start a New Case";
                        break;
                    default:
                        icon = 'fa-folder-open';
                        message = "No cases found";
                        buttonText = "Browse Cases";
                }

                emptyState.innerHTML = `
                    <i class="fas ${icon}"></i>
                    <h3>${message}</h3>
                    <p>Start tracking cases that matter to you and help bring attention to important issues.</p>
                    <a href="${tabType === 'created' || tabType === 'drafts' ? 'create-case.html' : 'index.html'}" class="create-case-btn">
                        <i class="fas fa-plus"></i>
                        <span>${buttonText}</span>
                    </a>
                `;

                container.appendChild(emptyState);
            }
        });
    </script>
</body>
</html>
