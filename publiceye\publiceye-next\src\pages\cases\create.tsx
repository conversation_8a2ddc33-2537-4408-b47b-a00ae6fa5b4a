import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { Fa<PERSON>lag, FaSearch, FaUserLock, FaGavel, FaBalanceScale, FaCheckCircle, FaUpload } from 'react-icons/fa';
import Layout from '@/components/layout/Layout';
import { CaseStage } from '@/types/case';
import { useCases } from '@/hooks/useCases';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/components/ui/Toast';

const CreateCase: NextPage = () => {
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [sourceUrl, setSourceUrl] = useState('');
  const [currentStage, setCurrentStage] = useState<CaseStage>(CaseStage.REPORTED);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [mediaPreviewUrls, setMediaPreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const router = useRouter();
  const { createCase } = useCases();
  const { user, loading } = useAuth();
  const { showToast } = useToast();

  // Check if user is authenticated
  useEffect(() => {
    if (!loading && !user) {
      // Redirect to sign in page with return URL
      router.push(`/auth/signin?returnUrl=${encodeURIComponent(router.asPath)}`);
    }
  }, [user, loading, router]);

  // Handle media file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setMediaFiles([...mediaFiles, ...filesArray]);

      // Create preview URLs
      const newPreviewUrls = filesArray.map(file => URL.createObjectURL(file));
      setMediaPreviewUrls([...mediaPreviewUrls, ...newPreviewUrls]);
    }
  };

  // Remove a media file
  const removeMedia = (index: number) => {
    const newFiles = [...mediaFiles];
    newFiles.splice(index, 1);
    setMediaFiles(newFiles);

    const newPreviewUrls = [...mediaPreviewUrls];
    URL.revokeObjectURL(newPreviewUrls[index]);
    newPreviewUrls.splice(index, 1);
    setMediaPreviewUrls(newPreviewUrls);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      // Redirect to sign in if not logged in
      router.push('/auth/signin?returnUrl=/cases/create');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage('');

    try {
      // Prepare case data
      const caseData = {
        title,
        location,
        description,
        sourceUrl: sourceUrl || undefined,
        currentStage,
        createdBy: user.id,
      };

      // Check if there are media files to upload
      if (mediaFiles.length > 0) {
        console.log(`Attempting to upload ${mediaFiles.length} files`);

        // Validate file sizes
        const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
        const oversizedFiles = mediaFiles.filter(file => file.size > maxSizeInBytes);

        if (oversizedFiles.length > 0) {
          const errorMsg = `${oversizedFiles.length} file(s) exceed the maximum size of 10MB. Please reduce file size and try again.`;
          showToast(errorMsg, 'error');
          setErrorMessage(errorMsg);
          setIsSubmitting(false);
          return;
        }
      }

      // Save case to Firebase
      const caseId = await createCase(caseData, mediaFiles);

      if (caseId) {
        // Clean up preview URLs
        mediaPreviewUrls.forEach(url => URL.revokeObjectURL(url));

        // Show success message with toast
        showToast('Case created successfully!', 'success');

        // Short delay before redirect for better UX
        setTimeout(() => {
          // Redirect to the case page
          router.push(`/cases/${caseId}`);
        }, 1000);
      } else {
        const errorMsg = 'Failed to create case. Please try again.';
        showToast(errorMsg, 'error');
        setErrorMessage(errorMsg);
      }
    } catch (error: any) {
      console.error('Error creating case:', error);

      // Provide more specific error message
      let errorMsg = '';
      if (error.message && error.message.includes('Upload failed')) {
        errorMsg = 'Failed to upload images. Please check your internet connection and try again.';
      } else {
        errorMsg = `An error occurred while creating the case: ${error.message || 'Unknown error'}`;
      }

      showToast(errorMsg, 'error');
      setErrorMessage(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Layout title="Loading... - Public Eye">
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
        </div>
      </Layout>
    );
  }

  // If not authenticated, don't render the form (will redirect in useEffect)
  if (!user) {
    return (
      <Layout title="Sign In Required - Public Eye">
        <div className="flex justify-center items-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Sign in required</h2>
            <p className="text-gray-600">Please sign in to create a case.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
      <Layout title="Create Case - Public Eye">
        <section className="bg-gradient-to-r from-red-700 to-red-900 text-white py-12">
          <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">Create a New Case</h1>
          <p className="text-xl opacity-90">Document a case that needs public attention and tracking.</p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-md p-8 max-w-3xl mx-auto">
          <form onSubmit={handleSubmit}>
            {/* Case Title */}
            <div className="mb-6">
              <label htmlFor="title" className="block text-gray-700 font-semibold mb-2">
                Case Title
              </label>
              <input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Enter a clear, descriptive title"
                required
              />
            </div>

            {/* Location */}
            <div className="mb-6">
              <label htmlFor="location" className="block text-gray-700 font-semibold mb-2">
                Location
              </label>
              <input
                id="location"
                type="text"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="City, State, Country"
                required
              />
            </div>

            {/* Media Upload */}
            <div className="mb-6">
              <label className="block text-gray-700 font-semibold mb-2">
                Media (Photos/Videos)
              </label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-red-500 hover:bg-red-50 transition-colors"
                onClick={() => document.getElementById('media-input')?.click()}
              >
                <input
                  type="file"
                  id="media-input"
                  multiple
                  accept="image/*,video/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <FaUpload className="mx-auto text-5xl text-red-600 mb-4" />
                <p className="text-gray-700 font-medium">Click to upload or drag and drop</p>
                <p className="text-gray-500 text-sm mt-2">
                  Supported formats: JPG, PNG, MP4, MOV (max 10MB each)
                </p>
              </div>

              {/* Media Previews */}
              {mediaPreviewUrls.length > 0 && (
                <div className="grid grid-cols-4 gap-4 mt-4">
                  {mediaPreviewUrls.map((url, index) => (
                    <div key={index} className="relative h-24 rounded-md overflow-hidden">
                      <img
                        src={url}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => removeMedia(index)}
                        className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Source URL */}
            <div className="mb-6">
              <label htmlFor="sourceUrl" className="block text-gray-700 font-semibold mb-2">
                Related URL
              </label>
              <input
                id="sourceUrl"
                type="url"
                value={sourceUrl}
                onChange={(e) => setSourceUrl(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="News article, official report, or other relevant link"
              />
            </div>

            {/* Case Description */}
            <div className="mb-6">
              <label htmlFor="description" className="block text-gray-700 font-semibold mb-2">
                Case Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={6}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Provide a detailed account of the case..."
                required
              ></textarea>
            </div>

            {/* Current Progress Stage */}
            <div className="mb-8">
              <label className="block text-gray-700 font-semibold mb-3">
                Current Progress Stage
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.REPORTED
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.REPORTED)}
                >
                  <FaFlag className="mx-auto text-2xl text-red-600 mb-2" />
                  <p className="font-medium">REPORTED</p>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.INVESTIGATING
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.INVESTIGATING)}
                >
                  <FaSearch className="mx-auto text-2xl text-orange-500 mb-2" />
                  <p className="font-medium">INVESTIGATING</p>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.EVIDENCE_COLLECTED
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.EVIDENCE_COLLECTED)}
                >
                  <FaUserLock className="mx-auto text-2xl text-yellow-500 mb-2" />
                  <p className="font-medium">EVIDENCE COLLECTED</p>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.VERIFIED
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.VERIFIED)}
                >
                  <FaGavel className="mx-auto text-2xl text-blue-500 mb-2" />
                  <p className="font-medium">VERIFIED</p>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.RESOLVED
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.RESOLVED)}
                >
                  <FaBalanceScale className="mx-auto text-2xl text-indigo-600 mb-2" />
                  <p className="font-medium">RESOLVED</p>
                </div>

                <div
                  className={`border rounded-lg p-4 text-center cursor-pointer ${
                    currentStage === CaseStage.CLOSED
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50'
                  }`}
                  onClick={() => setCurrentStage(CaseStage.CLOSED)}
                >
                  <FaCheckCircle className="mx-auto text-2xl text-green-600 mb-2" />
                  <p className="font-medium">CLOSED</p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {errorMessage && (
              <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
                {errorMessage}
              </div>
            )}

            {/* Submit Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-full font-medium hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating Case...' : 'Submit Case'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default CreateCase;
