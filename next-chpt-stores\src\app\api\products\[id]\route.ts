import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, Product } from '@/utils/database';

// Define the route params type
type RouteParams = {
  params: {
    id: string;
  };
}

/**
 * GET /api/products/[id]
 * Fetch a single product by ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get product by ID
    const product = await Product.findOne({ id: params.id });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(product);
  } catch (error) {
    console.error(`Error fetching product ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/products/[id]
 * Update a product
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Find and update product
    const updatedProduct = await Product.findOneAndUpdate(
      { id: params.id },
      { $set: body },
      { new: true }
    );

    if (!updatedProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error(`Error updating product ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/products/[id]
 * Delete a product
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find and delete product
    const deletedProduct = await Product.findOneAndDelete({ id: params.id });

    if (!deletedProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Product deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error deleting product ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
