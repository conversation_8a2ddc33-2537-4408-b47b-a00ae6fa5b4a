/**
 * API service for interacting with the backend
 * Handles authentication, data fetching, and error handling
 */

import { Platform } from 'react-native';
import { getItem, setItem } from './storage';

// API configuration
const API_CONFIG = {
  // Replace with your actual API URL
  baseUrl: 'https://api.ndungusshop.com/v1',
  timeout: 10000, // 10 seconds

  // MongoDB Data API configuration
  mongoDbDataApi: {
    baseUrl: 'https://data.mongodb-api.com/app/data-api/endpoint/data/v1',
    apiKey: 'your-api-key',
    dataSource: 'ClusterNDUNGU',
    database: 'ndungus_shop',
  },
};

// Import storage key
import { StorageKey } from './storage';

// Auth token storage key
const AUTH_TOKEN_KEY = StorageKey.AUTH_TOKEN;

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  TIMEOUT = 'TIMEOUT',
  SERVER = 'SERVER',
  AUTH = 'AUTH',
  UNKNOWN = 'UNKNOWN',
}

// API error class
export class ApiError extends Error {
  type: ErrorType;
  status?: number;

  constructor(message: string, type: ErrorType, status?: number) {
    super(message);
    this.type = type;
    this.status = status;
    this.name = 'ApiError';
  }
}

// Get stored auth token
export const getAuthToken = async (): Promise<string | null> => {
  return getItem(AUTH_TOKEN_KEY);
};

// Set auth token
export const setAuthToken = async (token: string): Promise<void> => {
  return setItem(AUTH_TOKEN_KEY, token);
};

// Clear auth token
export const clearAuthToken = async (): Promise<void> => {
  return setItem(AUTH_TOKEN_KEY, '');
};

// Check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const token = await getAuthToken();
  return !!token;
};

// API request options
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  requiresAuth?: boolean;
  timeout?: number;
}

// Make API request
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<T> => {
  const {
    method = 'GET',
    headers = {},
    body,
    requiresAuth = true,
    timeout = API_CONFIG.timeout,
  } = options;

  // Build request URL
  const url = `${API_CONFIG.baseUrl}${endpoint}`;

  // Set up headers
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': `NdungusShop/${Platform.OS}`,
    ...headers,
  };

  // Add auth token if required
  if (requiresAuth) {
    const token = await getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      throw new ApiError('Authentication required', ErrorType.AUTH);
    }
  }

  // Set up request options
  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
  };

  // Add body if provided
  if (body) {
    requestOptions.body = JSON.stringify(body);
  }

  try {
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    requestOptions.signal = controller.signal;

    // Make request
    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);

    // Handle response
    if (!response.ok) {
      // Handle authentication errors
      if (response.status === 401) {
        await clearAuthToken();
        throw new ApiError('Authentication failed', ErrorType.AUTH, response.status);
      }

      // Handle other errors
      throw new ApiError(
        `API request failed with status ${response.status}`,
        ErrorType.SERVER,
        response.status
      );
    }

    // Parse response
    const data = await response.json();
    return data as T;
  } catch (error: any) {
    // Handle fetch errors
    if (error.name === 'AbortError') {
      throw new ApiError('Request timed out', ErrorType.TIMEOUT);
    }

    if (error instanceof ApiError) {
      throw error;
    }

    if (error.message === 'Network request failed') {
      throw new ApiError('Network request failed', ErrorType.NETWORK);
    }

    throw new ApiError(error.message || 'Unknown error', ErrorType.UNKNOWN);
  }
};

// MongoDB Data API request
export const mongoDbRequest = async <T>(
  action: string,
  collection: string,
  data: any = {}
): Promise<T> => {
  const { baseUrl, apiKey, dataSource, database } = API_CONFIG.mongoDbDataApi;

  const url = `${baseUrl}/${action}`;

  const requestOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'api-key': apiKey,
    },
    body: JSON.stringify({
      dataSource,
      database,
      collection,
      ...data,
    }),
  };

  try {
    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new ApiError(
        `MongoDB Data API request failed with status ${response.status}`,
        ErrorType.SERVER,
        response.status
      );
    }

    return await response.json() as T;
  } catch (error: any) {
    if (error instanceof ApiError) {
      throw error;
    }

    if (error.message === 'Network request failed') {
      throw new ApiError('Network request failed', ErrorType.NETWORK);
    }

    throw new ApiError(error.message || 'Unknown error', ErrorType.UNKNOWN);
  }
};

// API endpoints
export const api = {
  // MongoDB Data API endpoints
  mongodb: {
    // Get all documents from a collection
    getAll: async <T>(collection: string): Promise<T[]> => {
      const result = await mongoDbRequest<{ documents: T[] }>('find', collection, {
        filter: {},
      });
      return result.documents || [];
    },

    // Get a document by ID
    getById: async <T>(collection: string, id: string): Promise<T | null> => {
      const result = await mongoDbRequest<{ document: T | null }>('findOne', collection, {
        filter: { id },
      });
      return result.document;
    },

    // Insert a document
    insert: async <T>(collection: string, document: T): Promise<T> => {
      await mongoDbRequest('insertOne', collection, {
        document,
      });
      return document;
    },

    // Update a document
    update: async <T>(collection: string, id: string, update: Partial<T>): Promise<boolean> => {
      const result = await mongoDbRequest<{ modifiedCount: number }>('updateOne', collection, {
        filter: { id },
        update: { $set: update },
      });
      return result.modifiedCount > 0;
    },

    // Delete a document
    delete: async (collection: string, id: string): Promise<boolean> => {
      const result = await mongoDbRequest<{ deletedCount: number }>('deleteOne', collection, {
        filter: { id },
      });
      return result.deletedCount > 0;
    },
  },

  // Auth endpoints
  auth: {
    login: async (email: string, password: string) => {
      const data = await apiRequest<{ token: string }>('/auth/login', {
        method: 'POST',
        body: { email, password },
        requiresAuth: false,
      });
      await setAuthToken(data.token);
      return data;
    },
    register: async (userData: any) => {
      return apiRequest<{ token: string }>('/auth/register', {
        method: 'POST',
        body: userData,
        requiresAuth: false,
      });
    },
    logout: async () => {
      await clearAuthToken();
      return { success: true };
    },
  },

  // Inventory endpoints
  inventory: {
    getAll: async () => {
      return apiRequest<any[]>('/inventory');
    },
    getById: async (id: string) => {
      return apiRequest<any>(`/inventory/${id}`);
    },
    create: async (item: any) => {
      return apiRequest<any>('/inventory', {
        method: 'POST',
        body: item,
      });
    },
    update: async (id: string, item: any) => {
      return apiRequest<any>(`/inventory/${id}`, {
        method: 'PUT',
        body: item,
      });
    },
    delete: async (id: string) => {
      return apiRequest<any>(`/inventory/${id}`, {
        method: 'DELETE',
      });
    },
  },

  // Orders endpoints
  orders: {
    getAll: async () => {
      return apiRequest<any[]>('/orders');
    },
    getById: async (id: string) => {
      return apiRequest<any>(`/orders/${id}`);
    },
    create: async (order: any) => {
      return apiRequest<any>('/orders', {
        method: 'POST',
        body: order,
      });
    },
    update: async (id: string, order: any) => {
      return apiRequest<any>(`/orders/${id}`, {
        method: 'PUT',
        body: order,
      });
    },
    pickOrder: async (id: string, userId: string) => {
      return apiRequest<any>(`/orders/${id}/pick`, {
        method: 'POST',
        body: { userId },
      });
    },
    releaseOrder: async (id: string) => {
      return apiRequest<any>(`/orders/${id}/release`, {
        method: 'POST',
      });
    },
    fulfillOrder: async (id: string) => {
      return apiRequest<any>(`/orders/${id}/fulfill`, {
        method: 'POST',
      });
    },
  },

  // Sales endpoints
  sales: {
    getAll: async () => {
      return apiRequest<any[]>('/sales');
    },
    getById: async (id: string) => {
      return apiRequest<any>(`/sales/${id}`);
    },
    create: async (sale: any) => {
      return apiRequest<any>('/sales', {
        method: 'POST',
        body: sale,
      });
    },
  },

  // Analytics endpoints
  analytics: {
    getSummary: async (timeRange: string) => {
      return apiRequest<any>(`/analytics/summary?timeRange=${timeRange}`);
    },
    getTopProducts: async (timeRange: string) => {
      return apiRequest<any[]>(`/analytics/top-products?timeRange=${timeRange}`);
    },
    getTopCategories: async (timeRange: string) => {
      return apiRequest<any[]>(`/analytics/top-categories?timeRange=${timeRange}`);
    },
    getSalesTrend: async (timeRange: string) => {
      return apiRequest<any[]>(`/analytics/sales-trend?timeRange=${timeRange}`);
    },
  },

  // Notes endpoints
  notes: {
    getAll: async () => {
      return apiRequest<any[]>('/notes');
    },
    getByDate: async (date: string) => {
      return apiRequest<any[]>(`/notes?date=${date}`);
    },
    create: async (note: any) => {
      return apiRequest<any>('/notes', {
        method: 'POST',
        body: note,
      });
    },
    update: async (id: string, note: any) => {
      return apiRequest<any>(`/notes/${id}`, {
        method: 'PUT',
        body: note,
      });
    },
    delete: async (id: string) => {
      return apiRequest<any>(`/notes/${id}`, {
        method: 'DELETE',
      });
    },
  },

  // Potential sales endpoints
  potentialSales: {
    getAll: async () => {
      return apiRequest<any[]>('/potential-sales');
    },
    getById: async (id: string) => {
      return apiRequest<any>(`/potential-sales/${id}`);
    },
    create: async (potentialSale: any) => {
      return apiRequest<any>('/potential-sales', {
        method: 'POST',
        body: potentialSale,
      });
    },
    update: async (id: string, potentialSale: any) => {
      return apiRequest<any>(`/potential-sales/${id}`, {
        method: 'PUT',
        body: potentialSale,
      });
    },
    updateStatus: async (id: string, status: string) => {
      return apiRequest<any>(`/potential-sales/${id}/status`, {
        method: 'PUT',
        body: { status },
      });
    },
  },

  // Sync endpoint
  sync: {
    uploadOfflineData: async (data: any) => {
      return apiRequest<any>('/sync', {
        method: 'POST',
        body: data,
      });
    },
  },
};
