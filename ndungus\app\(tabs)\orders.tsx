import { MaterialIcons } from '@expo/vector-icons';
import { <PERSON> } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, Text, View, FlatList, RefreshControl, Pressable } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { OrderCard } from '@/components/ui/OrderCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock data for orders
const mockOrders = [
  {
    id: '1',
    name: 'iPhone 13 Pro',
    image: 'https://images.unsplash.com/photo-1632661674596-df8be070a5c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8aXBob25lJTIwMTMlMjBwcm98ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
    description: 'Customer wants the latest iPhone model in Sierra Blue color with 256GB storage.',
    contact: '+254712345678',
    units: 1,
    category: 'Electronics',
    date: new Date(2023, 4, 15),
    status: 'pending',
    dayCount: 3,
  },
  {
    id: '2',
    name: 'Sony PlayStation 5',
    image: 'https://images.unsplash.com/photo-1606813907291-d86efa9b94db?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8cGxheXN0YXRpb24lMjA1fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
    description: 'Standard edition with disc drive and an extra controller.',
    contact: '+254723456789',
    units: 1,
    category: 'Electronics',
    date: new Date(2023, 4, 14),
    status: 'picked',
    pickedBy: 'John Doe',
    dayCount: 4,
  },
  {
    id: '3',
    name: 'Nike Air Jordan 1',
    description: 'Size 42, Chicago colorway, authentic.',
    contact: '+254734567890',
    units: 1,
    category: 'Footwear',
    date: new Date(2023, 4, 10),
    status: 'fulfilled',
    pickedBy: 'Jane Smith',
    dayCount: 8,
  },
  {
    id: '4',
    name: 'Samsung 55" QLED TV',
    image: 'https://images.unsplash.com/photo-1593305841991-05c297ba4575?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8c2Ftc3VuZyUyMHR2fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
    description: 'Smart TV with 4K resolution and HDR support.',
    contact: '+254745678901',
    units: 1,
    category: 'Electronics',
    date: new Date(2023, 4, 12),
    status: 'pending',
    dayCount: 6,
  },
  {
    id: '5',
    name: 'Apple MacBook Pro M2',
    description: '14-inch model with 16GB RAM and 512GB storage.',
    contact: '+254756789012',
    units: 1,
    category: 'Electronics',
    date: new Date(2023, 4, 16),
    status: 'pending',
    dayCount: 2,
  },
];

type OrderStatus = 'pending' | 'picked' | 'fulfilled';

export default function OrdersScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<OrderStatus | 'all'>('all');

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate a data refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  // Filter orders based on active tab
  const filteredOrders = activeTab === 'all'
    ? mockOrders
    : mockOrders.filter(order => order.status === activeTab);

  // Handle order actions
  const handlePickOrder = (id: string) => {
    console.log(`Picking order ${id}`);
    // In a real app, you would update the order status in the database
  };

  const handleReleaseOrder = (id: string) => {
    console.log(`Releasing order ${id}`);
    // In a real app, you would update the order status in the database
  };

  const handleFulfillOrder = (id: string) => {
    console.log(`Fulfilling order ${id}`);
    // In a real app, you would update the order status in the database
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <GlassCard style={styles.header}>
        <View style={styles.headerTop}>
          <Text
            style={[
              styles.title,
              { color: colorScheme === 'dark' ? Colors.white : Colors.black },
            ]}>
            Orders
          </Text>
          <Link href="/orders/new" asChild>
            <Button
              title="New Order"
              variant="primary"
              size="sm"
              icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
              onPress={() => {}}
            />
          </Link>
        </View>
        
        {/* Tabs */}
        <View style={styles.tabsContainer}>
          {(['all', 'pending', 'picked', 'fulfilled'] as const).map((tab) => (
            <Pressable
              key={tab}
              style={[
                styles.tab,
                activeTab === tab && {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 215, 0, 0.2)'
                    : 'rgba(255, 215, 0, 0.1)',
                  borderBottomColor: Colors.primary,
                  borderBottomWidth: 2,
                },
              ]}
              onPress={() => setActiveTab(tab)}>
              <Text
                style={[
                  styles.tabText,
                  {
                    color: activeTab === tab
                      ? Colors.primary
                      : colorScheme === 'dark'
                        ? Colors.gray300
                        : Colors.gray700,
                    fontFamily: activeTab === tab
                      ? Typography.fontFamily.semiBold
                      : Typography.fontFamily.regular,
                  },
                ]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
              {tab !== 'all' && (
                <View
                  style={[
                    styles.tabBadge,
                    {
                      backgroundColor: activeTab === tab
                        ? Colors.primary
                        : colorScheme === 'dark'
                          ? Colors.gray700
                          : Colors.gray300,
                    },
                  ]}>
                  <Text
                    style={[
                      styles.tabBadgeText,
                      {
                        color: activeTab === tab
                          ? Colors.black
                          : colorScheme === 'dark'
                            ? Colors.gray300
                            : Colors.gray700,
                      },
                    ]}>
                    {mockOrders.filter(order => order.status === tab).length}
                  </Text>
                </View>
              )}
            </Pressable>
          ))}
        </View>
      </GlassCard>

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <OrderCard
            name={item.name}
            image={item.image}
            description={item.description}
            contact={item.contact}
            units={item.units}
            category={item.category}
            date={item.date}
            status={item.status as any}
            pickedBy={item.pickedBy}
            dayCount={item.dayCount}
            onPress={() => {}}
            onPick={item.status === 'pending' ? () => handlePickOrder(item.id) : undefined}
            onRelease={item.status === 'picked' ? () => handleReleaseOrder(item.id) : undefined}
            onFulfill={item.status === 'picked' ? () => handleFulfillOrder(item.id) : undefined}
          />
        )}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <GlassCard style={styles.emptyContainer}>
            <MaterialIcons
              name="shopping-bag"
              size={48}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.emptyText,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              No orders found
            </Text>
            <Text
              style={[
                styles.emptySubtext,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {activeTab !== 'all'
                ? `No ${activeTab} orders at the moment`
                : 'Add your first customer order'}
            </Text>
            {activeTab === 'all' && (
              <Link href="/orders/new" asChild>
                <Button
                  title="New Order"
                  variant="primary"
                  icon={<MaterialIcons name="add" size={16} color={Colors.black} />}
                  style={{ marginTop: Spacing.md }}
                  onPress={() => {}}
                />
              </Link>
            )}
          </GlassCard>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.bold,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginTop: Spacing.sm,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  tabText: {
    fontSize: Typography.fontSize.sm,
  },
  tabBadge: {
    marginLeft: Spacing.xs,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: BorderRadius.full,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  listContainer: {
    paddingBottom: Spacing.xl,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
    marginTop: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginTop: Spacing.md,
  },
  emptySubtext: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
});
