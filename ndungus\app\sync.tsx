import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, ActivityIndicator } from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { isOnline, manualSync, getLastSyncResult, SyncStatus } from '@/services/sync';
import { getPendingSyncItems } from '@/services/storage';

export default function SyncScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [online, setOnline] = useState<boolean>(false);
  const [syncing, setSyncing] = useState<boolean>(false);
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [pendingItems, setPendingItems] = useState<number>(0);
  const [lastSync, setLastSync] = useState<any>(getLastSyncResult());

  // Check online status and pending items on mount
  useEffect(() => {
    const checkStatus = async () => {
      const isDeviceOnline = await isOnline();
      setOnline(isDeviceOnline);
      
      const items = await getPendingSyncItems();
      setPendingItems(items.length);
    };
    
    checkStatus();
    
    // Set up interval to check status
    const intervalId = setInterval(checkStatus, 10000);
    
    // Clean up interval
    return () => clearInterval(intervalId);
  }, []);

  // Handle sync
  const handleSync = async () => {
    setSyncing(true);
    setSyncProgress(0);
    
    try {
      await manualSync((status, progress) => {
        setSyncProgress(progress);
        if (status === SyncStatus.SUCCESS || status === SyncStatus.ERROR) {
          setSyncing(false);
        }
      });
      
      // Update last sync result
      setLastSync(getLastSyncResult());
      
      // Update pending items
      const items = await getPendingSyncItems();
      setPendingItems(items.length);
    } catch (error) {
      console.error('Sync error:', error);
      setSyncing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status color
  const getStatusColor = (status: SyncStatus) => {
    switch (status) {
      case SyncStatus.SUCCESS:
        return Colors.success;
      case SyncStatus.ERROR:
        return Colors.error;
      case SyncStatus.SYNCING:
        return Colors.primary;
      default:
        return colorScheme === 'dark' ? Colors.gray300 : Colors.gray700;
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Sync Data',
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.white,
          },
          headerTintColor: colorScheme === 'dark' ? Colors.white : Colors.black,
          headerShadowVisible: false,
        }}
      />
      <View
        style={[
          styles.container,
          { backgroundColor: colorScheme === 'dark' ? Colors.gray900 : Colors.gray50 },
        ]}>
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}>
          {/* Connection Status */}
          <GlassCard style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Text
                style={[
                  styles.statusTitle,
                  { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                ]}>
                Connection Status
              </Text>
              <View
                style={[
                  styles.statusIndicator,
                  {
                    backgroundColor: online ? Colors.success : Colors.error,
                  },
                ]}
              />
            </View>
            
            <Text
              style={[
                styles.statusText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {online ? 'Online - Connected to server' : 'Offline - Working in local mode'}
            </Text>
            
            <View style={styles.pendingItemsContainer}>
              <MaterialIcons
                name="sync"
                size={20}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              />
              <Text
                style={[
                  styles.pendingItemsText,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                {pendingItems} {pendingItems === 1 ? 'item' : 'items'} pending sync
              </Text>
            </View>
          </GlassCard>
          
          {/* Last Sync */}
          <GlassCard style={styles.lastSyncCard}>
            <Text
              style={[
                styles.lastSyncTitle,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Last Sync
            </Text>
            
            <View style={styles.lastSyncDetails}>
              <View style={styles.lastSyncItem}>
                <Text
                  style={[
                    styles.lastSyncLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  Status
                </Text>
                <Text
                  style={[
                    styles.lastSyncValue,
                    { color: getStatusColor(lastSync.status) },
                  ]}>
                  {lastSync.status === SyncStatus.IDLE ? 'Never synced' : lastSync.status}
                </Text>
              </View>
              
              <View style={styles.lastSyncItem}>
                <Text
                  style={[
                    styles.lastSyncLabel,
                    { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                  ]}>
                  Time
                </Text>
                <Text
                  style={[
                    styles.lastSyncValue,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  {formatDate(lastSync.timestamp)}
                </Text>
              </View>
              
              {lastSync.status !== SyncStatus.IDLE && (
                <>
                  <View style={styles.lastSyncItem}>
                    <Text
                      style={[
                        styles.lastSyncLabel,
                        { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                      ]}>
                      Synced Items
                    </Text>
                    <Text
                      style={[
                        styles.lastSyncValue,
                        { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                      ]}>
                      {lastSync.syncedItems}
                    </Text>
                  </View>
                  
                  <View style={styles.lastSyncItem}>
                    <Text
                      style={[
                        styles.lastSyncLabel,
                        { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                      ]}>
                      Failed Items
                    </Text>
                    <Text
                      style={[
                        styles.lastSyncValue,
                        { color: lastSync.failedItems > 0 ? Colors.error : (colorScheme === 'dark' ? Colors.white : Colors.black) },
                      ]}>
                      {lastSync.failedItems}
                    </Text>
                  </View>
                </>
              )}
              
              {lastSync.message && (
                <View style={styles.lastSyncMessage}>
                  <Text
                    style={[
                      styles.lastSyncMessageText,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    {lastSync.message}
                  </Text>
                </View>
              )}
            </View>
          </GlassCard>
          
          {/* Sync Button */}
          <GlassCard style={styles.syncButtonCard}>
            {syncing ? (
              <View style={styles.syncingContainer}>
                <ActivityIndicator size="large" color={Colors.primary} />
                <Text
                  style={[
                    styles.syncingText,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  Syncing data...
                </Text>
                <View style={styles.progressBarContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      {
                        width: `${syncProgress}%`,
                        backgroundColor: Colors.primary,
                      },
                    ]}
                  />
                </View>
              </View>
            ) : (
              <>
                <Text
                  style={[
                    styles.syncButtonText,
                    { color: colorScheme === 'dark' ? Colors.white : Colors.black },
                  ]}>
                  Sync your data with the server to ensure everything is up to date.
                </Text>
                <Button
                  title="Sync Now"
                  variant="primary"
                  icon={<MaterialIcons name="sync" size={18} color={Colors.black} />}
                  onPress={handleSync}
                  disabled={!online || pendingItems === 0}
                  style={styles.syncButton}
                />
                {(!online || pendingItems === 0) && (
                  <Text
                    style={[
                      styles.syncDisabledText,
                      { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                    ]}>
                    {!online
                      ? 'You need to be online to sync data'
                      : 'No pending items to sync'}
                  </Text>
                )}
              </>
            )}
          </GlassCard>
          
          {/* Sync Info */}
          <GlassCard style={styles.syncInfoCard}>
            <Text
              style={[
                styles.syncInfoTitle,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              About Syncing
            </Text>
            <Text
              style={[
                styles.syncInfoText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              This app works offline-first, which means you can use it without an internet connection. When you're back online, your data will be synced with the server.
            </Text>
            <Text
              style={[
                styles.syncInfoText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              • All changes are saved locally first
            </Text>
            <Text
              style={[
                styles.syncInfoText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              • Sync happens automatically when online
            </Text>
            <Text
              style={[
                styles.syncInfoText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              • You can also manually sync your data
            </Text>
            <Text
              style={[
                styles.syncInfoText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              • If conflicts occur, the most recent changes will be kept
            </Text>
          </GlassCard>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingBottom: Spacing.xl,
  },
  statusCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  statusTitle: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: Spacing.md,
  },
  pendingItemsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  pendingItemsText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  lastSyncCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  lastSyncTitle: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  lastSyncDetails: {
    gap: Spacing.sm,
  },
  lastSyncItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastSyncLabel: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
  },
  lastSyncValue: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
  },
  lastSyncMessage: {
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: BorderRadius.md,
  },
  lastSyncMessageText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  syncButtonCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    alignItems: 'center',
  },
  syncButtonText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  syncButton: {
    minWidth: 150,
  },
  syncDisabledText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
    marginTop: Spacing.sm,
  },
  syncingContainer: {
    alignItems: 'center',
    gap: Spacing.md,
  },
  syncingText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.medium,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: BorderRadius.full,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
  },
  syncInfoCard: {
    padding: Spacing.md,
  },
  syncInfoTitle: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: Spacing.md,
  },
  syncInfoText: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    marginBottom: Spacing.sm,
  },
});
