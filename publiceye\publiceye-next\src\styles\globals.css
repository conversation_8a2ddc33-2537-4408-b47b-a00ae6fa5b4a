@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #D00000;
  --secondary-color: #9D0208;
  --accent-color: #03045E;
  --hope-color: #0077B6;
  --hope-accent: #90E0EF;
  --dark-text: #370617;
  --medium-text: #6A040F;
  --light-bg: #FFF1F0;
}

body {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--dark-text);
  background-color: var(--light-bg);
  background-image:
    radial-gradient(at 40% 20%, rgba(208, 0, 0, 0.08) 0px, transparent 50%),
    radial-gradient(at 80% 0%, rgba(0, 119, 182, 0.07) 0px, transparent 50%),
    radial-gradient(at 0% 50%, rgba(157, 2, 8, 0.08) 0px, transparent 50%),
    radial-gradient(at 90% 90%, rgba(56, 176, 0, 0.05) 0px, transparent 50%);
  background-attachment: fixed;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c1c1c1;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
