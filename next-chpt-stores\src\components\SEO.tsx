import { Metadata } from 'next';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  ogType?: string;
}

// This function generates metadata for a page
export function generateMetadata({
  title,
  description,
  keywords = "university essentials, student supplies, dorm room, college necessities, student shopping",
  ogImage = "/logo.png",
  ogUrl = "https://nextchapterstores.com",
  ogType = "website"
}: SEOProps): Metadata {
  // Ensure title has the site name
  const fullTitle = title.includes("Next Chapter Stores")
    ? title
    : `${title} | Next Chapter Stores`;

  return {
    title: fullTitle,
    description,
    keywords,
    openGraph: {
      title: fullTitle,
      description,
      url: ogUrl,
      images: [
        {
          url: ogImage,
          width: 800,
          height: 600,
          alt: 'Next Chapter Stores',
        },
      ],
      type: ogType,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [ogImage],
    },
    alternates: {
      canonical: ogUrl,
    },
  };
}
