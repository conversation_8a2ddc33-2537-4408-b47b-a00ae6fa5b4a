import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function NotFound() {
  return (
    <>
      <Header />
      
      <main className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-6xl font-bold text-primary mb-4">404</h1>
          <h2 className="text-3xl font-semibold text-secondary mb-6">Page Not Found</h2>
          <p className="text-lg text-secondary mb-8 max-w-2xl mx-auto">
            The page you are looking for might have been removed, had its name changed, 
            or is temporarily unavailable.
          </p>
          <Link 
            href="/" 
            className="inline-block bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-secondary transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </main>
      
      <Footer />
    </>
  );
}
