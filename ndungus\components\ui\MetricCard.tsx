import { MaterialIcons } from '@expo/vector-icons';
import React, { ReactNode } from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';

import { GlassCard } from '@/components/ui/GlassCard';
import { Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type MetricCardProps = {
  title: string;
  value: string | number;
  icon?: keyof typeof MaterialIcons.glyphMap;
  iconColor?: string;
  iconBackground?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  footer?: ReactNode;
  style?: ViewStyle;
};

/**
 * A card component for displaying metrics on the dashboard
 */
export function MetricCard({
  title,
  value,
  icon,
  iconColor,
  iconBackground,
  trend,
  footer,
  style,
}: MetricCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Default icon color if not provided
  const defaultIconColor = colorScheme === 'dark' ? Colors.white : Colors.black;
  const defaultIconBackground = colorScheme === 'dark' 
    ? 'rgba(255, 255, 255, 0.1)' 
    : 'rgba(0, 0, 0, 0.05)';
  
  return (
    <GlassCard style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={[
          styles.title, 
          { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 }
        ]}>
          {title}
        </Text>
        {icon && (
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor: iconBackground || defaultIconBackground,
              },
            ]}>
            <MaterialIcons
              name={icon}
              size={20}
              color={iconColor || defaultIconColor}
            />
          </View>
        )}
      </View>
      
      <View style={styles.valueContainer}>
        <Text
          style={[
            styles.value,
            { color: colorScheme === 'dark' ? Colors.white : Colors.black },
          ]}>
          {value}
        </Text>
        
        {trend && (
          <View
            style={[
              styles.trendContainer,
              {
                backgroundColor: trend.isPositive
                  ? `${Colors.success}${colorScheme === 'dark' ? '30' : '15'}`
                  : `${Colors.error}${colorScheme === 'dark' ? '30' : '15'}`,
              },
            ]}>
            <MaterialIcons
              name={trend.isPositive ? 'arrow-upward' : 'arrow-downward'}
              size={12}
              color={trend.isPositive ? Colors.success : Colors.error}
            />
            <Text
              style={[
                styles.trendText,
                {
                  color: trend.isPositive ? Colors.success : Colors.error,
                },
              ]}>
              {trend.value}%
            </Text>
          </View>
        )}
      </View>
      
      {footer && <View style={styles.footer}>{footer}</View>}
    </GlassCard>
  );
}

const styles = StyleSheet.create({
  container: {
    minWidth: 150,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  title: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  value: {
    fontSize: Typography.fontSize['2xl'],
    fontFamily: Typography.fontFamily.bold,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: 4,
    gap: 2,
  },
  trendText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
  },
  footer: {
    marginTop: Spacing.xs,
  },
});
