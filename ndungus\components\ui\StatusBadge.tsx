import React from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';

import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type StatusType = 'success' | 'warning' | 'error' | 'info' | 'pending' | 'fulfilled' | 'picked';

type StatusBadgeProps = {
  status: StatusType;
  label?: string;
  style?: ViewStyle;
};

/**
 * A badge component for displaying status indicators
 */
export function StatusBadge({ status, label, style }: StatusBadgeProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Get the appropriate colors based on status
  const { backgroundColor, textColor } = getStatusColors(status, colorScheme);
  
  // Get the default label if not provided
  const displayLabel = label || getDefaultLabel(status);
  
  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor,
        },
        style,
      ]}>
      <Text
        style={[
          styles.text,
          {
            color: textColor,
          },
        ]}>
        {displayLabel}
      </Text>
    </View>
  );
}

// Helper function to get colors based on status
function getStatusColors(status: StatusType, colorScheme: 'light' | 'dark') {
  const opacity = colorScheme === 'dark' ? '30' : '15';
  
  switch (status) {
    case 'success':
    case 'fulfilled':
      return {
        backgroundColor: `${Colors.success}${opacity}`,
        textColor: Colors.success,
      };
    case 'warning':
    case 'pending':
      return {
        backgroundColor: `${Colors.warning}${opacity}`,
        textColor: Colors.warning,
      };
    case 'error':
      return {
        backgroundColor: `${Colors.error}${opacity}`,
        textColor: Colors.error,
      };
    case 'info':
      return {
        backgroundColor: `${Colors.info}${opacity}`,
        textColor: Colors.info,
      };
    case 'picked':
      return {
        backgroundColor: `${Colors.secondary}${opacity}`,
        textColor: Colors.secondary,
      };
    default:
      return {
        backgroundColor: `${Colors.gray500}${opacity}`,
        textColor: Colors.gray500,
      };
  }
}

// Helper function to get default label based on status
function getDefaultLabel(status: StatusType): string {
  switch (status) {
    case 'success':
      return 'Success';
    case 'warning':
      return 'Warning';
    case 'error':
      return 'Error';
    case 'info':
      return 'Info';
    case 'pending':
      return 'Pending';
    case 'fulfilled':
      return 'Fulfilled';
    case 'picked':
      return 'Picked';
    default:
      return '';
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.medium,
    textTransform: 'uppercase',
  },
});
