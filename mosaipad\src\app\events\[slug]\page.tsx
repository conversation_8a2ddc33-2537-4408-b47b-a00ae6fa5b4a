"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Image as ImageIcon, Clock, Users, Sparkles, CheckCircle } from "lucide-react";

export default function EventPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    notes: "",
    standoutThought: "",
    imageFile: null as File | null,
  });

  // Mock event data - in real app, this would be fetched from API
  const eventData = {
    name: "UX Summit 2024",
    description: "A collaborative conference exploring the future of user experience design",
    date: "March 15, 2024",
    deadline: "March 20, 2024, 11:59 PM",
    submissionsCount: 23,
    isActive: true,
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Implement API call to submit notes
      console.log("Submitting notes:", formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting notes:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      imageFile: file
    }));
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-sky-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle>Notes Submitted Successfully!</CardTitle>
            <CardDescription>
              Thank you for contributing to {eventData.name}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-sm text-gray-600">
              Your notes have been added to the collective summary. We'll notify you when the final summary is ready!
            </p>
            <div className="bg-indigo-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-indigo-900">
                Summary will be available after: {eventData.deadline}
              </p>
            </div>
            <Button asChild className="w-full">
              <Link href="/">Back to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-sky-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Mosaipad</span>
            </div>
            <div className="text-sm text-gray-600">
              Event: {eventData.name}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Event Info */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {eventData.name}
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            {eventData.description}
          </p>
          
          <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              Deadline: {eventData.deadline}
            </div>
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-2" />
              {eventData.submissionsCount} contributions so far
            </div>
          </div>
        </div>

        {/* Submission Form */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Share Your Notes</CardTitle>
            <CardDescription>
              Help build a collective summary by sharing what you learned or found interesting
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name and Email */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Name (Optional)
                  </label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder="Your name"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email (Optional)
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    To receive the final summary
                  </p>
                </div>
              </div>

              {/* Notes Input */}
              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="w-4 h-4 inline mr-1" />
                  Your Notes *
                </label>
                <Textarea
                  id="notes"
                  name="notes"
                  required
                  placeholder="Paste your notes here... What did you learn? What stood out to you?"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={6}
                />
              </div>

              {/* Image Upload */}
              <div>
                <label htmlFor="imageFile" className="block text-sm font-medium text-gray-700 mb-2">
                  <ImageIcon className="w-4 h-4 inline mr-1" />
                  Or Upload Handwritten Notes
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
                  <input
                    id="imageFile"
                    name="imageFile"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <label htmlFor="imageFile" className="cursor-pointer">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Click to upload an image of your handwritten notes
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      We'll use OCR to extract the text automatically
                    </p>
                  </label>
                  {formData.imageFile && (
                    <p className="text-sm text-indigo-600 mt-2">
                      Selected: {formData.imageFile.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Standout Thought */}
              <div>
                <label htmlFor="standoutThought" className="block text-sm font-medium text-gray-700 mb-2">
                  What stood out most to you? (Optional)
                </label>
                <Textarea
                  id="standoutThought"
                  name="standoutThought"
                  placeholder="Share your key takeaway or most interesting insight..."
                  value={formData.standoutThought}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || (!formData.notes && !formData.imageFile)}
                className="w-full"
              >
                {isLoading ? "Submitting..." : "Submit Notes"}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Info Section */}
        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-lg">What happens next?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-indigo-600 font-semibold">1</span>
                  </div>
                  <p className="font-medium">Notes Collected</p>
                  <p className="text-gray-600">All submissions gathered until deadline</p>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-sky-600 font-semibold">2</span>
                  </div>
                  <p className="font-medium">AI Processing</p>
                  <p className="text-gray-600">Smart clustering and summarization</p>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-emerald-600 font-semibold">3</span>
                  </div>
                  <p className="font-medium">Summary Ready</p>
                  <p className="text-gray-600">Beautiful collaborative document</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
