// Browser-compatible Cloudinary integration
// This file handles client-side uploads to Cloudinary

/**
 * Uploads a file to Cloudinary using the Upload API
 * This is a browser-compatible approach that doesn't use the Node.js SDK
 */
export const uploadImage = async (file: File): Promise<string> => {
  try {
    console.log('Starting upload to Cloudinary...');

    // First, convert the file to a data URL
    const fileDataUrl = await toBase64(file);

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', fileDataUrl as string);
    formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'public-eye');

    // Upload to Cloudinary via fetch API
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'du02pkrhf';
    const uploadUrl = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;

    console.log('Uploading to:', uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Cloudinary response:', errorText);
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Upload successful, secure URL:', data.secure_url);
    return data.secure_url;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw new Error('Failed to upload image');
  }
};

/**
 * Uploads multiple files to Cloudinary
 */
export const uploadMultipleImages = async (files: File[]): Promise<string[]> => {
  try {
    const uploadPromises = files.map(file => uploadImage(file));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw new Error('Failed to upload images');
  }
};

// Helper function to convert file to base64
const toBase64 = (file: File): Promise<string | ArrayBuffer | null> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
