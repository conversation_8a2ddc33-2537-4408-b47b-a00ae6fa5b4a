import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface Note {
  id: string;
  content: string;
  authorName?: string;
  standoutThought?: string;
}

export interface SummaryResult {
  keyTakeaways: string[];
  standoutThoughts: string[];
  unexpectedNuggets: string[];
  questionsReflections: string[];
  mentions: string[];
  communityQuotes: string[];
  htmlContent: string;
}

export async function generateCollaborativeSummary(
  notes: Note[],
  eventName: string,
  eventDescription?: string
): Promise<SummaryResult> {
  try {
    const notesText = notes.map((note, index) => 
      `Note ${index + 1} (${note.authorName || 'Anonymous'}):\n${note.content}\n${note.standoutThought ? `Standout thought: ${note.standoutThought}\n` : ''}`
    ).join('\n---\n');

    const prompt = `
You are an expert at synthesizing collective insights from event notes. Your task is to create a beautiful, structured summary that preserves the human perspective and highlights diverse thinking.

Event: ${eventName}
${eventDescription ? `Description: ${eventDescription}` : ''}

Notes from attendees:
${notesText}

Please analyze these notes and create a collaborative summary with the following sections:

1. KEY TAKEAWAYS (3-5 main insights that emerged across multiple notes)
2. STANDOUT THOUGHTS (unique perspectives or "aha moments" from individuals)
3. UNEXPECTED NUGGETS (surprising insights or outlier thinking that stood out)
4. QUESTIONS & REFLECTIONS (thought-provoking questions or areas for further exploration)
5. MENTIONS & LINKS (any tools, resources, people, or links mentioned)
6. COMMUNITY QUOTES (powerful direct quotes from the notes, with attribution if name provided)

Guidelines:
- Preserve the human voice and diverse perspectives
- Don't over-synthesize - maintain the richness of individual insights
- Highlight outlier thinking and unique viewpoints
- Use engaging, accessible language
- Include specific quotes when they're particularly insightful
- Group similar ideas but don't lose nuance

Return your response as a JSON object with the following structure:
{
  "keyTakeaways": ["takeaway1", "takeaway2", ...],
  "standoutThoughts": ["thought1", "thought2", ...],
  "unexpectedNuggets": ["nugget1", "nugget2", ...],
  "questionsReflections": ["question1", "question2", ...],
  "mentions": ["mention1", "mention2", ...],
  "communityQuotes": ["quote1 - Author", "quote2 - Anonymous", ...]
}
`;

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert at creating collaborative summaries that preserve human insight and diverse perspectives. Always respond with valid JSON."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No response from AI");
    }

    // Parse the JSON response
    const summaryData = JSON.parse(content);

    // Generate HTML content
    const htmlContent = generateHTMLSummary(summaryData, eventName);

    return {
      ...summaryData,
      htmlContent,
    };

  } catch (error) {
    console.error("AI summarization error:", error);
    throw new Error("Failed to generate summary");
  }
}

function generateHTMLSummary(data: any, eventName: string): string {
  return `
    <div class="summary-document">
      <header class="summary-header">
        <h1>📝 EventMosaic Collective Notes – ${eventName}</h1>
        <p class="subtitle">A collaborative summary of insights and perspectives</p>
      </header>

      <section class="summary-section">
        <h2>🌟 Key Takeaways</h2>
        <ul>
          ${data.keyTakeaways.map((item: string) => `<li>${item}</li>`).join('')}
        </ul>
      </section>

      <section class="summary-section">
        <h2>💡 Standout Thoughts</h2>
        <ul>
          ${data.standoutThoughts.map((item: string) => `<li>${item}</li>`).join('')}
        </ul>
      </section>

      <section class="summary-section">
        <h2>✨ Unexpected Nuggets</h2>
        <ul>
          ${data.unexpectedNuggets.map((item: string) => `<li>${item}</li>`).join('')}
        </ul>
      </section>

      <section class="summary-section">
        <h2>🧠 Questions & Reflections</h2>
        <ul>
          ${data.questionsReflections.map((item: string) => `<li>${item}</li>`).join('')}
        </ul>
      </section>

      <section class="summary-section">
        <h2>🔗 Mentions & Links</h2>
        <ul>
          ${data.mentions.map((item: string) => `<li>${item}</li>`).join('')}
        </ul>
      </section>

      <section class="summary-section">
        <h2>🙌 From the Community</h2>
        <div class="quotes">
          ${data.communityQuotes.map((quote: string) => `<blockquote>${quote}</blockquote>`).join('')}
        </div>
      </section>

      <footer class="summary-footer">
        <p>Compiled by <strong>Mosaipad</strong> • Transform everyone's notes into collective insights</p>
      </footer>
    </div>
  `;
}
