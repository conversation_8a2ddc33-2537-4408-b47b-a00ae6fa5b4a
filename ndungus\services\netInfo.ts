/**
 * Simple NetInfo implementation to avoid dependency issues
 * This is a temporary solution until we can properly install @react-native-community/netinfo
 */

// NetInfo state interface
interface NetInfoState {
  isConnected: boolean | null;
  isInternetReachable: boolean | null;
  type: string;
  details: any;
}

// Default state - assume online for demo purposes
const defaultState: NetInfoState = {
  isConnected: true,
  isInternetReachable: true,
  type: 'wifi',
  details: null,
};

// NetInfo mock implementation
const NetInfo = {
  // Fetch network state
  fetch: async (): Promise<NetInfoState> => {
    // In a real implementation, this would check the actual network state
    // For now, we'll just return the default state
    return defaultState;
  },
  
  // Add event listener (mock implementation)
  addEventListener: (listener: (state: NetInfoState) => void) => {
    // In a real implementation, this would add an event listener
    // For now, we'll just call the listener with the default state
    setTimeout(() => {
      listener(defaultState);
    }, 0);
    
    // Return unsubscribe function
    return () => {
      // In a real implementation, this would remove the event listener
    };
  },
};

export default NetInfo;
