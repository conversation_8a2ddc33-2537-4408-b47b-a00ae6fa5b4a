import { notFound } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CategorySection from '@/components/CategorySection';
import BudgetRecommendation from '@/components/BudgetRecommendation';
import { getProductsByCategory, getCategories } from '@/data/products';

interface CategoryPageProps {
  params: {
    category: string;
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  // Make sure params is awaited
  const categorySlug = params.category;
  const categories = getCategories();

  // Find the category that matches the slug (case-insensitive)
  const category = categories.find(
    cat => cat.toLowerCase() === categorySlug.toLowerCase()
  );

  if (!category) {
    notFound();
  }

  const products = getProductsByCategory(category);

  return (
    <>
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h1 className="text-4xl font-bold text-primary mb-4">{category} Essentials</h1>
            <p className="text-secondary text-lg max-w-3xl mx-auto">
              Everything you need for your {category.toLowerCase()} as you start your university journey.
              Quality products at student-friendly prices.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <BudgetRecommendation
                products={products}
                category={category}
              />
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-primary mb-4">Shopping Tips</h3>
              <ul className="space-y-2 text-secondary">
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span>Start with essential items before adding extras</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span>Consider sharing costs with roommates for common items</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span>Check what's already provided in your accommodation</span>
                </li>
                <li className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span>Use our budget recommendation tool to optimize your spending</span>
                </li>
              </ul>
            </div>
          </div>

          <CategorySection
            title={category}
            products={products}
          />
        </div>
      </main>

      <Footer />
    </>
  );
}

// Generate static paths for all categories
export function generateStaticParams() {
  const categories = getCategories();

  return categories.map(category => ({
    category: category.toLowerCase(),
  }));
}
