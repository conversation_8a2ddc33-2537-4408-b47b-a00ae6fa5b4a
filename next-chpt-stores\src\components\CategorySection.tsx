"use client";

import { useState, useEffect } from 'react';
import ProductCard from './ProductCard';
import BudgetRecommendation from './BudgetRecommendation';

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  lifeChapter: string;
  category: string;
  subcategory: string;
  description: string;
}

interface CategorySectionProps {
  title: string;
  products: Product[];
}

export default function CategorySection({ title, products }: CategorySectionProps) {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [allSelected, setAllSelected] = useState(false);
  const [budgetInput, setBudgetInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);

  // Filter products based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredProducts(products);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query) ||
        product.subcategory.toLowerCase().includes(query)
      );
      setFilteredProducts(filtered);
    }
  }, [searchQuery, products]);

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (allSelected) {
      // If currently all selected, deselect all
      setSelectedProducts([]);
      setAllSelected(false);
    } else {
      // If not all selected, select all
      setSelectedProducts(filteredProducts.map(product => product.id));
      setAllSelected(true);
    }
  };

  // Handle individual product selection
  const handleProductSelect = (productId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Update allSelected state when individual selections change
  useEffect(() => {
    if (filteredProducts.length === 0) {
      setAllSelected(false);
    } else {
      setAllSelected(selectedProducts.length === filteredProducts.length);
    }
  }, [selectedProducts, filteredProducts]);

  const [cartSuccessMessage, setCartSuccessMessage] = useState<string | null>(null);

  const handleAddAllToCart = () => {
    if (selectedProducts.length === 0) {
      alert('Please select at least one item to add to cart');
      return;
    }

    if (confirm(`Do you want to add ${selectedProducts.length} selected items to your cart?`)) {
      // This would be replaced with actual cart functionality
      console.log(`Added ${selectedProducts.length} items to cart`);

      // Show success message
      setCartSuccessMessage(`Successfully added ${selectedProducts.length} items to your cart!`);

      // Clear the success message after 5 seconds
      setTimeout(() => {
        setCartSuccessMessage(null);
      }, 5000);
    }
  };

  const [showBudgetModal, setShowBudgetModal] = useState(false);

  const handleBudgetRecommendation = () => {
    const budget = parseFloat(budgetInput);
    if (isNaN(budget) || budget <= 0) {
      alert('Please enter a valid budget amount');
      return;
    }

    // Show the budget recommendation modal
    setShowBudgetModal(true);
  };

  return (
    <section className="my-16">
      <div className="container mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          {/* Success Message */}
          {cartSuccessMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 flex items-center shadow-md animate-fadeIn">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {cartSuccessMessage}
            </div>
          )}

          <div className="flex flex-col space-y-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b border-gray-200 pb-6">
              <div>
                <h2 className="text-3xl font-bold text-primary mb-2">{title}</h2>
                <p className="text-gray-700">Essential items for your {title.toLowerCase()} needs</p>
              </div>

              <div className="flex items-center space-x-3 mt-4 md:mt-0">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id={`select-all-${title}`}
                    checked={allSelected}
                    onChange={handleSelectAll}
                    className="h-5 w-5 rounded text-accent focus:ring-accent"
                  />
                  <label htmlFor={`select-all-${title}`} className="ml-2 text-gray-700 font-medium">Select All</label>
                </div>
                <button
                  onClick={handleAddAllToCart}
                  className="bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-secondary transition-colors flex items-center shadow-md"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Add Selected
                </button>
              </div>
            </div>

            <div className="flex justify-end">
              {/* Budget recommendation button */}
              <button
                onClick={handleBudgetRecommendation}
                className="bg-accent text-white px-5 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors whitespace-nowrap flex items-center shadow-md"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Get Budget Recommendations
              </button>
            </div>
          </div>
        </div>

        {/* Budget Recommendation Modal */}
        {showBudgetModal && (
          <BudgetRecommendation
            products={products}
            category={title}
            isModal={true}
            onClose={() => setShowBudgetModal(false)}
          />
        )}

        {filteredProducts.length === 0 ? (
          <div className="bg-white p-12 rounded-lg shadow-md text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-xl font-semibold text-primary mb-2">No products found</h3>
            <p className="text-gray-700 text-lg mb-4">We couldn't find any products matching your search criteria.</p>
            <button
              onClick={() => setSearchQuery('')}
              className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-secondary transition-colors"
            >
              Clear Search
            </button>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-4">
              <p className="text-gray-700">
                <span className="font-semibold">{filteredProducts.length}</span> products found
                {searchQuery && <span> for "<span className="italic">{searchQuery}</span>"</span>}
              </p>
              <p className="text-gray-700">
                <span className="font-semibold">{selectedProducts.length}</span> items selected
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredProducts.map(product => (
                <ProductCard
                  key={product.id}
                  id={product.id}
                  name={product.name}
                  price={product.price}
                  image={product.image}
                  category={product.category}
                  description={product.description}
                  isSelected={selectedProducts.includes(product.id)}
                  onSelectChange={(isSelected) => handleProductSelect(product.id, isSelected)}
                />
              ))}
            </div>

            {filteredProducts.length > 8 && (
              <div className="mt-8 text-center">
                <button
                  onClick={handleAddAllToCart}
                  className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-secondary transition-colors inline-flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Add Selected Items to Cart
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
}
