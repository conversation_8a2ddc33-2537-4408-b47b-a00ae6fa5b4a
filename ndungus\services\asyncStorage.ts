/**
 * Simple AsyncStorage implementation to avoid dependency issues
 * This is a temporary solution until we can properly install @react-native-async-storage/async-storage
 */

// In-memory storage
const storage: Record<string, string> = {};

// AsyncStorage mock implementation
const AsyncStorage = {
  // Get item from storage
  getItem: async (key: string): Promise<string | null> => {
    return storage[key] || null;
  },
  
  // Set item in storage
  setItem: async (key: string, value: string): Promise<void> => {
    storage[key] = value;
  },
  
  // Remove item from storage
  removeItem: async (key: string): Promise<void> => {
    delete storage[key];
  },
  
  // Clear all storage
  clear: async (): Promise<void> => {
    Object.keys(storage).forEach(key => {
      delete storage[key];
    });
  },
  
  // Get all keys
  getAllKeys: async (): Promise<string[]> => {
    return Object.keys(storage);
  },
  
  // Multi get
  multiGet: async (keys: string[]): Promise<[string, string | null][]> => {
    return keys.map(key => [key, storage[key] || null]);
  },
  
  // Multi set
  multiSet: async (keyValuePairs: [string, string][]): Promise<void> => {
    keyValuePairs.forEach(([key, value]) => {
      storage[key] = value;
    });
  },
  
  // Multi remove
  multiRemove: async (keys: string[]): Promise<void> => {
    keys.forEach(key => {
      delete storage[key];
    });
  },
};

export default AsyncStorage;
