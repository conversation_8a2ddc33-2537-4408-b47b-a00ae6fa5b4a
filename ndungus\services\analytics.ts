/**
 * Analytics service for calculating dashboard metrics and reports
 */

import { Product, calculateProfitMargin, calculateInventoryValue } from '@/models/Product';
import { Sale, SaleItem } from '@/models/Sale';
import { getSales } from '@/services/sales';
import { getProducts } from '@/services/products';

// Types for analytics data
export interface SalesTrend {
  label: string;
  amount: number;
}

export interface CategoryBreakdown {
  category: string;
  percentage: number;
  totalSales: number;
}

export interface TopProduct {
  id: string;
  name: string;
  units: number;
  profit: number;
}

export interface DashboardSummary {
  totalSales: number;
  totalRevenue: number;
  totalProfit: number;
  salesCount: number;
  averageOrderValue: number;
  mostProfitableDay: string;
  inventoryValue: number;
  lowStockCount: number;
}

/**
 * Get dashboard summary metrics
 * @param days Number of days to include (0 for all time)
 * @returns Dashboard summary metrics
 */
export async function getDashboardSummary(days: number = 0): Promise<DashboardSummary> {
  try {
    const sales = await getSales();
    const products = await getProducts();
    
    // Filter sales by date if days is specified
    const filteredSales = days > 0
      ? sales.filter(sale => {
          const saleDate = new Date(sale.createdAt);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          return saleDate >= cutoffDate && sale.status === 'completed';
        })
      : sales.filter(sale => sale.status === 'completed');
    
    // Calculate sales metrics
    const totalRevenue = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
    const totalProfit = filteredSales.reduce((sum, sale) => sum + sale.profit, 0);
    const totalItems = filteredSales.reduce((sum, sale) => 
      sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0);
    const salesCount = filteredSales.length;
    
    // Calculate average order value
    const averageOrderValue = salesCount > 0 ? totalRevenue / salesCount : 0;
    
    // Calculate most profitable day
    const profitByDay = new Map<string, number>();
    
    filteredSales.forEach(sale => {
      const day = new Date(sale.createdAt).toLocaleDateString('en-US', { weekday: 'long' });
      const currentProfit = profitByDay.get(day) || 0;
      profitByDay.set(day, currentProfit + sale.profit);
    });
    
    let mostProfitableDay = 'N/A';
    let highestProfit = 0;
    
    profitByDay.forEach((profit, day) => {
      if (profit > highestProfit) {
        highestProfit = profit;
        mostProfitableDay = day;
      }
    });
    
    // Calculate inventory metrics
    const inventoryValue = products.reduce((sum, product) => 
      sum + calculateInventoryValue(product), 0);
    
    const lowStockCount = products.filter(product => 
      product.status === 'low-stock' || product.status === 'out-of-stock').length;
    
    return {
      totalSales: totalItems,
      totalRevenue,
      totalProfit,
      salesCount,
      averageOrderValue,
      mostProfitableDay,
      inventoryValue,
      lowStockCount,
    };
  } catch (error) {
    console.error('Error getting dashboard summary:', error);
    return {
      totalSales: 0,
      totalRevenue: 0,
      totalProfit: 0,
      salesCount: 0,
      averageOrderValue: 0,
      mostProfitableDay: 'N/A',
      inventoryValue: 0,
      lowStockCount: 0,
    };
  }
}

/**
 * Get daily sales trend
 * @param days Number of days to include
 * @returns Array of daily sales data
 */
export async function getDailySalesTrend(days: number = 7): Promise<SalesTrend[]> {
  try {
    const sales = await getSales();
    const result: SalesTrend[] = [];
    
    // Create a map for each day in the range
    const dailyMap = new Map<string, number>();
    
    // Initialize with zero values for all days
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStr = date.toISOString().split('T')[0];
      dailyMap.set(dayStr, 0);
    }
    
    // Add sales data
    sales.forEach(sale => {
      if (sale.status === 'completed') {
        const saleDate = new Date(sale.createdAt);
        const dayStr = saleDate.toISOString().split('T')[0];
        
        if (dailyMap.has(dayStr)) {
          const currentAmount = dailyMap.get(dayStr) || 0;
          dailyMap.set(dayStr, currentAmount + sale.total);
        }
      }
    });
    
    // Convert map to array and sort by date
    dailyMap.forEach((amount, dayStr) => {
      const date = new Date(dayStr);
      const day = date.toLocaleDateString('en-US', { weekday: 'short' });
      result.push({ label: day, amount });
    });
    
    // Sort by date (newest last)
    return result.reverse();
  } catch (error) {
    console.error('Error getting daily sales trend:', error);
    return [];
  }
}

/**
 * Get top selling products
 * @param days Number of days to include
 * @param limit Maximum number of products to return
 * @returns Array of top products
 */
export async function getTopProducts(days: number = 30, limit: number = 5): Promise<TopProduct[]> {
  try {
    const sales = await getSales();
    const productMap = new Map<string, { id: string, name: string, units: number, profit: number }>();
    
    // Filter sales by date
    const filteredSales = days > 0
      ? sales.filter(sale => {
          const saleDate = new Date(sale.createdAt);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          return saleDate >= cutoffDate && sale.status === 'completed';
        })
      : sales.filter(sale => sale.status === 'completed');
    
    // Aggregate product sales
    filteredSales.forEach(sale => {
      sale.items.forEach(item => {
        const existing = productMap.get(item.productId);
        
        if (existing) {
          existing.units += item.quantity;
          existing.profit += (item.totalPrice - (item.costPrice * item.quantity));
        } else {
          productMap.set(item.productId, {
            id: item.productId,
            name: item.productName,
            units: item.quantity,
            profit: item.totalPrice - (item.costPrice * item.quantity),
          });
        }
      });
    });
    
    // Convert to array and sort by units sold
    const result = Array.from(productMap.values())
      .sort((a, b) => b.units - a.units)
      .slice(0, limit);
    
    return result;
  } catch (error) {
    console.error('Error getting top products:', error);
    return [];
  }
}

/**
 * Get category breakdown
 * @param days Number of days to include
 * @returns Array of category data
 */
export async function getCategoryBreakdown(days: number = 30): Promise<CategoryBreakdown[]> {
  try {
    const sales = await getSales();
    const products = await getProducts();
    
    // Create a map of product IDs to categories
    const productCategories = new Map<string, string>();
    products.forEach(product => {
      productCategories.set(product.id, product.category);
    });
    
    // Filter sales by date
    const filteredSales = days > 0
      ? sales.filter(sale => {
          const saleDate = new Date(sale.createdAt);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          return saleDate >= cutoffDate && sale.status === 'completed';
        })
      : sales.filter(sale => sale.status === 'completed');
    
    // Aggregate sales by category
    const categoryMap = new Map<string, number>();
    
    filteredSales.forEach(sale => {
      sale.items.forEach(item => {
        const category = productCategories.get(item.productId) || 'Other';
        const currentTotal = categoryMap.get(category) || 0;
        categoryMap.set(category, currentTotal + item.totalPrice);
      });
    });
    
    // Calculate total sales
    const totalSales = Array.from(categoryMap.values()).reduce((sum, value) => sum + value, 0);
    
    // Convert to array with percentages
    const result = Array.from(categoryMap.entries())
      .map(([category, sales]) => ({
        category,
        totalSales: sales,
        percentage: totalSales > 0 ? Math.round((sales / totalSales) * 100) : 0,
      }))
      .sort((a, b) => b.percentage - a.percentage);
    
    return result;
  } catch (error) {
    console.error('Error getting category breakdown:', error);
    return [];
  }
}
