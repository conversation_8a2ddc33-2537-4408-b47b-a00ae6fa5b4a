import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';
import { Case } from '@/types/case';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const limitCount = parseInt(req.query.limit as string) || 10;
    const db = getAdminDatabase();
    const casesRef = db.ref('cases');
    const snapshot = await casesRef.once('value');
    const cases: Case[] = [];

    if (snapshot.exists()) {
      const casesData = snapshot.val();

      // Convert object to array with IDs and ensure mediaUrls is an array
      for (const key in casesData) {
        const caseData = casesData[key];
        cases.push({
          id: key,
          ...caseData,
          mediaUrls: Array.isArray(caseData.mediaUrls) ? caseData.mediaUrls : [],
          followers: caseData.followers || {},
          contributions: caseData.contributions || {}
        });
      }

      // Sort by followers count (descending)
      cases.sort((a, b) => {
        const followersA = Object.keys(a.followers || {}).length;
        const followersB = Object.keys(b.followers || {}).length;
        return followersB - followersA;
      });

      // Limit to requested count
      const limitedCases = cases.slice(0, limitCount);

      return res.status(200).json(limitedCases);
    }

    return res.status(200).json([]);
  } catch (error) {
    console.error('Error fetching popular cases:', error);
    return res.status(500).json({ error: 'Failed to fetch popular cases' });
  }
}
