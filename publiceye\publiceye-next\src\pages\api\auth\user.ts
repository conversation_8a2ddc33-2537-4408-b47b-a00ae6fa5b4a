import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
getServerFirebase();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get the authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Extract the token
  const idToken = authHeader.split('Bearer ')[1];

  try {
    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const uid = decodedToken.uid;

    // Get user data from Firebase Auth
    const userRecord = await admin.auth().getUser(uid);

    // Get additional user data from Realtime Database
    const db = getAdminDatabase();
    const userRef = db.ref(`users/${uid}`);
    const snapshot = await userRef.once('value');
    const userData = snapshot.val() || {};

    // Return user data
    return res.status(200).json({
      id: uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      photoURL: userRecord.photoURL,
      role: userData.role || 'user',
      followedCases: userData.followedCases || {},
      contributedCases: userData.contributedCases || {},
    });
  } catch (error) {
    console.error('Error verifying ID token:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
}
