"use client";

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CategorySection from '@/components/CategorySection';
import { getLifeChapters, getCategoriesInLifeChapter, getProductsByCategoryInLifeChapter } from '@/data/products';

interface CategoryPageProps {
  params: {
    chapter: string;
    category: string;
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const [lifeChapter, setLifeChapter] = useState<string | null>(null);
  const [category, setCategory] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  
  useEffect(() => {
    // Convert URL slugs to proper names
    const chapterSlug = params.chapter;
    const categorySlug = params.category;
    const allChapters = getLifeChapters();
    
    // Find the chapter that matches the slug (case-insensitive)
    const chapter = allChapters.find(
      ch => ch.toLowerCase().replace(/\s+/g, '-') === chapterSlug.toLowerCase()
    );
    
    if (!chapter) {
      notFound();
      return;
    }
    
    setLifeChapter(chapter);
    
    // Get categories for this life chapter
    const chapterCategories = getCategoriesInLifeChapter(chapter);
    
    // Find the category that matches the slug (case-insensitive)
    const matchedCategory = chapterCategories.find(
      cat => cat.toLowerCase() === categorySlug.toLowerCase()
    );
    
    if (!matchedCategory) {
      notFound();
      return;
    }
    
    setCategory(matchedCategory);
    
    // Get products for this category in this life chapter
    const categoryProducts = getProductsByCategoryInLifeChapter(chapter, matchedCategory);
    setProducts(categoryProducts);
  }, [params.chapter, params.category]);
  
  if (!lifeChapter || !category) {
    return <div>Loading...</div>;
  }
  
  return (
    <>
      <Header />
      
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <nav className="flex mb-6" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-3">
                <li className="inline-flex items-center">
                  <a href="/" className="inline-flex items-center text-sm font-medium text-secondary hover:text-primary">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Home
                  </a>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                    </svg>
                    <a href="/life-chapters" className="ml-1 text-sm font-medium text-secondary hover:text-primary md:ml-2">Life Chapters</a>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                    </svg>
                    <a href={`/life-chapters/${lifeChapter.toLowerCase().replace(/\s+/g, '-')}`} className="ml-1 text-sm font-medium text-secondary hover:text-primary md:ml-2">{lifeChapter}</a>
                  </div>
                </li>
                <li aria-current="page">
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                    </svg>
                    <span className="ml-1 text-sm font-medium text-accent md:ml-2">{category}</span>
                  </div>
                </li>
              </ol>
            </nav>
            
            <h1 className="text-4xl font-bold text-primary mb-2">{category} for {lifeChapter}</h1>
            <p className="text-secondary text-lg mb-8">
              Find all the essential {category.toLowerCase()} items you need for your {lifeChapter.toLowerCase()} journey.
            </p>
          </div>
          
          <CategorySection 
            title={category}
            products={products}
          />
        </div>
      </main>
      
      <Footer />
    </>
  );
}
