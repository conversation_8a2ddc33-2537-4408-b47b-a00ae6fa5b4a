"use client";

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AccountPage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [registerForm, setRegisterForm] = useState({ 
    name: '', 
    email: '', 
    password: '', 
    confirmPassword: '' 
  });
  
  const handleLoginSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would authenticate the user
    setIsLoggedIn(true);
  };
  
  const handleRegisterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would register the user
    setIsLoggedIn(true);
  };
  
  const handleLogout = () => {
    setIsLoggedIn(false);
  };
  
  return (
    <>
      <Header />
      
      <main className="py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-primary mb-8">My Account</h1>
          
          {!isLoggedIn ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Login Form */}
              <div className="bg-white rounded-lg shadow-md p-8">
                <h2 className="text-2xl font-semibold text-primary mb-6">Login</h2>
                <form onSubmit={handleLoginSubmit}>
                  <div className="mb-4">
                    <label htmlFor="login-email" className="block text-secondary mb-2">Email Address</label>
                    <input 
                      type="email" 
                      id="login-email" 
                      value={loginForm.email}
                      onChange={(e) => setLoginForm({...loginForm, email: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="login-password" className="block text-secondary mb-2">Password</label>
                    <input 
                      type="password" 
                      id="login-password" 
                      value={loginForm.password}
                      onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <button 
                    type="submit"
                    className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-secondary transition-colors"
                  >
                    Login
                  </button>
                  <div className="mt-4 text-center">
                    <a href="#" className="text-accent hover:underline">Forgot your password?</a>
                  </div>
                </form>
              </div>
              
              {/* Register Form */}
              <div className="bg-white rounded-lg shadow-md p-8">
                <h2 className="text-2xl font-semibold text-primary mb-6">Register</h2>
                <form onSubmit={handleRegisterSubmit}>
                  <div className="mb-4">
                    <label htmlFor="register-name" className="block text-secondary mb-2">Full Name</label>
                    <input 
                      type="text" 
                      id="register-name" 
                      value={registerForm.name}
                      onChange={(e) => setRegisterForm({...registerForm, name: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="register-email" className="block text-secondary mb-2">Email Address</label>
                    <input 
                      type="email" 
                      id="register-email" 
                      value={registerForm.email}
                      onChange={(e) => setRegisterForm({...registerForm, email: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="register-password" className="block text-secondary mb-2">Password</label>
                    <input 
                      type="password" 
                      id="register-password" 
                      value={registerForm.password}
                      onChange={(e) => setRegisterForm({...registerForm, password: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="register-confirm-password" className="block text-secondary mb-2">Confirm Password</label>
                    <input 
                      type="password" 
                      id="register-confirm-password" 
                      value={registerForm.confirmPassword}
                      onChange={(e) => setRegisterForm({...registerForm, confirmPassword: e.target.value})}
                      className="w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>
                  <button 
                    type="submit"
                    className="w-full bg-accent text-white py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors"
                  >
                    Create Account
                  </button>
                </form>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="border-b">
                <div className="flex">
                  <button 
                    onClick={() => setActiveTab('profile')}
                    className={`px-6 py-4 font-medium ${activeTab === 'profile' ? 'text-primary border-b-2 border-primary' : 'text-secondary'}`}
                  >
                    Profile
                  </button>
                  <button 
                    onClick={() => setActiveTab('orders')}
                    className={`px-6 py-4 font-medium ${activeTab === 'orders' ? 'text-primary border-b-2 border-primary' : 'text-secondary'}`}
                  >
                    Orders
                  </button>
                  <button 
                    onClick={() => setActiveTab('addresses')}
                    className={`px-6 py-4 font-medium ${activeTab === 'addresses' ? 'text-primary border-b-2 border-primary' : 'text-secondary'}`}
                  >
                    Addresses
                  </button>
                  <button 
                    onClick={() => setActiveTab('settings')}
                    className={`px-6 py-4 font-medium ${activeTab === 'settings' ? 'text-primary border-b-2 border-primary' : 'text-secondary'}`}
                  >
                    Settings
                  </button>
                </div>
              </div>
              
              <div className="p-8">
                {activeTab === 'profile' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-primary mb-6">Profile Information</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-secondary mb-2">Full Name</label>
                        <input 
                          type="text" 
                          value="John Doe" 
                          className="w-full border border-gray-300 rounded px-4 py-2"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-secondary mb-2">Email Address</label>
                        <input 
                          type="email" 
                          value="<EMAIL>" 
                          className="w-full border border-gray-300 rounded px-4 py-2"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-secondary mb-2">Phone Number</label>
                        <input 
                          type="tel" 
                          placeholder="Add your phone number" 
                          className="w-full border border-gray-300 rounded px-4 py-2"
                        />
                      </div>
                      <div>
                        <label className="block text-secondary mb-2">Date of Birth</label>
                        <input 
                          type="date" 
                          className="w-full border border-gray-300 rounded px-4 py-2"
                        />
                      </div>
                    </div>
                    
                    <div className="mt-8">
                      <button className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-secondary transition-colors">
                        Update Profile
                      </button>
                    </div>
                  </div>
                )}
                
                {activeTab === 'orders' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-primary mb-6">Order History</h2>
                    <p className="text-secondary">You haven't placed any orders yet.</p>
                  </div>
                )}
                
                {activeTab === 'addresses' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-primary mb-6">Saved Addresses</h2>
                    <p className="text-secondary mb-4">You don't have any saved addresses yet.</p>
                    <button className="bg-accent text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors">
                      Add New Address
                    </button>
                  </div>
                )}
                
                {activeTab === 'settings' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-primary mb-6">Account Settings</h2>
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-medium text-primary mb-3">Change Password</h3>
                        <div className="grid grid-cols-1 gap-4">
                          <div>
                            <label className="block text-secondary mb-2">Current Password</label>
                            <input 
                              type="password" 
                              className="w-full border border-gray-300 rounded px-4 py-2"
                            />
                          </div>
                          <div>
                            <label className="block text-secondary mb-2">New Password</label>
                            <input 
                              type="password" 
                              className="w-full border border-gray-300 rounded px-4 py-2"
                            />
                          </div>
                          <div>
                            <label className="block text-secondary mb-2">Confirm New Password</label>
                            <input 
                              type="password" 
                              className="w-full border border-gray-300 rounded px-4 py-2"
                            />
                          </div>
                        </div>
                        <button className="mt-4 bg-primary text-white px-6 py-2 rounded font-medium hover:bg-secondary transition-colors">
                          Update Password
                        </button>
                      </div>
                      
                      <div className="pt-6 border-t">
                        <h3 className="text-lg font-medium text-primary mb-3">Email Preferences</h3>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <input type="checkbox" id="promo-emails" className="mr-2" />
                            <label htmlFor="promo-emails" className="text-secondary">Receive promotional emails</label>
                          </div>
                          <div className="flex items-center">
                            <input type="checkbox" id="order-updates" className="mr-2" checked />
                            <label htmlFor="order-updates" className="text-secondary">Receive order updates</label>
                          </div>
                        </div>
                        <button className="mt-4 bg-primary text-white px-6 py-2 rounded font-medium hover:bg-secondary transition-colors">
                          Save Preferences
                        </button>
                      </div>
                      
                      <div className="pt-6 border-t">
                        <h3 className="text-lg font-medium text-red-600 mb-3">Danger Zone</h3>
                        <p className="text-secondary mb-4">Once you delete your account, there is no going back. Please be certain.</p>
                        <button className="bg-red-600 text-white px-6 py-2 rounded font-medium hover:bg-red-700 transition-colors">
                          Delete Account
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className="mt-8 pt-6 border-t">
                  <button 
                    onClick={handleLogout}
                    className="text-red-600 hover:text-red-800 transition-colors"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </>
  );
}
