import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, Product } from '@/utils/database';

/**
 * GET /api/products
 * Fetch all products with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const lifeChapter = searchParams.get('lifeChapter');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    // Build query
    const query: any = {};
    
    if (lifeChapter) {
      query.lifeChapter = lifeChapter;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } },
        { subcategory: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Execute query
    const products = await Product.find(query).limit(limit);
    
    return NextResponse.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/products
 * Create a new product
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get request body
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'price', 'image', 'lifeChapter', 'category', 'description'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    // Create new product
    const product = new Product({
      id: body.id || `${body.category.toLowerCase()}-${Date.now()}`,
      name: body.name,
      price: body.price,
      image: body.image,
      lifeChapter: body.lifeChapter,
      category: body.category,
      subcategory: body.subcategory || 'General',
      description: body.description,
      stock: body.stock || 10
    });
    
    // Save to database
    await product.save();
    
    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
