import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getCategories, getProductsByCategory } from '@/data/products';

export default function CategoriesPage() {
  const categories = getCategories();
  
  return (
    <>
      <Header />
      
      <main className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h1 className="text-4xl font-bold text-primary mb-4">Shop by Category</h1>
            <p className="text-secondary text-lg max-w-3xl mx-auto">
              Browse our carefully curated collections of university essentials.
              Everything you need for your next chapter, organized by category.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {categories.map(category => {
              const products = getProductsByCategory(category);
              const productCount = products.length;
              
              return (
                <Link 
                  key={category} 
                  href={`/categories/${category.toLowerCase()}`}
                  className="group"
                >
                  <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative h-64">
                      {/* Use the first product image as category image */}
                      {products.length > 0 && (
                        <Image 
                          src={products[0].image} 
                          alt={category}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-primary/80 to-transparent flex items-end">
                        <div className="p-6 text-white">
                          <h2 className="text-2xl font-bold mb-1">{category}</h2>
                          <p className="text-light/90">{productCount} products</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <p className="text-secondary mb-4">
                        Essential {category.toLowerCase()} items for your university life
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-primary font-semibold">Browse Collection</span>
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          className="h-5 w-5 text-accent group-hover:translate-x-1 transition-transform" 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </main>
      
      <Footer />
    </>
  );
}
