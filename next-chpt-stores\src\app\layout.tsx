import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { CartProvider } from '@/context/CartContext';

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Next Chapter Stores | Your University Essentials",
  description: "The go-to store for students joining university. Find all the essentials you need for your next chapter in life.",
  keywords: "university essentials, student supplies, dorm room, college necessities, student shopping",
  metadataBase: new URL('https://nextchapterstores.com/'),
  openGraph: {
    title: 'Next Chapter Stores | Your University Essentials',
    description: 'The go-to store for students joining university. Find all the essentials you need for your next chapter in life.',
    url: 'https://nextchapterstores.com',
    siteName: 'Next Chapter Stores',
    images: [
      {
        url: '/logo.png',
        width: 800,
        height: 600,
        alt: 'Next Chapter Stores Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Next Chapter Stores | Your University Essentials',
    description: 'The go-to store for students joining university. Find all the essentials you need for your next chapter in life.',
    images: ['/logo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
};



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${montserrat.variable} font-montserrat antialiased`}>
        <CartProvider>
          {children}
        </CartProvider>
      </body>
    </html>
  );
}
