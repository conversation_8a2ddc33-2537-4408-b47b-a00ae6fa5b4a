import React, { useState } from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { FaPlus, FaUpload, FaLink, FaFileAlt, FaTasks, FaSpinner } from 'react-icons/fa';
import { CaseStage, Contribution } from '@/types/case';
import { useCases } from '@/hooks/useCases';
import { useAuth } from '@/hooks/useAuth';
import { verifyContent } from '@/lib/news-verification';
import { useToast } from '@/components/ui/Toast';

interface AddContributionProps {
  caseId: string;
  onSuccess: () => void;
}

type FormInputs = {
  stage: CaseStage;
  description: string;
  sourceUrl?: string;
};

const AddContribution: React.FC<AddContributionProps> = ({ caseId, onSuccess }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [mediaPreviewUrls, setMediaPreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [verificationResult, setVerificationResult] = useState<any>(null);
  const [showVerificationWarning, setShowVerificationWarning] = useState(false);

  const { addContribution } = useCases();
  const { user } = useAuth();
  const { showToast } = useToast();

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<FormInputs>({
    defaultValues: {
      stage: CaseStage.REPORTED,
      description: '',
      sourceUrl: '',
    }
  });

  // Watch description and source URL for verification
  const description = watch('description');
  const sourceUrl = watch('sourceUrl');

  // Handle media file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setMediaFiles([...mediaFiles, ...filesArray]);

      // Create preview URLs
      const newPreviewUrls = filesArray.map(file => URL.createObjectURL(file));
      setMediaPreviewUrls([...mediaPreviewUrls, ...newPreviewUrls]);
    }
  };

  // Remove a media file
  const removeMedia = (index: number) => {
    const newFiles = [...mediaFiles];
    newFiles.splice(index, 1);
    setMediaFiles(newFiles);

    const newPreviewUrls = [...mediaPreviewUrls];
    URL.revokeObjectURL(newPreviewUrls[index]);
    newPreviewUrls.splice(index, 1);
    setMediaPreviewUrls(newPreviewUrls);
  };

  // Verify content for fake news
  const verifyContentBeforeSubmit = async () => {
    if (description.trim().length > 50 || sourceUrl) {
      try {
        const result = await verifyContent(description, sourceUrl);
        setVerificationResult(result);

        if (!result.isReliable) {
          setShowVerificationWarning(true);
          return false;
        }
      } catch (error) {
        console.error('Error verifying content:', error);
      }
    }

    return true;
  };

  // Form submission
  const onSubmit: SubmitHandler<FormInputs> = async (data) => {
    if (!user) {
      showToast('You must be signed in to add a contribution', 'error');
      return;
    }

    setIsSubmitting(true);

    // Verify content first
    const isContentVerified = await verifyContentBeforeSubmit();

    if (!isContentVerified) {
      // Show warning toast
      showToast('This content may contain unreliable information', 'info');

      // Ask for confirmation
      if (!window.confirm('This content may contain unreliable information. Do you still want to submit it?')) {
        setIsSubmitting(false);
        return;
      }
    }

    try {
      // Prepare contribution data
      const contributionData: Omit<Contribution, 'id' | 'createdAt' | 'isVerified' | 'isFlagged' | 'flagReason'> = {
        userId: user.id,
        codeName: user.codeName || user.displayName || 'Anonymous',
        colorCode: user.colorCode || '#F44336',
        stage: data.stage,
        description: data.description,
        mediaUrls: [],
        sourceUrl: data.sourceUrl,
      };

      // Submit contribution
      const success = await addContribution(caseId, contributionData, mediaFiles);

      if (success) {
        // Clean up preview URLs
        mediaPreviewUrls.forEach(url => URL.revokeObjectURL(url));

        // Show success toast
        showToast('Update added successfully!', 'success');

        // Reset form
        reset();
        setMediaFiles([]);
        setMediaPreviewUrls([]);
        setIsOpen(false);
        setVerificationResult(null);
        setShowVerificationWarning(false);

        // Notify parent component
        onSuccess();
      } else {
        showToast('Failed to add update. Please try again.', 'error');
      }
    } catch (error: any) {
      console.error('Error adding contribution:', error);
      showToast(`Error: ${error.message || 'Failed to add update'}`, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Add Contribution Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center justify-center gap-2 bg-gradient-to-r from-red-600 to-red-800 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all"
      >
        <FaPlus />
        <span>Add Progress Update</span>
      </button>

      {/* Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-red-600 to-red-800 text-white px-6 py-4 rounded-t-2xl">
              <h2 className="text-xl font-bold text-center">Add Progress Update</h2>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stage
                  </label>
                  <select
                    {...register('stage', { required: 'Stage is required' })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value={CaseStage.REPORTED}>Reported</option>
                    <option value={CaseStage.INVESTIGATING}>Under Investigation</option>
                    <option value={CaseStage.EVIDENCE_COLLECTED}>Evidence Collected</option>
                    <option value={CaseStage.VERIFIED}>Verified</option>
                    <option value={CaseStage.RESOLVED}>Resolved</option>
                    <option value={CaseStage.CLOSED}>Closed</option>
                  </select>
                  {errors.stage && (
                    <p className="mt-1 text-sm text-red-600">{errors.stage.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    {...register('description', {
                      required: 'Description is required',
                      minLength: { value: 20, message: 'Description must be at least 20 characters' }
                    })}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Describe the progress or update..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Source URL (optional)
                  </label>
                  <input
                    type="url"
                    {...register('sourceUrl', {
                      pattern: {
                        value: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
                        message: 'Please enter a valid URL'
                      }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="https://..."
                  />
                  {errors.sourceUrl && (
                    <p className="mt-1 text-sm text-red-600">{errors.sourceUrl.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Media Files (optional)
                  </label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg">
                    <div className="space-y-1 text-center">
                      <FaUpload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="media-upload"
                          className="relative cursor-pointer bg-white rounded-md font-medium text-red-600 hover:text-red-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500"
                        >
                          <span>Upload files</span>
                          <input
                            id="media-upload"
                            type="file"
                            multiple
                            accept="image/*,video/*"
                            className="sr-only"
                            onChange={handleFileChange}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </div>
                  </div>
                  {mediaFiles.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        {mediaFiles.length} file(s) selected
                      </p>
                    </div>
                  )}
                </div>

                {/* Verification Warning */}
                {showVerificationWarning && (
                  <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 className="font-bold text-yellow-800 mb-2">Content Verification Warning</h4>
                    <p className="text-yellow-700 text-sm mb-2">
                      Our system has flagged this content as potentially unreliable:
                    </p>
                    <ul className="list-disc list-inside text-sm text-yellow-700">
                      {verificationResult?.reasons.map((reason: string, index: number) => (
                        <li key={index}>{reason}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-800 hover:from-red-700 hover:to-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Adding Contribution...
                    </>
                  ) : (
                    'Add Contribution'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AddContribution;
