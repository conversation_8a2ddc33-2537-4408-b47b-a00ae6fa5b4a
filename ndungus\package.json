{"name": "ndungus", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "clean": "expo start -c", "reset-cache": "expo start -c --clear", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.8", "cloudinary": "^2.6.1", "dotenv": "^16.5.0", "expo": "~53.0.8", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-file-system": "^18.1.9", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "^13.1.6", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.4", "expo-router": "^5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "firebase": "^11.7.1", "metro": "^0.82.3", "metro-core": "^0.82.3", "metro-resolver": "^0.82.3", "metro-runtime": "^0.82.3", "metro-source-map": "^0.82.3", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.1.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}