import React from 'react';
import Image from 'next/image';
import { FaFlag, FaSearch, FaUserLock, FaGavel, FaBalanceScale, FaCheckCircle, FaLink } from 'react-icons/fa';
import { Contribution, CaseStage, GroupedContributions } from '@/types/case';
import { format } from 'date-fns';

interface CaseTimelineProps {
  currentStage: CaseStage;
  groupedContributions: GroupedContributions;
}

const CaseTimeline: React.FC<CaseTimelineProps> = ({ currentStage, groupedContributions }) => {
  // Define the stages in order
  const stages: CaseStage[] = [
    CaseStage.REPORTED,
    CaseStage.INVESTIGATING,
    CaseStage.EVIDENCE_COLLECTED,
    CaseStage.VERIFIED,
    CaseStage.RESOLVED,
    CaseStage.CLOSED
  ];

  // Get the current stage index
  const currentStageIndex = stages.indexOf(currentStage);

  // Get stage information (icon, label, color)
  const getStageInfo = (stage: CaseStage) => {
    const stageMap: Record<CaseStage, { icon: React.ReactNode; label: string; color: string }> = {
      [CaseStage.REPORTED]: {
        icon: <FaFlag />,
        label: 'REPORTED',
        color: 'bg-red-600'
      },
      [CaseStage.INVESTIGATING]: {
        icon: <FaSearch />,
        label: 'INVESTIGATING',
        color: 'bg-orange-500'
      },
      [CaseStage.EVIDENCE_COLLECTED]: {
        icon: <FaUserLock />,
        label: 'EVIDENCE COLLECTED',
        color: 'bg-yellow-500'
      },
      [CaseStage.VERIFIED]: {
        icon: <FaGavel />,
        label: 'VERIFIED',
        color: 'bg-blue-500'
      },
      [CaseStage.RESOLVED]: {
        icon: <FaBalanceScale />,
        label: 'RESOLVED',
        color: 'bg-indigo-600'
      },
      [CaseStage.CLOSED]: {
        icon: <FaCheckCircle />,
        label: 'CLOSED',
        color: 'bg-green-600'
      }
    };

    return stageMap[stage];
  };

  // Format date for display
  const formatDate = (dateString: string | Date): string => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Unknown date';
    }
  };

  return (
    <div className="mb-12">
      <h2 className="text-2xl font-bold mb-6">Case Progress</h2>

      {/* Timeline steps */}
      <div className="relative mb-12">
        {/* Timeline line */}
        <div className="absolute top-6 left-0 right-0 h-1 bg-gray-200"></div>

        {/* Timeline steps */}
        <div className="flex justify-between relative">
          {stages.map((stage, index) => {
            const { icon, label, color } = getStageInfo(stage);
            const isCompleted = index < currentStageIndex;
            const isActive = index === currentStageIndex;

            return (
              <div key={stage} className="flex flex-col items-center relative z-10 w-1/6">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center text-white ${
                    isCompleted ? 'bg-green-600' : isActive ? color : 'bg-gray-300'
                  }`}
                >
                  {icon}
                </div>
                <div className="text-xs font-semibold mt-2 text-center">{label}</div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Contributions by stage */}
      {Object.entries(groupedContributions).map(([stage, contributions]) => {
        if (!contributions || contributions.length === 0) return null;

        const { icon, label, color } = getStageInfo(stage as CaseStage);

        return (
          <div key={stage} className="mb-8">
            <div className="flex items-center mb-4">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${color} mr-3`}>
                {icon}
              </div>
              <h3 className="text-lg font-semibold">{label}</h3>
            </div>

            <div className="space-y-4">
              {contributions.map((contribution) => (
                <div key={contribution.id} className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-start">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3"
                      style={{ backgroundColor: contribution.colorCode }}
                    >
                      {(contribution.codeName || 'A').charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{contribution.codeName}</span>
                        <span className="text-sm text-gray-500">
                          {formatDate(contribution.createdAt)}
                        </span>
                      </div>
                      <p className="text-gray-700">{contribution.description}</p>
                      
                      {/* Media Gallery */}
                      {contribution.mediaUrls && contribution.mediaUrls.length > 0 && (
                        <div className="mt-4 grid grid-cols-2 gap-2">
                          {contribution.mediaUrls.map((url, index) => (
                            <div key={index} className="relative aspect-video rounded-lg overflow-hidden">
                              <Image
                                src={url}
                                alt={`Media ${index + 1}`}
                                fill
                                className="object-cover"
                              />
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Source Link */}
                      {contribution.sourceUrl && (
                        <div className="mt-3">
                          <a
                            href={contribution.sourceUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                          >
                            <FaLink className="mr-1" />
                            Source
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default CaseTimeline;
