import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FaSearch } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import UserMenu from './UserMenu';

const Header: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <header className="bg-white bg-opacity-90 backdrop-blur-md shadow-sm sticky top-0 z-50 border-b border-red-100">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/" className="text-2xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-800">
            Public Eye
          </Link>
          <p className="ml-2 text-sm text-red-700 font-medium hidden md:block">
            Justice Remembered, Cases Tracked
          </p>
        </div>

        <nav className="hidden md:block">
          <ul className="flex space-x-6">
            <li>
              <Link
                href="/about"
                className={`font-semibold uppercase text-sm ${
                  router.pathname === '/about'
                    ? 'text-red-700 border-b-2 border-red-700'
                    : 'text-gray-700 hover:text-red-700'
                }`}
              >
                About
              </Link>
            </li>
            <li>
              <Link
                href="/"
                className={`font-semibold uppercase text-sm ${
                  router.pathname === '/'
                    ? 'text-red-700 border-b-2 border-red-700'
                    : 'text-gray-700 hover:text-red-700'
                }`}
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                href="/cases/create"
                className={`font-semibold uppercase text-sm ${
                  router.pathname === '/cases/create'
                    ? 'text-red-700 border-b-2 border-red-700'
                    : 'text-gray-700 hover:text-red-700'
                }`}
              >
                Create Case
              </Link>
            </li>
            {user && (
            <li>
              <Link
                href="/user/cases"
                className={`font-semibold uppercase text-sm ${
                  router.pathname === '/user/cases'
                    ? 'text-red-700 border-b-2 border-red-700'
                    : 'text-gray-700 hover:text-red-700'
                }`}
              >
                Your Cases
              </Link>
            </li>
            )}
          </ul>
        </nav>

        <div className="flex items-center space-x-4">
          <button
            className="text-gray-700 hover:text-red-700 focus:outline-none"
            onClick={() => router.push('/search')}
            aria-label="Search"
          >
            <FaSearch className="text-lg" />
          </button>

          {user ? (
            <UserMenu />
          ) : (
            <Link
              href="/auth/signin"
              className="bg-gradient-to-r from-red-600 to-red-800 text-white px-4 py-2 rounded-full text-sm font-semibold hover:shadow-md transition-all"
            >
              Sign In
            </Link>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
