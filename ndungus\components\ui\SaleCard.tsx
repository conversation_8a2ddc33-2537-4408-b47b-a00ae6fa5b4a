import { MaterialIcons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import { StyleSheet, Text, View, Pressable, ViewStyle } from 'react-native';

import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

type PaymentMethod = 'cash' | 'mpesa' | 'card' | 'other';

type SaleCardProps = {
  itemName: string;
  image?: string;
  sellingPrice: number;
  buyingPrice?: number;
  units: number;
  profit?: number;
  date: Date | string;
  paymentMethod: PaymentMethod;
  notes?: string;
  onPress?: () => void;
  style?: ViewStyle;
};

/**
 * A card component for displaying sales
 */
export function SaleCard({
  itemName,
  image,
  sellingPrice,
  buyingPrice,
  units,
  profit,
  date,
  paymentMethod,
  notes,
  onPress,
  style,
}: SaleCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return `KES ${amount.toFixed(2)}`;
  };
  
  // Format date
  const formatDate = (date: Date | string) => {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    return date.toLocaleDateString();
  };
  
  // Calculate profit if not provided
  const calculatedProfit = profit ?? (buyingPrice ? (sellingPrice - buyingPrice) * units : 0);
  
  // Get payment method icon
  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'cash':
        return 'payments';
      case 'mpesa':
        return 'phone-android';
      case 'card':
        return 'credit-card';
      default:
        return 'payment';
    }
  };
  
  return (
    <Pressable onPress={onPress}>
      <GlassCard style={[styles.container, style]}>
        <View style={styles.header}>
          {image ? (
            <Image
              source={{ uri: image }}
              style={styles.image}
              contentFit="cover"
              transition={200}
            />
          ) : (
            <View
              style={[
                styles.imagePlaceholder,
                {
                  backgroundColor: colorScheme === 'dark'
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'rgba(0, 0, 0, 0.05)',
                },
              ]}>
              <MaterialIcons
                name="shopping-cart"
                size={24}
                color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray500}
              />
            </View>
          )}
          
          <View style={styles.headerContent}>
            <Text
              style={[
                styles.name,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}
              numberOfLines={1}>
              {itemName}
            </Text>
            
            <View style={styles.priceContainer}>
              <Text
                style={[
                  styles.price,
                  { color: colorScheme === 'dark' ? Colors.primary : Colors.primary },
                ]}>
                {formatCurrency(sellingPrice)}
              </Text>
              
              <Text
                style={[
                  styles.units,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                × {units}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <MaterialIcons
              name="event"
              size={14}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.detailText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {formatDate(date)}
            </Text>
          </View>
          
          <View style={styles.detailItem}>
            <MaterialIcons
              name={getPaymentMethodIcon(paymentMethod)}
              size={14}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.detailText,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              {paymentMethod.charAt(0).toUpperCase() + paymentMethod.slice(1)}
            </Text>
          </View>
        </View>
        
        <View style={styles.footer}>
          <View style={styles.totalContainer}>
            <Text
              style={[
                styles.totalLabel,
                { color: colorScheme === 'dark' ? Colors.gray400 : Colors.gray600 },
              ]}>
              Total:
            </Text>
            <Text
              style={[
                styles.totalValue,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              {formatCurrency(sellingPrice * units)}
            </Text>
          </View>
          
          <View
            style={[
              styles.profitContainer,
              {
                backgroundColor: colorScheme === 'dark'
                  ? 'rgba(16, 185, 129, 0.2)'
                  : 'rgba(16, 185, 129, 0.1)',
              },
            ]}>
            <MaterialIcons name="trending-up" size={14} color={Colors.success} />
            <Text style={[styles.profitText, { color: Colors.success }]}>
              {formatCurrency(calculatedProfit)}
            </Text>
          </View>
        </View>
        
        {notes && (
          <View style={styles.notesContainer}>
            <MaterialIcons
              name="note"
              size={14}
              color={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
            />
            <Text
              style={[
                styles.notes,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}
              numberOfLines={2}>
              {notes}
            </Text>
          </View>
        )}
      </GlassCard>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.xs,
  },
  header: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.semiBold,
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  price: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  units: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginTop: Spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  totalLabel: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.regular,
  },
  totalValue: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.bold,
  },
  profitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  profitText: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.semiBold,
  },
  notesContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 4,
    marginTop: Spacing.sm,
    padding: Spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: BorderRadius.sm,
  },
  notes: {
    flex: 1,
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
  },
});
