/**
 * Sales service for managing sales transactions
 */

import { v4 as uuidv4 } from 'uuid';

import {
    calculateProfit,
    calculateSubtotal,
    calculateTotal,
    CreateSaleInput,
    createSaleItem,
    Sale,
    SaleItem
} from '@/models/Sale';
import { getProductById, updateProduct } from '@/services/products';
import { getObject, setObject, StorageKey } from '@/services/storage';

/**
 * Get all sales from storage
 * @returns Array of sales
 */
export async function getSales(): Promise<Sale[]> {
  try {
    // Try to get sales from Firestore first
    try {
      const { getFirestoreSales } = await import('./firestoreSales');
      const firestoreSales = await getFirestoreSales();

      if (firestoreSales && firestoreSales.length > 0) {
        // Also update local storage for offline access
        await setObject(StorageKey.SALES, firestoreSales);
        return firestoreSales;
      }
    } catch (firestoreError) {
      console.error('Error getting sales from Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Get sales from local storage
    const sales = await getObject<Sale[]>(StorageKey.SALES);

    // If no sales found, initialize with empty array
    if (!sales) {
      await setObject(StorageKey.SALES, []);
      return [];
    }

    return sales;
  } catch (error) {
    console.error('Error getting sales:', error);
    return [];
  }
}

/**
 * Get a sale by ID
 * @param id Sale ID
 * @returns Sale or null if not found
 */
export async function getSaleById(id: string): Promise<Sale | null> {
  try {
    // Try to get sale from Firestore first
    try {
      const { getFirestoreSaleById } = await import('./firestoreSales');
      const firestoreSale = await getFirestoreSaleById(id);

      if (firestoreSale) {
        return firestoreSale;
      }
    } catch (firestoreError) {
      console.error('Error getting sale by ID from Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Get sale from local storage
    const sales = await getSales();
    return sales.find(sale => sale.id === id) || null;
  } catch (error) {
    console.error('Error getting sale by ID:', error);
    return null;
  }
}

/**
 * Create a new sale
 * @param input Sale data
 * @returns Created sale
 */
export async function createSale(input: CreateSaleInput): Promise<Sale> {
  try {
    // Try to create sale in Firestore first
    try {
      const { createFirestoreSale } = await import('./firestoreSales');
      const firestoreSale = await createFirestoreSale(input);

      if (firestoreSale) {
        return firestoreSale;
      }
    } catch (firestoreError) {
      console.error('Error creating sale in Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Create sale in local storage
    // Get products for each item
    const saleItems: SaleItem[] = [];

    for (const item of input.items) {
      const product = await getProductById(item.productId);

      if (!product) {
        throw new Error(`Product not found: ${item.productId}`);
      }

      // Check if enough stock is available
      if (product.units < item.quantity) {
        throw new Error(`Not enough stock for ${product.name}. Available: ${product.units}`);
      }

      // Create sale item
      const saleItem = createSaleItem(product, item.quantity);
      saleItems.push(saleItem);

      // Update product stock
      await updateProduct({
        id: product.id,
        units: product.units - item.quantity,
      });
    }

    // Calculate totals
    const subtotal = calculateSubtotal(saleItems);
    const discount = input.discount || 0;
    const tax = input.tax || 0;
    const total = calculateTotal(subtotal, discount, tax);
    const profit = calculateProfit(saleItems);

    // Create new sale
    const newSale: Sale = {
      id: uuidv4(),
      items: saleItems,
      subtotal,
      discount,
      tax,
      total,
      profit,
      paymentMethod: input.paymentMethod,
      customerName: input.customerName,
      customerPhone: input.customerPhone,
      notes: input.notes,
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Get existing sales
    const sales = await getSales();

    // Add to sales array
    const updatedSales = [newSale, ...sales];

    // Save to storage
    await setObject(StorageKey.SALES, updatedSales);

    // Add to sync queue for later sync with Firestore
    try {
      const { addToSyncQueue } = await import('./sync');
      await addToSyncQueue('create', 'sales', newSale);
    } catch (syncError) {
      console.error('Error adding create operation to sync queue:', syncError);
    }

    return newSale;
  } catch (error) {
    console.error('Error creating sale:', error);
    throw error;
  }
}

/**
 * Cancel a sale and restore inventory
 * @param id Sale ID
 * @returns True if cancelled, false otherwise
 */
export async function cancelSale(id: string): Promise<boolean> {
  try {
    // Try to cancel sale in Firestore first
    try {
      const { cancelFirestoreSale } = await import('./firestoreSales');
      const success = await cancelFirestoreSale(id);

      if (success) {
        return true;
      }
    } catch (firestoreError) {
      console.error('Error cancelling sale in Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Cancel sale in local storage
    const sales = await getSales();
    const saleIndex = sales.findIndex(sale => sale.id === id);

    if (saleIndex === -1) {
      return false;
    }

    const sale = sales[saleIndex];

    // Only allow cancelling if the sale is not already cancelled
    if (sale.status === 'cancelled') {
      return false;
    }

    // Restore inventory
    for (const item of sale.items) {
      const product = await getProductById(item.productId);

      if (product) {
        await updateProduct({
          id: product.id,
          units: product.units + item.quantity,
        });
      }
    }

    // Update sale status
    const updatedSale: Sale = {
      ...sale,
      status: 'cancelled',
      updatedAt: new Date().toISOString(),
    };

    // Update sales array
    const updatedSales = [...sales];
    updatedSales[saleIndex] = updatedSale;

    // Save to storage
    await setObject(StorageKey.SALES, updatedSales);

    // Add to sync queue for later sync with Firestore
    try {
      const { addToSyncQueue } = await import('./sync');
      await addToSyncQueue('update', 'sales', updatedSale);
    } catch (syncError) {
      console.error('Error adding update operation to sync queue:', syncError);
    }

    return true;
  } catch (error) {
    console.error('Error cancelling sale:', error);
    return false;
  }
}

/**
 * Get sales statistics
 * @param days Number of days to include (0 for all time)
 * @returns Sales statistics
 */
export async function getSalesStats(days: number = 0): Promise<{
  totalSales: number;
  totalRevenue: number;
  totalProfit: number;
  salesCount: number;
}> {
  try {
    // Get sales stats from local storage
    const sales = await getSales();

    // Filter by date if days is specified
    const filteredSales = days > 0
      ? sales.filter(sale => {
          const saleDate = new Date(sale.createdAt);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          return saleDate >= cutoffDate && sale.status === 'completed';
        })
      : sales.filter(sale => sale.status === 'completed');

    // Calculate statistics
    const totalRevenue = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
    const totalProfit = filteredSales.reduce((sum, sale) => sum + sale.profit, 0);
    const totalItems = filteredSales.reduce((sum, sale) =>
      sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0);

    return {
      totalSales: totalItems,
      totalRevenue,
      totalProfit,
      salesCount: filteredSales.length,
    };
  } catch (error) {
    console.error('Error getting sales stats:', error);
    return {
      totalSales: 0,
      totalRevenue: 0,
      totalProfit: 0,
      salesCount: 0,
    };
  }
}
