import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract the token
    const idToken = authHeader.split('Bearer ')[1];

    // Verify the ID token
    const decodedToken = await getServerFirebase().auth().verifyIdToken(idToken);
    const uid = decodedToken.uid;

    const db = getAdminDatabase();
    const casesRef = db.ref('cases');
    const snapshot = await casesRef.orderByChild('createdBy').equalTo(uid).once('value');
    
    const cases = [];
    if (snapshot.exists()) {
      const casesData = snapshot.val();
      for (const [id, data] of Object.entries(casesData)) {
        cases.push({
          id,
          ...data
        });
      }
    }

    return res.status(200).json(cases);
  } catch (error) {
    console.error('Error fetching user cases:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 