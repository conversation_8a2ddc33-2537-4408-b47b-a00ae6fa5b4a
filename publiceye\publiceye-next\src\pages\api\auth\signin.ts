import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
getServerFirebase();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // This endpoint is just for custom token generation
  // Actual authentication happens on the client side with Firebase Auth
  try {
    const { uid } = req.body;

    if (!uid) {
      return res.status(400).json({ error: 'Missing user ID' });
    }

    // Verify the user exists
    try {
      await admin.auth().getUser(uid);
    } catch (error) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create a custom token for the user
    const customToken = await admin.auth().createCustomToken(uid);

    // Return the custom token
    return res.status(200).json({
      success: true,
      token: customToken,
    });
  } catch (error) {
    console.error('Error creating custom token:', error);
    return res.status(500).json({ error: 'Failed to create custom token' });
  }
}
