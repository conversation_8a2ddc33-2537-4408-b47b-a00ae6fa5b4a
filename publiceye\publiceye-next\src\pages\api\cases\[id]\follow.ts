import { NextApiRequest, NextApiResponse } from 'next';
import { getServerFirebase, getAdminDatabase } from '@/lib/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;
  const { userId, action } = req.body;

  if (!id || !userId || !action) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const db = getAdminDatabase();
    const caseRef = db.ref(`cases/${id}`);
    const userRef = db.ref(`users/${userId}`);

    // Get current case data
    const caseSnapshot = await caseRef.once('value');
    const caseData = caseSnapshot.val();

    if (!caseData) {
      return res.status(404).json({ error: 'Case not found' });
    }

    // Get current user data
    const userSnapshot = await userRef.once('value');
    const userData = userSnapshot.val();

    if (!userData) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Initialize followers and followedCases if they don't exist
    const followers = caseData.followers || {};
    const followedCases = userData.followedCases || {};

    if (action === 'follow') {
      // Add user to case followers
      followers[userId] = true;
      // Add case to user's followed cases
      followedCases[id as string] = true;
    } else if (action === 'unfollow') {
      // Remove user from case followers
      delete followers[userId];
      // Remove case from user's followed cases
      delete followedCases[id as string];
    } else {
      return res.status(400).json({ error: 'Invalid action' });
    }

    // Update both case and user data
    await Promise.all([
      caseRef.update({ followers }),
      userRef.update({ followedCases })
    ]);

    return res.status(200).json({
      success: true,
      followersCount: Object.keys(followers).length,
      isFollowing: action === 'follow'
    });
  } catch (error) {
    console.error('Error updating follow status:', error);
    return res.status(500).json({ error: 'Failed to update follow status' });
  }
} 