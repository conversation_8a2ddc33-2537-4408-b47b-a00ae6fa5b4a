/**
 * Firestore Sales service for managing sales transactions
 */

import { v4 as uuidv4 } from 'uuid';

import {
  calculateProfit,
  calculateSubtotal,
  calculateTotal,
  CreateSaleInput,
  createSaleItem,
  Sale,
  SaleItem
} from '@/models/Sale';
import { COLLECTIONS, addDocument, getDocumentById, getDocuments, updateDocument } from '@/services/firebase';
import { getFirestoreProductById, updateFirestoreProduct } from '@/services/firestoreProducts';
import { getProductById, updateProduct } from '@/services/products';
import { getObject, setObject, StorageKey } from '@/services/storage';

/**
 * Get all sales from Firestore
 * @returns Array of sales
 */
export async function getFirestoreSales(): Promise<Sale[]> {
  try {
    // Get sales from Firestore
    const sales = await getDocuments<Sale>(COLLECTIONS.SALES);
    
    // Also save to local storage for offline access
    if (sales.length > 0) {
      await setObject(StorageKey.SALES, sales);
    }
    
    return sales;
  } catch (error) {
    console.error('Error getting sales from Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return getLocalSales();
  }
}

/**
 * Get all sales from local storage
 * @returns Array of sales
 */
export async function getLocalSales(): Promise<Sale[]> {
  try {
    // Get sales from storage
    const sales = await getObject<Sale[]>(StorageKey.SALES);
    
    // If no sales found, initialize with empty array
    if (!sales) {
      await setObject(StorageKey.SALES, []);
      return [];
    }
    
    return sales;
  } catch (error) {
    console.error('Error getting sales from local storage:', error);
    return [];
  }
}

/**
 * Get a sale by ID from Firestore
 * @param id Sale ID
 * @returns Sale or null if not found
 */
export async function getFirestoreSaleById(id: string): Promise<Sale | null> {
  try {
    // Get sale from Firestore
    const sale = await getDocumentById<Sale>(COLLECTIONS.SALES, id);
    return sale;
  } catch (error) {
    console.error('Error getting sale by ID from Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return getLocalSaleById(id);
  }
}

/**
 * Get a sale by ID from local storage
 * @param id Sale ID
 * @returns Sale or null if not found
 */
export async function getLocalSaleById(id: string): Promise<Sale | null> {
  try {
    const sales = await getLocalSales();
    return sales.find(sale => sale.id === id) || null;
  } catch (error) {
    console.error('Error getting sale by ID from local storage:', error);
    return null;
  }
}

/**
 * Create a new sale in Firestore
 * @param input Sale data
 * @returns Created sale
 */
export async function createFirestoreSale(input: CreateSaleInput): Promise<Sale> {
  try {
    // Get products for each item
    const saleItems: SaleItem[] = [];
    
    for (const item of input.items) {
      // Try to get product from Firestore first
      let product = await getFirestoreProductById(item.productId);
      
      // If not found in Firestore, try local storage
      if (!product) {
        product = await getProductById(item.productId);
      }
      
      if (!product) {
        throw new Error(`Product not found: ${item.productId}`);
      }
      
      // Check if enough stock is available
      if (product.units < item.quantity) {
        throw new Error(`Not enough stock for ${product.name}. Available: ${product.units}`);
      }
      
      // Create sale item
      const saleItem = createSaleItem(product, item.quantity);
      saleItems.push(saleItem);
      
      // Update product stock in Firestore
      await updateFirestoreProduct({
        id: product.id,
        units: product.units - item.quantity,
      });
    }
    
    // Calculate totals
    const subtotal = calculateSubtotal(saleItems);
    const discount = input.discount || 0;
    const tax = input.tax || 0;
    const total = calculateTotal(subtotal, discount, tax);
    const profit = calculateProfit(saleItems);
    
    // Create new sale
    const newSale: Sale = {
      id: uuidv4(),
      items: saleItems,
      subtotal,
      discount,
      tax,
      total,
      profit,
      paymentMethod: input.paymentMethod,
      customerName: input.customerName,
      customerPhone: input.customerPhone,
      notes: input.notes,
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to Firestore
    await addDocument<Sale>(COLLECTIONS.SALES, newSale);
    
    // Also save to local storage for offline access
    await createLocalSale(newSale);
    
    return newSale;
  } catch (error) {
    console.error('Error creating sale in Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return createLocalSale(input);
  }
}

/**
 * Create a new sale in local storage
 * @param input Sale data or complete sale object
 * @returns Created sale
 */
export async function createLocalSale(
  input: CreateSaleInput | Sale
): Promise<Sale> {
  try {
    // If input is already a complete Sale object, use it
    if ('id' in input && 'items' in input && 'subtotal' in input) {
      const newSale = input as Sale;
      
      // Get existing sales
      const sales = await getLocalSales();
      
      // Add to sales array
      const updatedSales = [newSale, ...sales];
      
      // Save to storage
      await setObject(StorageKey.SALES, updatedSales);
      
      return newSale;
    }
    
    // Otherwise, create a new sale from input
    const createInput = input as CreateSaleInput;
    
    // Get products for each item
    const saleItems: SaleItem[] = [];
    
    for (const item of createInput.items) {
      const product = await getProductById(item.productId);
      
      if (!product) {
        throw new Error(`Product not found: ${item.productId}`);
      }
      
      // Check if enough stock is available
      if (product.units < item.quantity) {
        throw new Error(`Not enough stock for ${product.name}. Available: ${product.units}`);
      }
      
      // Create sale item
      const saleItem = createSaleItem(product, item.quantity);
      saleItems.push(saleItem);
      
      // Update product stock
      await updateProduct({
        id: product.id,
        units: product.units - item.quantity,
      });
    }
    
    // Calculate totals
    const subtotal = calculateSubtotal(saleItems);
    const discount = createInput.discount || 0;
    const tax = createInput.tax || 0;
    const total = calculateTotal(subtotal, discount, tax);
    const profit = calculateProfit(saleItems);
    
    // Create new sale
    const newSale: Sale = {
      id: uuidv4(),
      items: saleItems,
      subtotal,
      discount,
      tax,
      total,
      profit,
      paymentMethod: createInput.paymentMethod,
      customerName: createInput.customerName,
      customerPhone: createInput.customerPhone,
      notes: createInput.notes,
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Get existing sales
    const sales = await getLocalSales();
    
    // Add to sales array
    const updatedSales = [newSale, ...sales];
    
    // Save to storage
    await setObject(StorageKey.SALES, updatedSales);
    
    return newSale;
  } catch (error) {
    console.error('Error creating sale in local storage:', error);
    throw error;
  }
}

/**
 * Cancel a sale and restore inventory in Firestore
 * @param id Sale ID
 * @returns True if cancelled, false otherwise
 */
export async function cancelFirestoreSale(id: string): Promise<boolean> {
  try {
    // Get sale from Firestore
    const sale = await getFirestoreSaleById(id);
    
    if (!sale) {
      return false;
    }
    
    // Only allow cancelling if the sale is not already cancelled
    if (sale.status === 'cancelled') {
      return false;
    }
    
    // Restore inventory
    for (const item of sale.items) {
      // Try to get product from Firestore first
      let product = await getFirestoreProductById(item.productId);
      
      if (product) {
        // Update product stock in Firestore
        await updateFirestoreProduct({
          id: product.id,
          units: product.units + item.quantity,
        });
      } else {
        // If not found in Firestore, try local storage
        product = await getProductById(item.productId);
        
        if (product) {
          await updateProduct({
            id: product.id,
            units: product.units + item.quantity,
          });
        }
      }
    }
    
    // Update sale status
    const updatedSale: Sale = {
      ...sale,
      status: 'cancelled',
      updatedAt: new Date().toISOString(),
    };
    
    // Update in Firestore
    await updateDocument<Sale>(COLLECTIONS.SALES, id, updatedSale);
    
    // Also update in local storage for offline access
    await cancelLocalSale(id);
    
    return true;
  } catch (error) {
    console.error('Error cancelling sale in Firestore:', error);
    
    // Fallback to local storage if Firestore fails
    return cancelLocalSale(id);
  }
}

/**
 * Cancel a sale and restore inventory in local storage
 * @param id Sale ID
 * @returns True if cancelled, false otherwise
 */
export async function cancelLocalSale(id: string): Promise<boolean> {
  try {
    const sales = await getLocalSales();
    const saleIndex = sales.findIndex(sale => sale.id === id);
    
    if (saleIndex === -1) {
      return false;
    }
    
    const sale = sales[saleIndex];
    
    // Only allow cancelling if the sale is not already cancelled
    if (sale.status === 'cancelled') {
      return false;
    }
    
    // Restore inventory
    for (const item of sale.items) {
      const product = await getProductById(item.productId);
      
      if (product) {
        await updateProduct({
          id: product.id,
          units: product.units + item.quantity,
        });
      }
    }
    
    // Update sale status
    const updatedSale: Sale = {
      ...sale,
      status: 'cancelled',
      updatedAt: new Date().toISOString(),
    };
    
    // Update sales array
    const updatedSales = [...sales];
    updatedSales[saleIndex] = updatedSale;
    
    // Save to storage
    await setObject(StorageKey.SALES, updatedSales);
    
    return true;
  } catch (error) {
    console.error('Error cancelling sale in local storage:', error);
    return false;
  }
}
