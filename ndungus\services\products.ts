/**
 * Product service for managing inventory items
 */

import { v4 as uuidv4 } from 'uuid';

import { calculateProductStatus, CreateProductInput, Product, UpdateProductInput } from '@/models/Product';
import { getObject, setObject, StorageKey } from '@/services/storage';

// Empty initial products array
const initialProducts: Product[] = [];

/**
 * Get all products from storage
 * @returns Array of products
 */
export async function getProducts(): Promise<Product[]> {
  try {
    // Try to get products from Firestore first
    try {
      const { getFirestoreProducts } = await import('./firestoreProducts');
      const firestoreProducts = await getFirestoreProducts();

      if (firestoreProducts && firestoreProducts.length > 0) {
        // Also update local storage for offline access
        await setObject(StorageKey.INVENTORY, firestoreProducts);
        return firestoreProducts;
      }
    } catch (firestoreError) {
      console.error('Error getting products from Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Get products from local storage
    const products = await getObject<Product[]>(StorageKey.INVENTORY);

    // If no products found, initialize with empty array
    if (!products) {
      await setObject(StorageKey.INVENTORY, initialProducts);
      return initialProducts;
    }

    return products;
  } catch (error) {
    console.error('Error getting products:', error);
    return [];
  }
}

/**
 * Get a product by ID
 * @param id Product ID
 * @returns Product or null if not found
 */
export async function getProductById(id: string): Promise<Product | null> {
  try {
    const products = await getProducts();
    return products.find(product => product.id === id) || null;
  } catch (error) {
    console.error('Error getting product by ID:', error);
    return null;
  }
}

/**
 * Create a new product
 * @param input Product data
 * @param imageUri Optional image URI to upload
 * @returns Created product
 */
export async function createProduct(input: CreateProductInput, imageUri?: string): Promise<Product> {
  try {
    // Try to create product in Firestore first
    try {
      const { createFirestoreProduct } = await import('./firestoreProducts');
      const firestoreProduct = await createFirestoreProduct(input, imageUri);

      if (firestoreProduct) {
        return firestoreProduct;
      }
    } catch (firestoreError) {
      console.error('Error creating product in Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Create product in local storage
    const products = await getProducts();

    // Create new product
    const newProduct: Product = {
      id: uuidv4(),
      ...input,
      status: calculateProductStatus(input.units),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to products array
    const updatedProducts = [...products, newProduct];

    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);

    return newProduct;
  } catch (error) {
    console.error('Error creating product:', error);
    throw new Error('Failed to create product');
  }
}

/**
 * Update an existing product
 * @param input Product data to update
 * @param imageUri Optional image URI to upload
 * @returns Updated product
 */
export async function updateProduct(input: UpdateProductInput, imageUri?: string): Promise<Product> {
  try {
    // Try to update product in Firestore first
    try {
      const { updateFirestoreProduct } = await import('./firestoreProducts');
      const firestoreProduct = await updateFirestoreProduct(input, imageUri);

      if (firestoreProduct) {
        return firestoreProduct;
      }
    } catch (firestoreError) {
      console.error('Error updating product in Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Update product in local storage
    const products = await getProducts();

    // Find product index
    const productIndex = products.findIndex(p => p.id === input.id);

    if (productIndex === -1) {
      throw new Error('Product not found');
    }

    // Get existing product
    const existingProduct = products[productIndex];

    // Create updated product
    const updatedProduct: Product = {
      ...existingProduct,
      ...input,
      status: input.units !== undefined
        ? calculateProductStatus(input.units)
        : existingProduct.status,
      updatedAt: new Date().toISOString(),
    };

    // Update products array
    const updatedProducts = [...products];
    updatedProducts[productIndex] = updatedProduct;

    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);

    return updatedProduct;
  } catch (error) {
    console.error('Error updating product:', error);
    throw new Error('Failed to update product');
  }
}

/**
 * Delete a product
 * @param id Product ID
 * @returns True if deleted, false otherwise
 */
export async function deleteProduct(id: string): Promise<boolean> {
  try {
    // Try to delete product from Firestore first
    try {
      const { deleteFirestoreProduct } = await import('./firestoreProducts');
      const success = await deleteFirestoreProduct(id);

      if (success) {
        // Also delete from local storage
        const products = await getProducts();
        const updatedProducts = products.filter(product => product.id !== id);
        await setObject(StorageKey.INVENTORY, updatedProducts);
        return true;
      }
    } catch (firestoreError) {
      console.error('Error deleting product from Firestore:', firestoreError);
      // Fall back to local storage
    }

    // Delete product from local storage
    const products = await getProducts();

    // Filter out the product to delete
    const updatedProducts = products.filter(product => product.id !== id);

    // If no products were removed, product wasn't found
    if (updatedProducts.length === products.length) {
      return false;
    }

    // Save to storage
    await setObject(StorageKey.INVENTORY, updatedProducts);

    // Add to sync queue for later sync with Firestore
    try {
      const { addToSyncQueue } = await import('./sync');
      await addToSyncQueue('delete', 'products', { id });
    } catch (syncError) {
      console.error('Error adding delete operation to sync queue:', syncError);
    }

    return true;
  } catch (error) {
    console.error('Error deleting product:', error);
    return false;
  }
}

/**
 * Get unique categories from products
 * @returns Array of unique categories
 */
export async function getCategories(): Promise<string[]> {
  try {
    const products = await getProducts();
    return Array.from(new Set(products.map(product => product.category)));
  } catch (error) {
    console.error('Error getting categories:', error);
    return [];
  }
}
