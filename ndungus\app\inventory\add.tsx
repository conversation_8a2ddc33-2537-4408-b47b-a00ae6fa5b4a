import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

import { Button } from '@/components/ui/Button';
import { GlassCard } from '@/components/ui/GlassCard';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { createProduct } from '@/services/products';

export default function AddProductScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isLoading, setIsLoading] = useState(false);
  
  // Form state
  const [name, setName] = useState('');
  const [code, setCode] = useState('');
  const [category, setCategory] = useState('');
  const [buyingPrice, setBuyingPrice] = useState('');
  const [sellingPrice, setSellingPrice] = useState('');
  const [units, setUnits] = useState('');
  const [description, setDescription] = useState('');
  const [image, setImage] = useState('');
  
  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!name) newErrors.name = 'Product name is required';
    if (!code) newErrors.code = 'Product code is required';
    if (!category) newErrors.category = 'Category is required';
    if (!buyingPrice) newErrors.buyingPrice = 'Buying price is required';
    if (!sellingPrice) newErrors.sellingPrice = 'Selling price is required';
    if (!units) newErrors.units = 'Units is required';
    
    // Validate numeric fields
    if (buyingPrice && isNaN(Number(buyingPrice))) {
      newErrors.buyingPrice = 'Buying price must be a number';
    }
    
    if (sellingPrice && isNaN(Number(sellingPrice))) {
      newErrors.sellingPrice = 'Selling price must be a number';
    }
    
    if (units && isNaN(Number(units))) {
      newErrors.units = 'Units must be a number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    try {
      // Create product
      await createProduct({
        name,
        code,
        category,
        buyingPrice: Number(buyingPrice),
        sellingPrice: Number(sellingPrice),
        units: Number(units),
        description,
        image,
      });
      
      // Show success message
      Alert.alert(
        'Success',
        'Product added successfully',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to add product');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <GlassCard style={styles.header}>
          <View style={styles.headerContent}>
            <Pressable onPress={() => router.back()} style={styles.backButton}>
              <MaterialIcons
                name="arrow-back"
                size={24}
                color={colorScheme === 'dark' ? Colors.white : Colors.black}
              />
            </Pressable>
            <Text
              style={[
                styles.title,
                { color: colorScheme === 'dark' ? Colors.white : Colors.black },
              ]}>
              Add New Product
            </Text>
          </View>
        </GlassCard>
        
        {/* Form */}
        <GlassCard style={styles.formCard}>
          {/* Product Name */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Product Name*
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
                errors.name ? styles.inputError : null,
              ]}
              placeholder="Enter product name"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={name}
              onChangeText={setName}
            />
            {errors.name ? <Text style={styles.errorText}>{errors.name}</Text> : null}
          </View>
          
          {/* Product Code */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Product Code*
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
                errors.code ? styles.inputError : null,
              ]}
              placeholder="Enter product code (e.g., ELEC-001)"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={code}
              onChangeText={setCode}
            />
            {errors.code ? <Text style={styles.errorText}>{errors.code}</Text> : null}
          </View>
          
          {/* Category */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Category*
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
                errors.category ? styles.inputError : null,
              ]}
              placeholder="Enter category (e.g., Electronics)"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={category}
              onChangeText={setCategory}
            />
            {errors.category ? <Text style={styles.errorText}>{errors.category}</Text> : null}
          </View>
          
          {/* Price Row */}
          <View style={styles.row}>
            {/* Buying Price */}
            <View style={[styles.formGroup, { flex: 1 }]}>
              <Text
                style={[
                  styles.label,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Buying Price*
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { 
                    color: colorScheme === 'dark' ? Colors.white : Colors.black,
                    backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                  },
                  errors.buyingPrice ? styles.inputError : null,
                ]}
                placeholder="0.00"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                keyboardType="numeric"
                value={buyingPrice}
                onChangeText={setBuyingPrice}
              />
              {errors.buyingPrice ? <Text style={styles.errorText}>{errors.buyingPrice}</Text> : null}
            </View>
            
            {/* Selling Price */}
            <View style={[styles.formGroup, { flex: 1, marginLeft: Spacing.md }]}>
              <Text
                style={[
                  styles.label,
                  { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
                ]}>
                Selling Price*
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { 
                    color: colorScheme === 'dark' ? Colors.white : Colors.black,
                    backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                  },
                  errors.sellingPrice ? styles.inputError : null,
                ]}
                placeholder="0.00"
                placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
                keyboardType="numeric"
                value={sellingPrice}
                onChangeText={setSellingPrice}
              />
              {errors.sellingPrice ? <Text style={styles.errorText}>{errors.sellingPrice}</Text> : null}
            </View>
          </View>
          
          {/* Units */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Units in Stock*
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
                errors.units ? styles.inputError : null,
              ]}
              placeholder="0"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              keyboardType="numeric"
              value={units}
              onChangeText={setUnits}
            />
            {errors.units ? <Text style={styles.errorText}>{errors.units}</Text> : null}
          </View>
          
          {/* Image URL */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Image URL (Optional)
            </Text>
            <TextInput
              style={[
                styles.input,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter image URL"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              value={image}
              onChangeText={setImage}
            />
          </View>
          
          {/* Description */}
          <View style={styles.formGroup}>
            <Text
              style={[
                styles.label,
                { color: colorScheme === 'dark' ? Colors.gray300 : Colors.gray700 },
              ]}>
              Description (Optional)
            </Text>
            <TextInput
              style={[
                styles.textArea,
                { 
                  color: colorScheme === 'dark' ? Colors.white : Colors.black,
                  backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                },
              ]}
              placeholder="Enter product description"
              placeholderTextColor={colorScheme === 'dark' ? Colors.gray400 : Colors.gray600}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              value={description}
              onChangeText={setDescription}
            />
          </View>
          
          {/* Submit Button */}
          <Button
            title="Add Product"
            variant="primary"
            isLoading={isLoading}
            onPress={handleSubmit}
            style={styles.submitButton}
          />
        </GlassCard>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: Spacing.sm,
  },
  title: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.bold,
  },
  formCard: {
    padding: Spacing.md,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.medium,
    marginBottom: Spacing.xs,
  },
  input: {
    height: 48,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  inputError: {
    borderColor: Colors.error,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.xs,
    fontFamily: Typography.fontFamily.regular,
    marginTop: Spacing.xs,
  },
  textArea: {
    minHeight: 100,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.regular,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  row: {
    flexDirection: 'row',
  },
  submitButton: {
    marginTop: Spacing.md,
  },
});
